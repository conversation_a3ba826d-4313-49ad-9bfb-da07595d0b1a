// Management Services JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel JavaScript loaded');

    // Initialize management services
    initializeManagementServices();

    // Initialize existing admin functionality
    initializeAdminTabs();

    // Initialize management tab functionality
    console.log('Initializing management tab...');

    // Initialize Other service functionality
    initializeOtherService();
    initializeManagementTab();
});

// Notification function - Exactly like crowd_detection
function showNotification(message, type = 'success') {
    // Ensure message is a string
    let displayMessage = message;
    if (typeof message === 'object') {
        displayMessage = JSON.stringify(message);
    } else if (message === null || message === undefined) {
        displayMessage = 'Unknown error occurred';
    } else {
        displayMessage = String(message);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' :
                          type === 'error' ? 'fa-exclamation-circle' :
                          type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
            <span>${displayMessage}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function initializeManagementServices() {
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        card.addEventListener('click', function() {
            const service = this.getAttribute('data-service');
            openManagementModal(service);
        });
    });
}

function openManagementModal(service) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('managementModal');
    if (!modal) {
        modal = createManagementModal();
        document.body.appendChild(modal);
    }
    
    // Update modal content based on service
    updateModalContent(service);
    
    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function createManagementModal() {
    const modal = document.createElement('div');
    modal.id = 'managementModal';
    modal.className = 'management-modal';
    
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h2 id="modalTitle">
                    <i id="modalIcon" class="fas fa-cog"></i>
                    <span id="modalTitleText">Management</span>
                </h2>
                <button class="management-modal-close" onclick="closeManagementModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body" id="modalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeManagementModal();
        }
    });
    
    return modal;
}

function updateModalContent(service) {
    const modalIcon = document.getElementById('modalIcon');
    const modalTitleText = document.getElementById('modalTitleText');
    const modalBody = document.getElementById('modalBody');
    
    const serviceConfig = {
        'camera-management': {
            icon: 'fas fa-video',
            title: 'Camera Management',
            content: getCameraManagementContent()
        },
        'alert-management': {
            icon: 'fas fa-bell',
            title: 'Alert Management',
            content: getAlertManagementContent()
        },
        'email-management': {
            icon: 'fas fa-envelope',
            title: 'Email Management',
            content: getEmailManagementContent()
        },
        'sms-management': {
            icon: 'fas fa-sms',
            title: 'SMS Management',
            content: getSMSManagementContent()
        },
        'webhook-management': {
            icon: 'fas fa-link',
            title: 'Webhook Management',
            content: getWebhookManagementContent()
        },
        'whatsapp-management': {
            icon: 'fab fa-whatsapp',
            title: 'WhatsApp Management',
            content: getWhatsAppManagementContent()
        }
    };
    
    const config = serviceConfig[service];
    if (config) {
        modalIcon.className = config.icon;
        modalTitleText.textContent = config.title;
        modalBody.innerHTML = config.content;
        
        // Initialize service-specific functionality
        initializeServiceFunctionality(service);
    }
}

function getCameraManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>Camera Management</h3>
                <p>Configure and manage surveillance cameras for face recognition</p>
            </div>
            
            <div class="management-form">
                <h4>Add New Camera</h4>
                <form id="cameraManagementForm">
                    <div class="form-group">
                        <label for="cameraNameMgmt">Camera Name</label>
                        <input type="text" id="cameraNameMgmt" class="form-control" placeholder="Enter camera name" required>
                    </div>
                    <div class="form-group">
                        <label for="rtspUrlMgmt">RTSP URL</label>
                        <input type="text" id="rtspUrlMgmt" class="form-control" placeholder="rtsp://username:password@ip:port/path" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Camera
                    </button>
                </form>
            </div>
            
            <div class="management-list">
                <div class="management-list-header">
                    <i class="fas fa-list"></i> Configured Cameras
                </div>
                <div id="cameraManagementList">
                    <div class="empty-state">
                        <i class="fas fa-video"></i>
                        <h3>No Cameras Found</h3>
                        <p>Add your first camera to start monitoring</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getAlertManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>Alert Management</h3>
                <p>Configure system alerts and notification settings</p>
            </div>
            
            <div class="management-form">
                <h4>Alert System Control</h4>
                <div class="alert-controls" style="display: flex; gap: 15px; margin-bottom: 20px;">
                    <button id="startAlertSystem" class="btn btn-success">
                        <i class="fas fa-play"></i> Start Alert System
                    </button>
                    <button id="stopAlertSystem" class="btn btn-danger">
                        <i class="fas fa-stop"></i> Stop Alert System
                    </button>
                    <div class="alert-status" style="display: flex; align-items: center; gap: 10px;">
                        <span>Status:</span>
                        <span id="alertSystemStatus" class="status-badge inactive">Inactive</span>
                    </div>
                </div>
                
                <h4>Alert Configuration</h4>
                <form id="alertConfigForm">
                    <div class="form-group">
                        <label>Active Days</label>
                        <div class="days-selection" style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <label><input type="checkbox" value="Monday"> Monday</label>
                            <label><input type="checkbox" value="Tuesday"> Tuesday</label>
                            <label><input type="checkbox" value="Wednesday"> Wednesday</label>
                            <label><input type="checkbox" value="Thursday"> Thursday</label>
                            <label><input type="checkbox" value="Friday"> Friday</label>
                            <label><input type="checkbox" value="Saturday"> Saturday</label>
                            <label><input type="checkbox" value="Sunday"> Sunday</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="startTime">Start Time</label>
                        <input type="time" id="startTime" class="form-control" value="09:00">
                    </div>
                    <div class="form-group">
                        <label for="endTime">End Time</label>
                        <input type="time" id="endTime" class="form-control" value="17:00">
                    </div>
                    <div class="form-group">
                        <label for="alertFrequency">Alert Frequency (minutes)</label>
                        <input type="number" id="alertFrequency" class="form-control" min="1" max="1440" value="5">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Configuration
                    </button>
                </form>
            </div>
        </div>
    `;
}

function getEmailManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>Email Management</h3>
                <p>Manage email notification contacts</p>
            </div>
            
            <div class="management-form">
                <h4>Add Email Contact</h4>
                <form id="emailManagementForm">
                    <div class="form-group">
                        <label for="emailAddressMgmt">Email Address</label>
                        <input type="email" id="emailAddressMgmt" class="form-control" placeholder="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label for="emailNameMgmt">Full Name</label>
                        <input type="text" id="emailNameMgmt" class="form-control" placeholder="Enter full name" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Contact
                    </button>
                </form>
            </div>
            
            <div class="management-list">
                <div class="management-list-header">
                    <i class="fas fa-list"></i> Email Contacts
                </div>
                <div id="emailManagementList">
                    <div class="empty-state">
                        <i class="fas fa-envelope"></i>
                        <h3>No Email Contacts</h3>
                        <p>Add email contacts to receive notifications</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getSMSManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>SMS Management</h3>
                <p>Manage SMS notification contacts</p>
            </div>
            
            <div class="management-form">
                <h4>Add SMS Contact</h4>
                <form id="smsManagementForm">
                    <div class="form-group">
                        <label for="smsPhoneMgmt">Phone Number</label>
                        <input type="tel" id="smsPhoneMgmt" class="form-control" placeholder="+1234567890" required>
                    </div>
                    <div class="form-group">
                        <label for="smsNameMgmt">Full Name</label>
                        <input type="text" id="smsNameMgmt" class="form-control" placeholder="Enter full name" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Contact
                    </button>
                </form>
            </div>
            
            <div class="management-list">
                <div class="management-list-header">
                    <i class="fas fa-list"></i> SMS Contacts
                </div>
                <div id="smsManagementList">
                    <div class="empty-state">
                        <i class="fas fa-sms"></i>
                        <h3>No SMS Contacts</h3>
                        <p>Add SMS contacts to receive notifications</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getWebhookManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>Webhook Management</h3>
                <p>Configure webhook endpoints for alert notifications</p>
            </div>
            
            <div class="management-form">
                <h4>Add Webhook</h4>
                <form id="webhookManagementForm">
                    <div class="form-group">
                        <label for="webhookUrlMgmt">Webhook URL</label>
                        <input type="url" id="webhookUrlMgmt" class="form-control" placeholder="https://example.com/webhook" required>
                    </div>
                    <div class="form-group">
                        <label for="webhookMethodMgmt">HTTP Method</label>
                        <select id="webhookMethodMgmt" class="form-control">
                            <option value="POST">POST</option>
                            <option value="GET">GET</option>
                            <option value="PUT">PUT</option>
                            <option value="PATCH">PATCH</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="webhookDescMgmt">Description</label>
                        <input type="text" id="webhookDescMgmt" class="form-control" placeholder="Enter description">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Webhook
                    </button>
                </form>
            </div>
            
            <div class="management-list">
                <div class="management-list-header">
                    <i class="fas fa-list"></i> Configured Webhooks
                </div>
                <div id="webhookManagementList">
                    <div class="empty-state">
                        <i class="fas fa-link"></i>
                        <h3>No Webhooks Configured</h3>
                        <p>Add webhook endpoints to receive alerts</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getWhatsAppManagementContent() {
    return `
        <div class="service-section active">
            <div class="service-header">
                <h3>WhatsApp Management</h3>
                <p>Manage WhatsApp notification contacts</p>
            </div>
            
            <div class="management-form">
                <h4>Add WhatsApp Contact</h4>
                <form id="whatsappManagementForm">
                    <div class="form-group">
                        <label for="whatsappPhoneMgmt">Phone Number</label>
                        <input type="tel" id="whatsappPhoneMgmt" class="form-control" placeholder="+1234567890" required>
                    </div>
                    <div class="form-group">
                        <label for="whatsappNameMgmt">Full Name</label>
                        <input type="text" id="whatsappNameMgmt" class="form-control" placeholder="Enter full name" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Contact
                    </button>
                </form>
            </div>
            
            <div class="management-list">
                <div class="management-list-header">
                    <i class="fas fa-list"></i> WhatsApp Contacts
                </div>
                <div id="whatsappManagementList">
                    <div class="empty-state">
                        <i class="fab fa-whatsapp"></i>
                        <h3>No WhatsApp Contacts</h3>
                        <p>Add WhatsApp contacts to receive notifications</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function closeManagementModal() {
    const modal = document.getElementById('managementModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function initializeServiceFunctionality(service) {
    // Initialize service-specific functionality based on the service type
    switch(service) {
        case 'camera-management':
            initializeCameraManagement();
            break;
        case 'alert-management':
            initializeAlertManagement();
            break;
        case 'email-management':
            initializeEmailManagement();
            break;
        case 'sms-management':
            initializeSMSManagement();
            break;
        case 'webhook-management':
            initializeWebhookManagement();
            break;
        case 'whatsapp-management':
            initializeWhatsAppManagement();
            break;
    }
}

function initializeAdminTabs() {
    // Initialize existing admin tab functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            console.log('Tab clicked:', targetTab);

            // Special handling for management tab
            if (targetTab === 'management') {
                console.log('Management tab clicked - activating full screen mode');

                // Hide all other content and show management interface
                document.body.classList.add('management-active');

                // Remove active class from all buttons and panes
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Add active class to management button and pane
                this.classList.add('active');
                const managementPane = document.getElementById('management-tab');
                if (managementPane) {
                    managementPane.classList.add('active');
                    console.log('Management pane activated');
                } else {
                    console.error('Management pane not found!');
                }

                // Initialize management tab if not already done
                if (!window.managementInitialized) {
                    setTimeout(() => {
                        console.log('Initializing management functionality...');
                        initializeManagementTab();
                        window.managementInitialized = true;
                    }, 100);
                }

                return;
            }

            // Normal tab handling for non-management tabs
            document.body.classList.remove('management-active');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            const targetPane = document.getElementById(targetTab + '-tab');
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

// Service-specific initialization functions
function initializeCameraManagement() {
    const form = document.getElementById('cameraManagementForm');
    if (form) {
        form.addEventListener('submit', handleCameraSubmit);
        loadCamerasManagement();
    }
}

function initializeAlertManagement() {
    const startBtn = document.getElementById('startAlertSystem');
    const stopBtn = document.getElementById('stopAlertSystem');
    const configForm = document.getElementById('alertConfigForm');

    if (startBtn) startBtn.addEventListener('click', startAlertSystem);
    if (stopBtn) stopBtn.addEventListener('click', stopAlertSystem);
    if (configForm) configForm.addEventListener('submit', saveAlertConfig);

    loadAlertStatus();
    loadAlertConfig();
}

function initializeEmailManagement() {
    const form = document.getElementById('emailManagementForm');
    if (form) {
        form.addEventListener('submit', handleEmailSubmit);
        loadEmailsManagement();
    }
}

function initializeSMSManagement() {
    const form = document.getElementById('smsManagementForm');
    if (form) {
        form.addEventListener('submit', handleSMSSubmit);
        loadSMSManagement();
    }
}

function initializeWebhookManagement() {
    const form = document.getElementById('webhookManagementForm');
    if (form) {
        form.addEventListener('submit', handleWebhookSubmit);
        loadWebhooksManagement();
    }
}

function initializeWhatsAppManagement() {
    const form = document.getElementById('whatsappManagementForm');
    if (form) {
        form.addEventListener('submit', handleWhatsAppSubmit);
        loadWhatsAppManagement();
    }
}

// Camera Management Functions
async function handleCameraSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const cameraData = {
        cameraName: formData.get('cameraNameMgmt') || document.getElementById('cameraNameMgmt').value,
        rtspUrl: formData.get('rtspUrlMgmt') || document.getElementById('rtspUrlMgmt').value
    };

    try {
        const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(cameraData)
        });

        const result = await response.json();
        if (result.status === 'success') {
            showNotification('Camera added successfully!', 'success');
            e.target.reset();
            loadCamerasManagement();
        } else {
            showNotification(result.message || 'Failed to add camera', 'error');
        }
    } catch (error) {
        showNotification('Error adding camera: ' + error.message, 'error');
    }
}

async function loadCamerasManagement() {
    try {
        const response = await fetch('/face_recognition/cameras');
        const cameras = await response.json();
        const listContainer = document.getElementById('cameraManagementList');

        if (cameras.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-video"></i>
                    <h3>No Cameras Found</h3>
                    <p>Add your first camera to start monitoring</p>
                </div>
            `;
            return;
        }

        let html = '';
        cameras.forEach(camera => {
            const statusClass = camera.is_active ? 'active' : 'inactive';
            const statusText = camera.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="item-info">
                        <h4>${camera.name}</h4>
                        <p>${camera.rtsp_url}</p>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="deleteCamera('${camera.name}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    } catch (error) {
        console.error('Error loading cameras:', error);
    }
}

// Alert Management Functions
async function startAlertSystem() {
    try {
        const response = await fetch('/face_recognition/alert-system/start', { method: 'POST' });
        const result = await response.json();
        showNotification(result.message, result.status === 'success' ? 'success' : 'info');
        loadAlertStatus();
    } catch (error) {
        showNotification('Error starting alert system: ' + error.message, 'error');
    }
}

async function stopAlertSystem() {
    try {
        const response = await fetch('/face_recognition/alert-system/stop', { method: 'POST' });
        const result = await response.json();
        showNotification(result.message, result.status === 'success' ? 'success' : 'info');
        loadAlertStatus();
    } catch (error) {
        showNotification('Error stopping alert system: ' + error.message, 'error');
    }
}

async function loadAlertStatus() {
    try {
        const response = await fetch('/face_recognition/alert-system/status');
        const status = await response.json();
        const statusElement = document.getElementById('alertSystemStatus');

        if (statusElement) {
            statusElement.textContent = status.active ? 'Active' : 'Inactive';
            statusElement.className = `status-badge ${status.active ? 'active' : 'inactive'}`;
        }
    } catch (error) {
        console.error('Error loading alert status:', error);
    }
}

async function saveAlertConfig(e) {
    e.preventDefault();

    const selectedDays = Array.from(document.querySelectorAll('.days-selection input:checked'))
        .map(input => input.value);

    const configData = {
        active: document.getElementById('alertSystemStatus').textContent === 'Active',
        days: JSON.stringify(selectedDays),
        start_time: document.getElementById('startTime').value,
        end_time: document.getElementById('endTime').value,
        alert_frequency: parseInt(document.getElementById('alertFrequency').value)
    };

    try {
        const response = await fetch('/face_recognition/alert-config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(configData)
        });

        const result = await response.json();
        showNotification('Alert configuration saved successfully!', 'success');
    } catch (error) {
        showNotification('Error saving alert configuration: ' + error.message, 'error');
    }
}

async function loadAlertConfig() {
    try {
        const response = await fetch('/face_recognition/alert-config');
        const config = await response.json();

        // Load days
        const days = JSON.parse(config.days || '[]');
        days.forEach(day => {
            const checkbox = document.querySelector(`.days-selection input[value="${day}"]`);
            if (checkbox) checkbox.checked = true;
        });

        // Load times
        if (config.start_time) document.getElementById('startTime').value = config.start_time;
        if (config.end_time) document.getElementById('endTime').value = config.end_time;
        if (config.alert_frequency) document.getElementById('alertFrequency').value = config.alert_frequency;

    } catch (error) {
        console.error('Error loading alert config:', error);
    }
}

// Email Management Functions
async function handleEmailSubmit(e) {
    e.preventDefault();
    const emailData = {
        email_address: document.getElementById('emailAddressMgmt').value,
        full_name: document.getElementById('emailNameMgmt').value
    };

    try {
        const response = await fetch('/face_recognition/email', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(emailData)
        });

        const result = await response.json();
        if (response.ok) {
            showNotification('Email contact added successfully!', 'success');
            e.target.reset();
            loadEmailsManagement();
        } else {
            showNotification(result.detail || 'Failed to add email contact', 'error');
        }
    } catch (error) {
        showNotification('Error adding email contact: ' + error.message, 'error');
    }
}

async function loadEmailsManagement() {
    try {
        const response = await fetch('/face_recognition/email');
        const emails = await response.json();
        const listContainer = document.getElementById('emailManagementList');

        if (emails.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-envelope"></i>
                    <h3>No Email Contacts</h3>
                    <p>Add email contacts to receive notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        emails.forEach(email => {
            const statusClass = email.is_active ? 'active' : 'inactive';
            const statusText = email.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="item-info">
                        <h4>${email.full_name}</h4>
                        <p>${email.email_address}</p>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="deleteEmail(${email.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    } catch (error) {
        console.error('Error loading emails:', error);
    }
}

// SMS Management Functions
async function handleSMSSubmit(e) {
    e.preventDefault();
    const smsData = {
        phone_number: document.getElementById('smsPhoneMgmt').value,
        full_name: document.getElementById('smsNameMgmt').value
    };

    try {
        const response = await fetch('/face_recognition/sms', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(smsData)
        });

        const result = await response.json();
        if (response.ok) {
            showNotification('SMS contact added successfully!', 'success');
            e.target.reset();
            loadSMSManagement();
        } else {
            showNotification(result.detail || 'Failed to add SMS contact', 'error');
        }
    } catch (error) {
        showNotification('Error adding SMS contact: ' + error.message, 'error');
    }
}

async function loadSMSManagement() {
    try {
        const response = await fetch('/face_recognition/sms');
        const smsContacts = await response.json();
        const listContainer = document.getElementById('smsManagementList');

        if (smsContacts.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-sms"></i>
                    <h3>No SMS Contacts</h3>
                    <p>Add SMS contacts to receive notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        smsContacts.forEach(sms => {
            const statusClass = sms.is_active ? 'active' : 'inactive';
            const statusText = sms.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="item-info">
                        <h4>${sms.full_name}</h4>
                        <p>${sms.phone_number}</p>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="deleteSMS(${sms.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    } catch (error) {
        console.error('Error loading SMS contacts:', error);
    }
}

// Webhook Management Functions
async function handleWebhookSubmit(e) {
    e.preventDefault();
    const webhookData = {
        url: document.getElementById('webhookUrlMgmt').value,
        http_method: document.getElementById('webhookMethodMgmt').value,
        description: document.getElementById('webhookDescMgmt').value
    };

    try {
        const response = await fetch('/face_recognition/webhooks', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(webhookData)
        });

        const result = await response.json();
        if (response.ok) {
            showNotification('Webhook added successfully!', 'success');
            e.target.reset();
            loadWebhooksManagement();
        } else {
            showNotification(result.detail || 'Failed to add webhook', 'error');
        }
    } catch (error) {
        showNotification('Error adding webhook: ' + error.message, 'error');
    }
}

async function loadWebhooksManagement() {
    try {
        const response = await fetch('/face_recognition/webhooks');
        const webhooks = await response.json();
        const listContainer = document.getElementById('webhookManagementList');

        if (webhooks.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-link"></i>
                    <h3>No Webhooks Configured</h3>
                    <p>Add webhook endpoints to receive alerts</p>
                </div>
            `;
            return;
        }

        let html = '';
        webhooks.forEach(webhook => {
            const statusClass = webhook.is_active ? 'active' : 'inactive';
            const statusText = webhook.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="item-info">
                        <h4>${webhook.description || 'Webhook'}</h4>
                        <p>${webhook.http_method} ${webhook.url}</p>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="deleteWebhook(${webhook.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    } catch (error) {
        console.error('Error loading webhooks:', error);
    }
}

// WhatsApp Management Functions
async function handleWhatsAppSubmit(e) {
    e.preventDefault();
    const whatsappData = {
        phone_number: document.getElementById('whatsappPhoneMgmt').value,
        full_name: document.getElementById('whatsappNameMgmt').value
    };

    try {
        const response = await fetch('/face_recognition/whatsapp', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(whatsappData)
        });

        const result = await response.json();
        if (response.ok) {
            showNotification('WhatsApp contact added successfully!', 'success');
            e.target.reset();
            loadWhatsAppManagement();
        } else {
            showNotification(result.detail || 'Failed to add WhatsApp contact', 'error');
        }
    } catch (error) {
        showNotification('Error adding WhatsApp contact: ' + error.message, 'error');
    }
}

async function loadWhatsAppManagement() {
    try {
        const response = await fetch('/face_recognition/whatsapp');
        const whatsappContacts = await response.json();
        const listContainer = document.getElementById('whatsappManagementList');

        if (whatsappContacts.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fab fa-whatsapp"></i>
                    <h3>No WhatsApp Contacts</h3>
                    <p>Add WhatsApp contacts to receive notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        whatsappContacts.forEach(whatsapp => {
            const statusClass = whatsapp.is_active ? 'active' : 'inactive';
            const statusText = whatsapp.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="item-info">
                        <h4>${whatsapp.full_name}</h4>
                        <p>${whatsapp.phone_number}</p>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="deleteWhatsApp(${whatsapp.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    } catch (error) {
        console.error('Error loading WhatsApp contacts:', error);
    }
}

// Delete functions
async function deleteCamera(cameraName) {
    if (!confirm(`Are you sure you want to delete camera "${cameraName}"?`)) return;

    try {
        const response = await fetch(`/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.status === 'success') {
            showNotification('Camera deleted successfully!', 'success');
            loadCamerasManagement();
        } else {
            showNotification(result.message || 'Failed to delete camera', 'error');
        }
    } catch (error) {
        showNotification('Error deleting camera: ' + error.message, 'error');
    }
}

async function deleteEmail(emailId) {
    if (!confirm('Are you sure you want to delete this email contact?')) return;

    try {
        const response = await fetch(`/face_recognition/email/${emailId}`, { method: 'DELETE' });
        const result = await response.json();
        showNotification('Email contact deleted successfully!', 'success');
        loadEmailsManagement();
    } catch (error) {
        showNotification('Error deleting email contact: ' + error.message, 'error');
    }
}

async function deleteSMS(smsId) {
    if (!confirm('Are you sure you want to delete this SMS contact?')) return;

    try {
        const response = await fetch(`/face_recognition/sms/${smsId}`, { method: 'DELETE' });
        const result = await response.json();
        showNotification('SMS contact deleted successfully!', 'success');
        loadSMSManagement();
    } catch (error) {
        showNotification('Error deleting SMS contact: ' + error.message, 'error');
    }
}

async function deleteWebhook(webhookId) {
    if (!confirm('Are you sure you want to delete this webhook?')) return;

    try {
        const response = await fetch(`/face_recognition/webhooks/${webhookId}`, { method: 'DELETE' });
        const result = await response.json();
        showNotification('Webhook deleted successfully!', 'success');
        loadWebhooksManagement();
    } catch (error) {
        showNotification('Error deleting webhook: ' + error.message, 'error');
    }
}

async function deleteWhatsApp(whatsappId) {
    if (!confirm('Are you sure you want to delete this WhatsApp contact?')) return;

    try {
        const response = await fetch(`/face_recognition/whatsapp/${whatsappId}`, { method: 'DELETE' });
        const result = await response.json();
        showNotification('WhatsApp contact deleted successfully!', 'success');
        loadWhatsAppManagement();
    } catch (error) {
        showNotification('Error deleting WhatsApp contact: ' + error.message, 'error');
    }
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => notification.style.transform = 'translateX(0)', 100);
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// ==================== MANAGEMENT TAB FUNCTIONALITY ====================

function initializeManagementTab() {
    // Initialize management navigation
    initializeManagementNavigation();

    // Initialize all management modules
    initializeManagementCameraModule();
    initializeManagementWebhookModule();
    initializeManagementWhatsAppModule();
    initializeManagementSMSModule();
    initializeManagementEmailModule();
    initializeManagementAlertModule();
}

function initializeManagementNavigation() {
    // Navigation mapping for management tab
    const mgmtNavMapping = {
        'mgmtCameraManagementNav': {
            section: 'mgmtCameraManagementSection',
            title: 'Camera Management',
            subtitle: 'Configure surveillance cameras',
            icon: 'fas fa-video'
        },
        'mgmtWebhookManagementNav': {
            section: 'mgmtWebhookManagementSection',
            title: 'Webhook Management',
            subtitle: 'Configure webhook endpoints',
            icon: 'fas fa-link'
        },
        'mgmtWhatsappManagementNav': {
            section: 'mgmtWhatsappManagementSection',
            title: 'WhatsApp Management',
            subtitle: 'Manage WhatsApp contacts',
            icon: 'fab fa-whatsapp'
        },
        'mgmtSmsManagementNav': {
            section: 'mgmtSmsManagementSection',
            title: 'SMS Management',
            subtitle: 'Manage SMS contacts',
            icon: 'fas fa-sms'
        },
        'mgmtEmailManagementNav': {
            section: 'mgmtEmailManagementSection',
            title: 'Email Management',
            subtitle: 'Manage email contacts',
            icon: 'fas fa-envelope'
        },
        'mgmtAlertManagementNav': {
            section: 'mgmtAlertManagementSection',
            title: 'Alert Management',
            subtitle: 'Configure alert system',
            icon: 'fas fa-bell'
        }
    };

    const mgmtNavItems = document.querySelectorAll('.management-nav-item');
    const mgmtPageTitle = document.getElementById('mgmtPageTitle');
    const mgmtPageSubtitle = document.getElementById('mgmtPageSubtitle');

    function activateMgmtNavTab(navId) {
        const mapping = mgmtNavMapping[navId];
        if (!mapping) return;

        // Remove active class from all nav items and sections
        mgmtNavItems.forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.management-module-section').forEach(section => section.classList.remove('active'));

        // Add active class to clicked nav item
        const activeNavItem = document.getElementById(navId);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Show corresponding section
        const targetSection = document.getElementById(mapping.section);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update page title and subtitle
        if (mgmtPageTitle) {
            mgmtPageTitle.innerHTML = `<i class="${mapping.icon}"></i><span>${mapping.title}</span>`;
        }
        if (mgmtPageSubtitle) {
            mgmtPageSubtitle.textContent = mapping.subtitle;
        }

        // Save active tab to localStorage
        localStorage.setItem('activeMgmtTab', navId);

        // Update stats when switching tabs
        setTimeout(updateMgmtStats, 100);
    }

    // Navigation click handlers
    mgmtNavItems.forEach(item => {
        item.addEventListener('click', function() {
            activateMgmtNavTab(this.id);
        });
    });

    // Restore active tab on page load
    const savedActiveTab = localStorage.getItem('activeMgmtTab');
    if (savedActiveTab && mgmtNavMapping[savedActiveTab]) {
        activateMgmtNavTab(savedActiveTab);
    } else {
        // Default to camera management if no saved tab
        activateMgmtNavTab('mgmtCameraManagementNav');
    }
}

function updateMgmtStats() {
    // Update stats based on active section
    const activeSection = document.querySelector('.management-module-section.active');
    if (!activeSection) return;

    const sectionId = activeSection.id;

    switch(sectionId) {
        case 'mgmtCameraManagementSection':
            loadMgmtCameraStats();
            break;
        case 'mgmtWebhookManagementSection':
            loadMgmtWebhookStats();
            break;
        case 'mgmtWhatsappManagementSection':
            loadMgmtWhatsAppStats();
            break;
        case 'mgmtSmsManagementSection':
            loadMgmtSMSStats();
            break;
        case 'mgmtEmailManagementSection':
            loadMgmtEmailStats();
            break;
        case 'mgmtAlertManagementSection':
            loadMgmtAlertStats();
            break;
    }
}

async function loadMgmtCameraStats() {
    try {
        const response = await fetch('/face_recognition/get-cameras');
        const cameras = await response.json();

        const totalCameras = Object.keys(cameras).length;
        const activeCameras = Object.values(cameras).filter(cameraData => {
            // Handle both old format (array) and new format (object)
            return Array.isArray(cameraData) ? true : (cameraData.is_active !== undefined ? cameraData.is_active : true);
        }).length;

        const totalElement = document.getElementById('mgmtTotalCameras');
        const activeElement = document.getElementById('mgmtActiveCameras');

        if (totalElement) totalElement.textContent = totalCameras;
        if (activeElement) activeElement.textContent = activeCameras;
    } catch (error) {
        console.error('Error loading camera stats:', error);
    }
}

async function loadMgmtWebhookStats() {
    // Placeholder for webhook stats
    const totalElement = document.getElementById('mgmtTotalCameras');
    const activeElement = document.getElementById('mgmtActiveCameras');

    if (totalElement) {
        totalElement.textContent = '0';
        totalElement.parentElement.querySelector('.management-stat-label').textContent = 'Webhooks';
    }
    if (activeElement) {
        activeElement.textContent = '0';
        activeElement.parentElement.querySelector('.management-stat-label').textContent = 'Active';
    }
}

async function loadMgmtWhatsAppStats() {
    // Placeholder for WhatsApp stats
    const totalElement = document.getElementById('mgmtTotalCameras');
    const activeElement = document.getElementById('mgmtActiveCameras');

    if (totalElement) {
        totalElement.textContent = '0';
        totalElement.parentElement.querySelector('.management-stat-label').textContent = 'Contacts';
    }
    if (activeElement) {
        activeElement.textContent = '0';
        activeElement.parentElement.querySelector('.management-stat-label').textContent = 'Active';
    }
}

async function loadMgmtSMSStats() {
    // Placeholder for SMS stats
    const totalElement = document.getElementById('mgmtTotalCameras');
    const activeElement = document.getElementById('mgmtActiveCameras');

    if (totalElement) {
        totalElement.textContent = '0';
        totalElement.parentElement.querySelector('.management-stat-label').textContent = 'Contacts';
    }
    if (activeElement) {
        activeElement.textContent = '0';
        activeElement.parentElement.querySelector('.management-stat-label').textContent = 'Active';
    }
}

async function loadMgmtEmailStats() {
    // Placeholder for Email stats
    const totalElement = document.getElementById('mgmtTotalCameras');
    const activeElement = document.getElementById('mgmtActiveCameras');

    if (totalElement) {
        totalElement.textContent = '0';
        totalElement.parentElement.querySelector('.management-stat-label').textContent = 'Contacts';
    }
    if (activeElement) {
        activeElement.textContent = '0';
        activeElement.parentElement.querySelector('.management-stat-label').textContent = 'Active';
    }
}

async function loadMgmtAlertStats() {
    // Placeholder for Alert stats
    const totalElement = document.getElementById('mgmtTotalCameras');
    const activeElement = document.getElementById('mgmtActiveCameras');

    if (totalElement) {
        totalElement.textContent = '0';
        totalElement.parentElement.querySelector('.management-stat-label').textContent = 'Rules';
    }
    if (activeElement) {
        activeElement.textContent = '0';
        activeElement.parentElement.querySelector('.management-stat-label').textContent = 'Active';
    }
}

// Management Module Initialization Functions
function initializeManagementCameraModule() {
    const cameraForm = document.getElementById('mgmtCameraForm');
    const refreshBtn = document.getElementById('mgmtRefreshCamerasBtn');

    if (cameraForm) {
        cameraForm.addEventListener('submit', handleMgmtCameraSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtCameras);
    }

    // Load cameras on initialization
    loadMgmtCameras();
}

function initializeManagementWebhookModule() {
    const webhookForm = document.getElementById('mgmtWebhookForm');
    const refreshBtn = document.getElementById('mgmtRefreshWebhooksBtn');

    if (webhookForm) {
        webhookForm.addEventListener('submit', handleMgmtWebhookSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtWebhooks);
    }

    // Load webhooks on initialization
    loadMgmtWebhooks();
}

function initializeManagementWhatsAppModule() {
    const whatsappForm = document.getElementById('mgmtWhatsappForm');
    const refreshBtn = document.getElementById('mgmtRefreshWhatsAppBtn');

    if (whatsappForm) {
        whatsappForm.addEventListener('submit', handleMgmtWhatsAppSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtWhatsAppContacts);
    }

    // Load contacts on initialization
    loadMgmtWhatsAppContacts();
}

function initializeManagementSMSModule() {
    const smsForm = document.getElementById('mgmtSmsForm');
    const refreshBtn = document.getElementById('mgmtRefreshSMSBtn');

    if (smsForm) {
        smsForm.addEventListener('submit', handleMgmtSMSSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtSMSContacts);
    }

    // Load contacts on initialization
    loadMgmtSMSContacts();
}

function initializeManagementEmailModule() {
    const emailForm = document.getElementById('mgmtEmailForm');
    const refreshBtn = document.getElementById('mgmtRefreshEmailBtn');

    if (emailForm) {
        emailForm.addEventListener('submit', handleMgmtEmailSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtEmailContacts);
    }

    // Load contacts on initialization
    loadMgmtEmailContacts();
}

function initializeManagementAlertModule() {
    const alertForm = document.getElementById('mgmtAlertForm');
    const startBtn = document.getElementById('mgmtStartAlertBtn');
    const stopBtn = document.getElementById('mgmtStopAlertBtn');

    if (alertForm) {
        alertForm.addEventListener('submit', handleMgmtAlertSubmit);
    }

    if (startBtn) {
        startBtn.addEventListener('click', startMgmtAlertSystem);
    }

    if (stopBtn) {
        stopBtn.addEventListener('click', stopMgmtAlertSystem);
    }

    // Load alert settings on initialization
    loadMgmtAlertSettings();
}

// Management Camera Functions - Exactly like crowd_detection
async function handleMgmtCameraSubmit(e) {
    e.preventDefault();

    const submitBtn = e.target.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    }

    const cameraName = document.getElementById('mgmtCameraName').value;
    const rtspUrl = document.getElementById('mgmtRtspUrl').value;

    try {
        const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cameraName: cameraName,
                rtspUrl: rtspUrl
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Camera added successfully!');
            document.getElementById('mgmtCameraForm').reset();
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to add camera', 'error');
        }
    } catch (error) {
        console.error('Error adding camera:', error);
        showNotification('Error adding camera: ' + error.message, 'error');
    } finally {
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Camera';
        }
    }
}

async function loadMgmtCameras() {
    try {
        console.log('Loading cameras...');
        const response = await fetch('/face_recognition/get-cameras');
        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        console.log('Cameras loaded:', cameras);

        const cameraList = document.getElementById('mgmtCameraList');
        if (!cameraList) {
            console.error('Camera list element not found!');
            return;
        }

        if (Object.keys(cameras).length === 0) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-video"></i>
                    <h3>No Cameras Found</h3>
                    <p>Add your first camera to start monitoring</p>
                </div>
            `;
            return;
        }

        let html = '';
        Object.entries(cameras).forEach(([name, cameraData]) => {
            // Handle both old format (array) and new format (object)
            const rtspUrl = Array.isArray(cameraData) ? (cameraData[0] || 'No URL') : (cameraData.rtsp_url || 'No URL');
            const isActive = Array.isArray(cameraData) ? true : (cameraData.is_active !== undefined ? cameraData.is_active : true);
            const statusClass = isActive ? 'success' : 'danger';
            const statusText = isActive ? 'Active' : 'Inactive';

            html += `
                <div class="management-camera-card">
                    <div class="management-camera-card-header">
                        <div class="management-camera-info">
                            <h4 class="management-camera-name">${name}</h4>
                            <p class="management-camera-url">${rtspUrl}</p>
                        </div>
                        <div class="management-camera-status">
                            <span class="management-status-badge management-status-${statusClass}">${statusText}</span>
                        </div>
                    </div>
                    <div class="management-camera-card-body">
                        <div class="management-camera-actions">
                            <button class="management-action-btn management-action-edit" onclick="editMgmtCamera('${name}', '${rtspUrl}')" title="Edit Camera">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                            <button class="management-action-btn management-action-${isActive ? 'deactivate' : 'activate'}" onclick="toggleMgmtCamera('${name}', ${isActive})" title="Toggle Active/Inactive">
                                <i class="fas ${isActive ? 'fa-pause' : 'fa-play'}"></i>
                                <span>${isActive ? 'Deactivate' : 'Activate'}</span>
                            </button>
                            <button class="management-action-btn management-action-delete" onclick="deleteMgmtCamera('${name}')" title="Delete Camera">
                                <i class="fas fa-trash"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        // Handle empty state
        if (Object.keys(cameras).length === 0) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-video" style="color: #ccc; font-size: 48px; margin-bottom: 16px;"></i>
                    <h3 style="color: #666;">No cameras configured</h3>
                    <p style="color: #999;">Add a camera to get started with face recognition</p>
                    <button class="management-btn management-btn-primary" onclick="addTestCamera()" style="margin-top: 16px;">
                        <i class="fas fa-plus"></i> Add Test Camera
                    </button>
                </div>
            `;
        } else {
            cameraList.innerHTML = html;
        }

        // Update stats like crowd_detection
        const totalElement = document.getElementById('mgmtTotalCameras');
        const activeElement = document.getElementById('mgmtActiveCameras');

        const totalCameras = Object.keys(cameras).length;
        const activeCameras = Object.values(cameras).filter(cameraData => {
            // Handle both old format (array) and new format (object)
            return Array.isArray(cameraData) ? true : (cameraData.is_active !== undefined ? cameraData.is_active : true);
        }).length;

        if (totalElement) totalElement.textContent = totalCameras;
        if (activeElement) activeElement.textContent = activeCameras;

        console.log('Camera list updated with HTML:', html);
        console.log('Camera data received:', cameras);
    } catch (error) {
        console.error('Error loading cameras:', error);
        const cameraList = document.getElementById('mgmtCameraList');
        if (cameraList) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-exclamation-triangle" style="color: var(--danger);"></i>
                    <h3 style="color: var(--danger);">Error Loading Cameras</h3>
                    <p style="color: var(--danger);">${error.message}</p>
                </div>
            `;
        }
    }
}

// Management Webhook Functions
async function handleMgmtWebhookSubmit(e) {
    e.preventDefault();

    const webhookUrl = document.getElementById('mgmtWebhookUrl').value;
    const webhookMethod = document.getElementById('mgmtWebhookMethod').value;
    const webhookDescription = document.getElementById('mgmtWebhookDescription').value;
    const webhookTemplate = document.getElementById('mgmtWebhookTemplate').value;

    try {
        const response = await fetch('/face_recognition/webhooks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: webhookUrl,
                http_method: webhookMethod,
                description: webhookDescription,
                body_template: webhookTemplate
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Webhook added successfully!', 'success');
            document.getElementById('mgmtWebhookForm').reset();
            loadMgmtWebhooks();
        } else {
            showNotification(result.detail || 'Failed to add webhook', 'error');
        }
    } catch (error) {
        console.error('Error adding webhook:', error);
        showNotification('Error adding webhook: ' + error.message, 'error');
    }
}

async function loadMgmtWebhooks() {
    try {
        const response = await fetch('/face_recognition/webhooks');
        const webhooks = await response.json();

        const webhookList = document.getElementById('mgmtWebhookList');
        if (!webhookList) return;

        if (webhooks.length === 0) {
            webhookList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-link"></i>
                    <h3>No Webhooks Configured</h3>
                    <p>Add your first webhook endpoint to receive alerts</p>
                </div>
            `;
            return;
        }

        let html = '';
        webhooks.forEach(webhook => {
            const statusClass = webhook.is_active ? 'success' : 'danger';
            const statusText = webhook.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="management-item-info">
                        <h4>${webhook.url}</h4>
                        <p><strong>Method:</strong> ${webhook.http_method || 'POST'} | <strong>Description:</strong> ${webhook.description || 'No description'}</p>
                        <span class="management-badge management-badge-${statusClass}">${statusText}</span>
                    </div>
                    <div class="management-item-actions">
                        <button class="management-btn management-btn-primary management-btn-sm" onclick="editMgmtWebhook(${webhook.id}, '${webhook.url}', '${webhook.http_method || 'POST'}', '${webhook.description || ''}')" title="Edit Webhook">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="management-btn management-btn-${webhook.is_active ? 'warning' : 'success'} management-btn-sm" onclick="toggleMgmtWebhook(${webhook.id}, ${webhook.is_active})" title="Toggle Active/Inactive">
                            <i class="fas ${webhook.is_active ? 'fa-pause' : 'fa-play'}"></i> ${webhook.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button class="management-btn management-btn-secondary management-btn-sm" onclick="deleteMgmtWebhook(${webhook.id})" title="Delete Webhook">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        webhookList.innerHTML = html;
    } catch (error) {
        console.error('Error loading webhooks:', error);
        const webhookList = document.getElementById('mgmtWebhookList');
        if (webhookList) {
            webhookList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-link"></i>
                    <h3>No Webhooks Configured</h3>
                    <p>Add your first webhook endpoint to receive alerts</p>
                </div>
            `;
        }
    }
}

// Management WhatsApp Functions
async function handleMgmtWhatsAppSubmit(e) {
    e.preventDefault();

    const phoneNumber = document.getElementById('mgmtWhatsappPhone').value;
    const fullName = document.getElementById('mgmtWhatsappName').value;

    try {
        const response = await fetch('/face_recognition/whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phoneNumber,
                full_name: fullName
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('WhatsApp contact added successfully!', 'success');
            document.getElementById('mgmtWhatsappForm').reset();
            loadMgmtWhatsAppContacts();
        } else {
            showNotification(result.detail || 'Failed to add WhatsApp contact', 'error');
        }
    } catch (error) {
        console.error('Error adding WhatsApp contact:', error);
        showNotification('Error adding WhatsApp contact: ' + error.message, 'error');
    }
}

async function loadMgmtWhatsAppContacts() {
    try {
        const response = await fetch('/face_recognition/whatsapp');
        const contacts = await response.json();

        const contactList = document.getElementById('mgmtWhatsappList');
        if (!contactList) return;

        if (contacts.length === 0) {
            contactList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fab fa-whatsapp"></i>
                    <h3>No WhatsApp Contacts</h3>
                    <p>Add contacts to receive WhatsApp notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        contacts.forEach(contact => {
            const statusClass = contact.is_active ? 'success' : 'danger';
            const statusText = contact.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="management-item-info">
                        <h4>${contact.full_name}</h4>
                        <p>${contact.phone_number}</p>
                        <span class="management-badge management-badge-${statusClass}">${statusText}</span>
                    </div>
                    <div class="management-item-actions">
                        <button class="management-btn management-btn-primary management-btn-sm" onclick="editMgmtWhatsApp(${contact.id}, '${contact.full_name}', '${contact.phone_number}')" title="Edit WhatsApp Contact">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="management-btn management-btn-${contact.is_active ? 'warning' : 'success'} management-btn-sm" onclick="toggleMgmtWhatsApp(${contact.id}, ${contact.is_active})" title="Toggle Active/Inactive">
                            <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}"></i> ${contact.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button class="management-btn management-btn-secondary management-btn-sm" onclick="deleteMgmtWhatsApp(${contact.id})" title="Delete WhatsApp Contact">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        contactList.innerHTML = html;
    } catch (error) {
        console.error('Error loading WhatsApp contacts:', error);
    }
}

// Management SMS Functions
async function handleMgmtSMSSubmit(e) {
    e.preventDefault();

    const phoneNumber = document.getElementById('mgmtSmsPhone').value;
    const fullName = document.getElementById('mgmtSmsName').value;

    try {
        const response = await fetch('/face_recognition/sms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: phoneNumber,
                full_name: fullName
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('SMS contact added successfully!', 'success');
            document.getElementById('mgmtSmsForm').reset();
            loadMgmtSMSContacts();
        } else {
            showNotification(result.detail || 'Failed to add SMS contact', 'error');
        }
    } catch (error) {
        console.error('Error adding SMS contact:', error);
        showNotification('Error adding SMS contact: ' + error.message, 'error');
    }
}

async function loadMgmtSMSContacts() {
    try {
        const response = await fetch('/face_recognition/sms');
        const contacts = await response.json();

        const contactList = document.getElementById('mgmtSmsList');
        if (!contactList) return;

        if (contacts.length === 0) {
            contactList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-sms"></i>
                    <h3>No SMS Contacts</h3>
                    <p>Add contacts to receive SMS notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        contacts.forEach(contact => {
            const statusClass = contact.is_active ? 'success' : 'danger';
            const statusText = contact.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="management-item-info">
                        <h4>${contact.full_name}</h4>
                        <p>${contact.phone_number}</p>
                        <span class="management-badge management-badge-${statusClass}">${statusText}</span>
                    </div>
                    <div class="management-item-actions">
                        <button class="management-btn management-btn-primary management-btn-sm" onclick="editMgmtSMS(${contact.id}, '${contact.full_name}', '${contact.phone_number}')" title="Edit SMS Contact">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="management-btn management-btn-${contact.is_active ? 'warning' : 'success'} management-btn-sm" onclick="toggleMgmtSMS(${contact.id}, ${contact.is_active})" title="Toggle Active/Inactive">
                            <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}"></i> ${contact.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button class="management-btn management-btn-secondary management-btn-sm" onclick="deleteMgmtSMS(${contact.id})" title="Delete SMS Contact">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        contactList.innerHTML = html;
    } catch (error) {
        console.error('Error loading SMS contacts:', error);
    }
}

// Management Email Functions
async function handleMgmtEmailSubmit(e) {
    e.preventDefault();

    const emailAddress = document.getElementById('mgmtEmailAddress').value;
    const fullName = document.getElementById('mgmtEmailName').value;

    try {
        const response = await fetch('/face_recognition/email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email_address: emailAddress,
                full_name: fullName
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Email contact added successfully!', 'success');
            document.getElementById('mgmtEmailForm').reset();
            loadMgmtEmailContacts();
        } else {
            showNotification(result.detail || 'Failed to add email contact', 'error');
        }
    } catch (error) {
        console.error('Error adding email contact:', error);
        showNotification('Error adding email contact: ' + error.message, 'error');
    }
}

async function loadMgmtEmailContacts() {
    try {
        const response = await fetch('/face_recognition/email');
        const contacts = await response.json();

        const contactList = document.getElementById('mgmtEmailList');
        if (!contactList) return;

        if (contacts.length === 0) {
            contactList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-envelope"></i>
                    <h3>No Email Contacts</h3>
                    <p>Add contacts to receive email notifications</p>
                </div>
            `;
            return;
        }

        let html = '';
        contacts.forEach(contact => {
            const statusClass = contact.is_active ? 'success' : 'danger';
            const statusText = contact.is_active ? 'Active' : 'Inactive';

            html += `
                <div class="management-list-item">
                    <div class="management-item-info">
                        <h4>${contact.full_name}</h4>
                        <p>${contact.email_address}</p>
                        <span class="management-badge management-badge-${statusClass}">${statusText}</span>
                    </div>
                    <div class="management-item-actions">
                        <button class="management-btn management-btn-primary management-btn-sm" onclick="editMgmtEmail(${contact.id}, '${contact.full_name}', '${contact.email_address}')" title="Edit Email Contact">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="management-btn management-btn-${contact.is_active ? 'warning' : 'success'} management-btn-sm" onclick="toggleMgmtEmail(${contact.id}, ${contact.is_active})" title="Toggle Active/Inactive">
                            <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}"></i> ${contact.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button class="management-btn management-btn-secondary management-btn-sm" onclick="deleteMgmtEmail(${contact.id})" title="Delete Email Contact">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        contactList.innerHTML = html;
    } catch (error) {
        console.error('Error loading email contacts:', error);
    }
}

// Management Alert Functions
async function handleMgmtAlertSubmit(e) {
    e.preventDefault();

    const startTime = document.getElementById('mgmtStartTime').value;
    const endTime = document.getElementById('mgmtEndTime').value;
    const alertFrequency = document.getElementById('mgmtAlertFrequency').value;

    // Get selected days
    const selectedDays = [];
    document.querySelectorAll('.management-day-btn.active').forEach(btn => {
        selectedDays.push(btn.getAttribute('data-day'));
    });

    try {
        const response = await fetch('/face_recognition/alert-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                alert_frequency: parseInt(alertFrequency),
                active_days: selectedDays
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Alert settings saved successfully!', 'success');
        } else {
            showNotification(result.detail || 'Failed to save alert settings', 'error');
        }
    } catch (error) {
        console.error('Error saving alert settings:', error);
        showNotification('Error saving alert settings: ' + error.message, 'error');
    }
}

async function startMgmtAlertSystem() {
    try {
        const response = await fetch('/face_recognition/start-alerts', {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Alert system started successfully!', 'success');
            updateMgmtAlertStatus(true);
        } else {
            showNotification(result.detail || 'Failed to start alert system', 'error');
        }
    } catch (error) {
        console.error('Error starting alert system:', error);
        showNotification('Error starting alert system: ' + error.message, 'error');
    }
}

async function stopMgmtAlertSystem() {
    try {
        const response = await fetch('/face_recognition/stop-alerts', {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Alert system stopped successfully!', 'success');
            updateMgmtAlertStatus(false);
        } else {
            showNotification(result.detail || 'Failed to stop alert system', 'error');
        }
    } catch (error) {
        console.error('Error stopping alert system:', error);
        showNotification('Error stopping alert system: ' + error.message, 'error');
    }
}

function updateMgmtAlertStatus(isActive) {
    const statusText = document.getElementById('mgmtAlertStatusText');
    const statusBadge = document.getElementById('mgmtSystemStatusBadge');
    const statusIndicator = document.querySelector('.management-alert-status-indicator');

    if (statusText) {
        statusText.textContent = isActive ? 'System Active' : 'System Inactive';
    }

    if (statusBadge) {
        statusBadge.textContent = isActive ? 'ACTIVE' : 'INACTIVE';
        statusBadge.className = `management-alert-status-badge ${isActive ? 'active' : 'inactive'}`;
    }

    if (statusIndicator) {
        statusIndicator.style.background = isActive ? 'var(--mgmt-success)' : 'var(--mgmt-danger)';
    }
}

async function loadMgmtAlertSettings() {
    try {
        const response = await fetch('/face_recognition/alert-settings');
        const settings = await response.json();

        if (settings) {
            // Populate form fields
            if (settings.start_time) {
                document.getElementById('mgmtStartTime').value = settings.start_time;
            }
            if (settings.end_time) {
                document.getElementById('mgmtEndTime').value = settings.end_time;
            }
            if (settings.alert_frequency) {
                document.getElementById('mgmtAlertFrequency').value = settings.alert_frequency;
            }

            // Set active days
            if (settings.active_days) {
                document.querySelectorAll('.management-day-btn').forEach(btn => {
                    const day = btn.getAttribute('data-day');
                    if (settings.active_days.includes(day)) {
                        btn.classList.add('active');
                    }
                });
            }

            // Update status
            updateMgmtAlertStatus(settings.is_active || false);
        }
    } catch (error) {
        console.error('Error loading alert settings:', error);
    }
}

// Global Management Functions - Exactly like crowd_detection
// Edit Camera Function
window.editMgmtCamera = async function(cameraName, currentRtspUrl) {
    console.log(`Edit camera called with name: "${cameraName}"`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit Camera</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editCameraForm">
                    <div class="management-form-group">
                        <label for="editCameraName">Camera Name</label>
                        <input type="text" id="editCameraName" value="${cameraName}" class="management-form-input" required>
                        <small>Enter a unique name for the camera</small>
                    </div>
                    <div class="management-form-group">
                        <label for="editRtspUrl">RTSP URL</label>
                        <input type="text" id="editRtspUrl" value="${currentRtspUrl}" class="management-form-input" required>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveCameraEdit('${cameraName}')">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save Camera Edit Function
window.saveCameraEdit = async function(originalCameraName) {
    const newCameraName = document.getElementById('editCameraName').value.trim();
    const newRtspUrl = document.getElementById('editRtspUrl').value.trim();

    if (!newCameraName || !newRtspUrl) {
        showNotification('Camera name and RTSP URL are required', 'error');
        return;
    }

    // Debug: Log the camera name being validated
    console.log('Validating camera name:', JSON.stringify(newCameraName));

    // Validate camera name (allow most common characters, but prevent problematic ones)
    if (/[<>:"\/\\|?*]/.test(newCameraName)) {
        console.log('Camera name validation failed for:', JSON.stringify(newCameraName));
        showNotification('Camera name cannot contain these characters: < > : " / \\ | ? *', 'error');
        return;
    }

    // Check for minimum length
    if (newCameraName.length < 1) {
        showNotification('Camera name cannot be empty', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/update-camera/${encodeURIComponent(originalCameraName)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newCameraName,
                rtsp_url: newRtspUrl
            })
        });

        if (!response.ok) {
            const errorResult = await response.json();
            console.error('Camera update HTTP error:', response.status, errorResult);
            showNotification(errorResult.detail || errorResult.message || 'Failed to update camera', 'error');
            return;
        }

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Camera updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtCameras();
        } else {
            console.error('Camera update failed:', result);
            showNotification(result.message || result.detail || 'Failed to update camera', 'error');
        }
    } catch (error) {
        console.error('Error updating camera:', error);
        showNotification(`Error updating camera: ${error.message}`, 'error');
    }
};

// Toggle Camera Active/Inactive Function
window.toggleMgmtCamera = async function(cameraName, isCurrentlyActive) {
    console.log(`Toggle camera called with name: "${cameraName}", currently active: ${isCurrentlyActive}`);

    const action = isCurrentlyActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} camera "${cameraName}"?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/toggle-camera/${encodeURIComponent(cameraName)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                active: !isCurrentlyActive
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            const statusText = !isCurrentlyActive ? 'activated' : 'deactivated';
            showNotification(`Camera ${statusText} successfully!`, 'success');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to toggle camera status', 'error');
        }
    } catch (error) {
        console.error('Error toggling camera status:', error);
        showNotification(`Error toggling camera status: ${error.message}`, 'error');
    }
};

// Add Test Camera Function
window.addTestCamera = async function() {
    try {
        const response = await fetch('/face_recognition/add-test-camera', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Test camera added successfully!', 'success');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to add test camera', 'error');
        }
    } catch (error) {
        console.error('Error adding test camera:', error);
        showNotification(`Error adding test camera: ${error.message}`, 'error');
    }
};

// Add Test Webhook Function
window.addTestWebhook = async function() {
    try {
        const response = await fetch('/face_recognition/add-test-webhook', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Test webhook added successfully!', 'success');
            loadMgmtWebhooks();
        } else {
            showNotification(result.message || 'Failed to add test webhook', 'error');
        }
    } catch (error) {
        console.error('Error adding test webhook:', error);
        showNotification(`Error adding test webhook: ${error.message}`, 'error');
    }
};

// Delete Camera Function
window.deleteMgmtCamera = async function(cameraName) {
    console.log(`Delete camera called with name: "${cameraName}"`);

    // Show a more explicit warning about permanent deletion (exactly like crowd_detection)
    if (!confirm(`⚠️ WARNING: This will permanently delete camera "${cameraName}" from the database.\n\nThis action cannot be undone. Are you sure you want to continue?`)) {
        console.log('User cancelled deletion');
        return;
    }

    try {
        const url = `/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`;
        console.log(`Making DELETE request to: ${url}`);

        const response = await fetch(url, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Delete response:', result);

        if (result.status === 'success') {
            showNotification('Camera permanently deleted from database!');
            console.log('Reloading cameras after successful deletion');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to delete camera', 'error');
        }
    } catch (error) {
        console.error('Error deleting camera:', error);
        showNotification(`Error deleting camera: ${error.message}`, 'error');
    }
};

// Edit Webhook Function
window.editMgmtWebhook = async function(webhookId, currentUrl, currentMethod, currentDescription) {
    console.log(`Edit webhook called with ID: ${webhookId}`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit Webhook</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editWebhookForm">
                    <div class="management-form-group">
                        <label for="editWebhookUrl">Webhook URL</label>
                        <input type="url" id="editWebhookUrl" value="${currentUrl}" class="management-form-input" required>
                    </div>
                    <div class="management-form-group">
                        <label for="editWebhookMethod">HTTP Method</label>
                        <select id="editWebhookMethod" class="management-form-input">
                            <option value="POST" ${currentMethod === 'POST' ? 'selected' : ''}>POST</option>
                            <option value="PUT" ${currentMethod === 'PUT' ? 'selected' : ''}>PUT</option>
                            <option value="PATCH" ${currentMethod === 'PATCH' ? 'selected' : ''}>PATCH</option>
                        </select>
                    </div>
                    <div class="management-form-group">
                        <label for="editWebhookDescription">Description</label>
                        <textarea id="editWebhookDescription" class="management-form-input" rows="3">${currentDescription}</textarea>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveWebhookEdit(${webhookId})">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save Webhook Edit Function
window.saveWebhookEdit = async function(webhookId) {
    const newUrl = document.getElementById('editWebhookUrl').value.trim();
    const newMethod = document.getElementById('editWebhookMethod').value;
    const newDescription = document.getElementById('editWebhookDescription').value.trim();

    if (!newUrl) {
        showNotification('Webhook URL is required', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                url: newUrl,
                http_method: newMethod,
                description: newDescription
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Webhook updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtWebhooks();
        } else {
            showNotification(result.detail || 'Failed to update webhook', 'error');
        }
    } catch (error) {
        console.error('Error updating webhook:', error);
        showNotification(`Error updating webhook: ${error.message}`, 'error');
    }
};

window.toggleMgmtWebhook = async function(webhookId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} this webhook?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/webhooks/${webhookId}/toggle`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            }
            // No body needed - backend automatically toggles the status
        });

        if (response.ok) {
            const result = await response.json();
            showNotification(result.message || 'Webhook status updated!', 'success');
            loadMgmtWebhooks();
        } else {
            const result = await response.json();
            showNotification(result.detail || 'Failed to toggle webhook status', 'error');
        }
    } catch (error) {
        console.error('Error toggling webhook:', error);
        showNotification('Error toggling webhook status: ' + error.message, 'error');
    }
};

window.deleteMgmtWebhook = async function(webhookId) {
    if (!confirm('Are you sure you want to delete this webhook?')) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('Webhook deleted successfully!', 'success');
            loadMgmtWebhooks();
        } else {
            const result = await response.json();
            showNotification(result.detail || 'Failed to delete webhook', 'error');
        }
    } catch (error) {
        console.error('Error deleting webhook:', error);
        showNotification('Error deleting webhook: ' + error.message, 'error');
    }
};

// Edit WhatsApp Contact Function
window.editMgmtWhatsApp = async function(contactId, currentName, currentPhone) {
    console.log(`Edit WhatsApp contact called with ID: ${contactId}`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit WhatsApp Contact</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editWhatsAppForm">
                    <div class="management-form-group">
                        <label for="editWhatsAppName">Full Name</label>
                        <input type="text" id="editWhatsAppName" value="${currentName}" class="management-form-input" required>
                    </div>
                    <div class="management-form-group">
                        <label for="editWhatsAppPhone">Phone Number</label>
                        <input type="tel" id="editWhatsAppPhone" value="${currentPhone}" class="management-form-input" required>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveWhatsAppEdit(${contactId})">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save WhatsApp Edit Function
window.saveWhatsAppEdit = async function(contactId) {
    const newName = document.getElementById('editWhatsAppName').value.trim();
    const newPhone = document.getElementById('editWhatsAppPhone').value.trim();

    if (!newName || !newPhone) {
        showNotification('Name and phone number are required', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/whatsapp/${contactId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: newName,
                phone_number: newPhone
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('WhatsApp contact updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtWhatsAppContacts();
        } else {
            showNotification(result.detail || 'Failed to update WhatsApp contact', 'error');
        }
    } catch (error) {
        console.error('Error updating WhatsApp contact:', error);
        showNotification(`Error updating WhatsApp contact: ${error.message}`, 'error');
    }
};

// Toggle WhatsApp Contact Function
window.toggleMgmtWhatsApp = async function(contactId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} this WhatsApp contact?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/whatsapp/${contactId}/toggle`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: !isActive
            })
        });

        const result = await response.json();

        if (response.ok) {
            const statusText = !isActive ? 'activated' : 'deactivated';
            showNotification(`WhatsApp contact ${statusText} successfully!`, 'success');
            loadMgmtWhatsAppContacts();
        } else {
            showNotification(result.detail || 'Failed to toggle WhatsApp contact status', 'error');
        }
    } catch (error) {
        console.error('Error toggling WhatsApp contact status:', error);
        showNotification(`Error toggling WhatsApp contact status: ${error.message}`, 'error');
    }
};

// Delete WhatsApp Contact Function
window.deleteMgmtWhatsApp = async function(contactId) {
    if (!confirm('Are you sure you want to delete this WhatsApp contact?')) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/whatsapp/${contactId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('WhatsApp contact deleted successfully!', 'success');
            loadMgmtWhatsAppContacts();
        } else {
            const result = await response.json();
            showNotification(result.detail || 'Failed to delete WhatsApp contact', 'error');
        }
    } catch (error) {
        console.error('Error deleting WhatsApp contact:', error);
        showNotification('Error deleting WhatsApp contact: ' + error.message, 'error');
    }
};

// Edit SMS Contact Function
window.editMgmtSMS = async function(contactId, currentName, currentPhone) {
    console.log(`Edit SMS contact called with ID: ${contactId}`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit SMS Contact</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editSMSForm">
                    <div class="management-form-group">
                        <label for="editSMSName">Full Name</label>
                        <input type="text" id="editSMSName" value="${currentName}" class="management-form-input" required>
                    </div>
                    <div class="management-form-group">
                        <label for="editSMSPhone">Phone Number</label>
                        <input type="tel" id="editSMSPhone" value="${currentPhone}" class="management-form-input" required>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveSMSEdit(${contactId})">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save SMS Edit Function
window.saveSMSEdit = async function(contactId) {
    const newName = document.getElementById('editSMSName').value.trim();
    const newPhone = document.getElementById('editSMSPhone').value.trim();

    if (!newName || !newPhone) {
        showNotification('Name and phone number are required', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/sms/${contactId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: newName,
                phone_number: newPhone
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('SMS contact updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtSMSContacts();
        } else {
            showNotification(result.detail || 'Failed to update SMS contact', 'error');
        }
    } catch (error) {
        console.error('Error updating SMS contact:', error);
        showNotification(`Error updating SMS contact: ${error.message}`, 'error');
    }
};

// Toggle SMS Contact Function
window.toggleMgmtSMS = async function(contactId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} this SMS contact?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/sms/${contactId}/toggle`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: !isActive
            })
        });

        const result = await response.json();

        if (response.ok) {
            const statusText = !isActive ? 'activated' : 'deactivated';
            showNotification(`SMS contact ${statusText} successfully!`, 'success');
            loadMgmtSMSContacts();
        } else {
            showNotification(result.detail || 'Failed to toggle SMS contact status', 'error');
        }
    } catch (error) {
        console.error('Error toggling SMS contact status:', error);
        showNotification(`Error toggling SMS contact status: ${error.message}`, 'error');
    }
};

// Delete SMS Contact Function
window.deleteMgmtSMS = async function(contactId) {
    if (!confirm('Are you sure you want to delete this SMS contact?')) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/sms/${contactId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('SMS contact deleted successfully!', 'success');
            loadMgmtSMSContacts();
        } else {
            const result = await response.json();
            showNotification(result.detail || 'Failed to delete SMS contact', 'error');
        }
    } catch (error) {
        console.error('Error deleting SMS contact:', error);
        showNotification('Error deleting SMS contact: ' + error.message, 'error');
    }
};

// Edit Email Contact Function
window.editMgmtEmail = async function(contactId, currentName, currentEmail) {
    console.log(`Edit email contact called with ID: ${contactId}`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit Email Contact</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editEmailForm">
                    <div class="management-form-group">
                        <label for="editEmailName">Full Name</label>
                        <input type="text" id="editEmailName" value="${currentName}" class="management-form-input" required>
                    </div>
                    <div class="management-form-group">
                        <label for="editEmailAddress">Email Address</label>
                        <input type="email" id="editEmailAddress" value="${currentEmail}" class="management-form-input" required>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveEmailEdit(${contactId})">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save Email Edit Function
window.saveEmailEdit = async function(contactId) {
    const newName = document.getElementById('editEmailName').value.trim();
    const newEmail = document.getElementById('editEmailAddress').value.trim();

    if (!newName || !newEmail) {
        showNotification('Name and email address are required', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/email/${contactId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: newName,
                email_address: newEmail
            })
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Email contact updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtEmailContacts();
        } else {
            showNotification(result.detail || 'Failed to update email contact', 'error');
        }
    } catch (error) {
        console.error('Error updating email contact:', error);
        showNotification(`Error updating email contact: ${error.message}`, 'error');
    }
};

// Toggle Email Contact Function
window.toggleMgmtEmail = async function(contactId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} this email contact?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/email/${contactId}/toggle`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: !isActive
            })
        });

        const result = await response.json();

        if (response.ok) {
            const statusText = !isActive ? 'activated' : 'deactivated';
            showNotification(`Email contact ${statusText} successfully!`, 'success');
            loadMgmtEmailContacts();
        } else {
            showNotification(result.detail || 'Failed to toggle email contact status', 'error');
        }
    } catch (error) {
        console.error('Error toggling email contact status:', error);
        showNotification(`Error toggling email contact status: ${error.message}`, 'error');
    }
};

// Delete Email Contact Function
window.deleteMgmtEmail = async function(contactId) {
    if (!confirm('Are you sure you want to delete this email contact?')) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/email/${contactId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('Email contact deleted successfully!', 'success');
            loadMgmtEmailContacts();
        } else {
            const result = await response.json();
            showNotification(result.detail || 'Failed to delete email contact', 'error');
        }
    } catch (error) {
        console.error('Error deleting email contact:', error);
        showNotification('Error deleting email contact: ' + error.message, 'error');
    }
};

window.mgmtToggleDayButton = function(button) {
    button.classList.toggle('active');
};

// Exit management mode function
window.exitManagementMode = function() {
    // Remove management active state
    document.body.classList.remove('management-active');

    // Remove active class from management tab
    const managementTab = document.getElementById('management-tab');
    const managementBtn = document.querySelector('[data-tab="management"]');

    if (managementTab) {
        managementTab.classList.remove('active');
    }

    if (managementBtn) {
        managementBtn.classList.remove('active');
    }

    // Activate the first non-management tab (Unknown Persons)
    const firstTab = document.querySelector('.tab-btn:not([data-tab="management"])');
    if (firstTab) {
        firstTab.click();
    }
};

// ==================== OTHER SERVICE FUNCTIONALITY ====================

// Initialize Other Service functionality
function initializeOtherService() {
    console.log('Initializing Other Service...');

    // Get Other service sub-navigation buttons
    const otherSubNavButtons = document.querySelectorAll('.other-sub-nav-btn');

    // Add click event listeners to sub-navigation buttons
    otherSubNavButtons.forEach(button => {
        button.addEventListener('click', function() {
            const service = this.getAttribute('data-other-service');
            console.log('Other service clicked:', service);

            // Update active state
            otherSubNavButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Load the corresponding content
            loadOtherServiceContent(service);
        });
    });

    // Load default content (camera management) when Other tab is activated
    const otherTab = document.querySelector('[data-tab="other"]');
    if (otherTab) {
        otherTab.addEventListener('click', function() {
            // Small delay to ensure tab is activated
            setTimeout(() => {
                loadOtherServiceContent('camera-management');
            }, 100);
        });
    }
}

// Load content for Other service based on selected service
function loadOtherServiceContent(serviceName) {
    console.log('Loading Other service content for:', serviceName);

    const contentArea = document.getElementById('otherContentArea');
    if (!contentArea) {
        console.error('Other content area not found');
        return;
    }

    // Get the corresponding management section content
    const managementTab = document.getElementById('management-tab');
    if (!managementTab) {
        console.error('Management tab not found');
        return;
    }

    let sourceSection;

    // Map service names to management section IDs
    switch(serviceName) {
        case 'camera-management':
            sourceSection = managementTab.querySelector('#mgmtCameraManagementSection');
            break;
        case 'webhook-management':
            sourceSection = managementTab.querySelector('#mgmtWebhookManagementSection');
            break;
        case 'whatsapp-management':
            sourceSection = managementTab.querySelector('#mgmtWhatsappManagementSection');
            break;
        case 'sms-management':
            sourceSection = managementTab.querySelector('#mgmtSmsManagementSection');
            break;
        case 'email-management':
            sourceSection = managementTab.querySelector('#mgmtEmailManagementSection');
            break;
        case 'alert-management':
            sourceSection = managementTab.querySelector('#mgmtAlertManagementSection');
            break;
        default:
            console.error('Unknown service:', serviceName);
            return;
    }

    if (!sourceSection) {
        console.error('Source section not found for:', serviceName);
        return;
    }

    // Clear the content area
    contentArea.innerHTML = '';

    // Clone the management section content
    const clonedContent = sourceSection.cloneNode(true);

    // Update IDs to avoid conflicts (prefix with 'other-')
    updateElementIds(clonedContent, 'other-');

    // Append the cloned content
    contentArea.appendChild(clonedContent);

    // Initialize the specific service functionality
    initializeOtherServiceModule(serviceName);

    console.log('Other service content loaded for:', serviceName);
}

// Update element IDs to avoid conflicts
function updateElementIds(element, prefix) {
    // Update the element's ID if it has one
    if (element.id) {
        element.id = prefix + element.id;
    }

    // Update all child elements' IDs
    const elementsWithIds = element.querySelectorAll('[id]');
    elementsWithIds.forEach(el => {
        if (el.id && !el.id.startsWith(prefix)) {
            el.id = prefix + el.id;
        }
    });

    // Update form references (for attributes)
    const elementsWithFor = element.querySelectorAll('[for]');
    elementsWithFor.forEach(el => {
        if (el.getAttribute('for') && !el.getAttribute('for').startsWith(prefix)) {
            el.setAttribute('for', prefix + el.getAttribute('for'));
        }
    });
}

// Initialize specific service modules for Other service
function initializeOtherServiceModule(serviceName) {
    console.log('Initializing Other service module:', serviceName);

    switch(serviceName) {
        case 'camera-management':
            initializeOtherCameraModule();
            break;
        case 'webhook-management':
            initializeOtherWebhookModule();
            break;
        case 'whatsapp-management':
            initializeOtherWhatsAppModule();
            break;
        case 'sms-management':
            initializeOtherSMSModule();
            break;
        case 'email-management':
            initializeOtherEmailModule();
            break;
        case 'alert-management':
            initializeOtherAlertModule();
            break;
    }
}

// Initialize Other Camera Management Module
function initializeOtherCameraModule() {
    const cameraForm = document.getElementById('other-mgmtCameraForm');
    const refreshBtn = document.getElementById('other-mgmtRefreshCamerasBtn');

    if (cameraForm) {
        cameraForm.addEventListener('submit', handleMgmtCameraSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtCameras);
    }

    // Load cameras on initialization
    loadMgmtCameras();
}

// Initialize Other Webhook Management Module
function initializeOtherWebhookModule() {
    const webhookForm = document.getElementById('other-mgmtWebhookForm');
    const refreshBtn = document.getElementById('other-mgmtRefreshWebhooksBtn');

    if (webhookForm) {
        webhookForm.addEventListener('submit', handleMgmtWebhookSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtWebhooks);
    }

    // Load webhooks on initialization
    loadMgmtWebhooks();
}

// Initialize Other WhatsApp Management Module
function initializeOtherWhatsAppModule() {
    const whatsappForm = document.getElementById('other-mgmtWhatsappForm');
    const refreshBtn = document.getElementById('other-mgmtRefreshWhatsAppBtn');

    if (whatsappForm) {
        whatsappForm.addEventListener('submit', handleMgmtWhatsAppSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtWhatsAppContacts);
    }

    // Load contacts on initialization
    loadMgmtWhatsAppContacts();
}

// Initialize Other SMS Management Module
function initializeOtherSMSModule() {
    const smsForm = document.getElementById('other-mgmtSmsForm');
    const refreshBtn = document.getElementById('other-mgmtRefreshSMSBtn');

    if (smsForm) {
        smsForm.addEventListener('submit', handleMgmtSMSSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtSMSContacts);
    }

    // Load contacts on initialization
    loadMgmtSMSContacts();
}

// Initialize Other Email Management Module
function initializeOtherEmailModule() {
    const emailForm = document.getElementById('other-mgmtEmailForm');
    const refreshBtn = document.getElementById('other-mgmtRefreshEmailBtn');

    if (emailForm) {
        emailForm.addEventListener('submit', handleMgmtEmailSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtEmailContacts);
    }

    // Load contacts on initialization
    loadMgmtEmailContacts();
}

// Initialize Other Alert Management Module
function initializeOtherAlertModule() {
    const startBtn = document.getElementById('other-mgmtStartAlertBtn');
    const stopBtn = document.getElementById('other-mgmtStopAlertBtn');
    const configForm = document.getElementById('other-mgmtAlertConfigForm');

    if (startBtn) startBtn.addEventListener('click', startAlertSystem);
    if (stopBtn) stopBtn.addEventListener('click', stopAlertSystem);
    if (configForm) configForm.addEventListener('submit', saveAlertConfig);

    loadAlertStatus();
    loadAlertConfig();
}
