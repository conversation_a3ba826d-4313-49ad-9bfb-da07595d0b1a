# WhatsApp Management Service

This folder contains all code related to WhatsApp contact management functionality.

## Files Structure:
- **html/** - HTML templates for WhatsApp management UI
- **css/** - CSS styles for WhatsApp management components
- **js/** - JavaScript functions for WhatsApp operations
- **backend/** - Backend API endpoints for WhatsApp CRUD operations

## Functionality:
- Add new WhatsApp contacts with phone numbers
- Edit existing WhatsApp contact information
- Delete WhatsApp contacts
- Toggle contact active/inactive status
- List all configured WhatsApp contacts
- Phone number validation and formatting
- WhatsApp-specific styling with green theme

## API Endpoints:
- POST /whatsapp-contacts - Create new WhatsApp contact
- GET /whatsapp-contacts - Get all WhatsApp contacts
- GET /whatsapp-contacts/{id} - Get specific WhatsApp contact
- PUT /whatsapp-contacts/{id} - Update WhatsApp contact
- DELETE /whatsapp-contacts/{id} - Delete WhatsApp contact
- PUT /whatsapp-contacts/{id}/toggle - Toggle contact status

## Database Models:
- WhatsAppContact model with phone_number, name, is_active fields
- Phone number validation and international format support

## Features:
- International phone number format support
- WhatsApp green color scheme integration
- Contact validation and duplicate prevention
- Professional contact cards with action buttons
