/* Premium Admin Panel CSS */
:root {
  --primary-color: #4f46e5;
  --primary-dark: #3730a3;
  --primary-light: #6366f1;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --border-radius: 12px;
  --border-radius-lg: 16px;
}

/* Tab Button Styling */
.tab-btn {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  color: var(--text-secondary);
  padding: 16px 24px;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.tab-btn:hover::before {
  left: 100%;
}

.tab-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.tab-btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-lg);
}

.tab-btn i {
  font-size: 16px;
}

/* Tab Pane */
.tab-pane {
  display: none;
  padding: 32px;
  min-height: 600px;
  background: var(--background-color);
}

.tab-pane.active {
  display: block;
  animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Section Header */
.section-header {
  margin-bottom: 32px;
  text-align: center;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.section-title i {
  color: var(--primary-color);
  font-size: 24px;
}

.section-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

/* Unknown Persons Grid */
.unknown-persons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  padding: 0;
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  grid-column: 1 / -1;
}

.loading-spinner {
  text-align: center;
  color: var(--text-secondary);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Person Card */
.person-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.person-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.person-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.person-card:hover::before {
  opacity: 1;
}

.person-image-container {
  position: relative;
  margin-bottom: 20px;
  text-align: center;
}

.person-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-color);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.person-card:hover .person-image {
  transform: scale(1.05);
}

.person-count {
  position: absolute;
  top: -8px;
  right: calc(50% - 80px);
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.person-info {
  text-align: center;
}

.person-info h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.person-info p {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

.person-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Attendance Records Section */
.attendance-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 2px solid var(--border-color);
}

.attendance-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--primary-color);
  font-weight: 600;
}

.attendance-table {
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.attendance-table th {
  background: linear-gradient(135deg, var(--text-primary), #374151);
  color: white;
  padding: 12px 16px;
  font-weight: 600;
  text-align: left;
}

.attendance-table td {
  background: var(--surface-color);
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Registration Options */
.registration-options {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.option-btn {
  flex: 1;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--surface-color);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.option-btn:hover {
  border-color: var(--primary-color);
  background: #f0f4ff;
}

.option-btn.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.option-btn i {
  display: block;
  font-size: 20px;
  margin-bottom: 8px;
}

/* Enhanced Form Styles */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: all 0.3s ease;
  background: var(--surface-color);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.profile-preview-section {
  text-align: center;
  margin-bottom: 32px;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.profile-preview-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-color);
  box-shadow: var(--shadow-lg);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  border-radius: 50%;
  box-shadow: var(--shadow-md);
}

/* Enhanced Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.enhanced-modal {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  position: relative;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--danger-color);
  color: white;
}

.modal-body {
  padding: 0 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 24px;
  border-top: 2px solid var(--border-color);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.modal-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  text-align: center;
}

/* Clusters Grid */
.clusters-grid {
  display: grid;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.cluster-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.cluster-item:hover {
  border-color: var(--primary-color);
  background: #f0f4ff;
}

.cluster-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cluster-name {
  font-weight: 600;
  color: var(--text-primary);
}

.cluster-id {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--text-secondary);
  color: var(--text-primary);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 40px;
  color: var(--text-secondary);
  grid-column: 1 / -1;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 24px;
  color: var(--primary-color);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .unknown-persons-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .person-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .tab-pane {
    padding: 16px;
  }
}