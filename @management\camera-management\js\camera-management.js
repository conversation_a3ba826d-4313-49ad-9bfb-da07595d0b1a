// Camera Management Module Initialization
function initializeManagementCameraModule() {
    const cameraForm = document.getElementById('mgmtCameraForm');
    const refreshBtn = document.getElementById('mgmtRefreshCamerasBtn');

    if (cameraForm) {
        cameraForm.addEventListener('submit', handleMgmtCameraSubmit);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMgmtCameras);
    }

    // Load cameras on initialization
    loadMgmtCameras();
}

// Handle Camera Form Submit
async function handleMgmtCameraSubmit(e) {
    e.preventDefault();

    const submitBtn = e.target.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    }

    const cameraName = document.getElementById('mgmtCameraName').value;
    const rtspUrl = document.getElementById('mgmtRtspUrl').value;

    try {
        const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cameraName: cameraName,
                rtspUrl: rtspUrl
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Camera added successfully!');
            document.getElementById('mgmtCameraForm').reset();
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to add camera', 'error');
        }
    } catch (error) {
        console.error('Error adding camera:', error);
        showNotification('Error adding camera: ' + error.message, 'error');
    } finally {
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Camera';
        }
    }
}

// Load Cameras from API
async function loadMgmtCameras() {
    try {
        console.log('Loading cameras...');
        const response = await fetch('/face_recognition/get-cameras');
        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        console.log('Cameras loaded:', cameras);

        const cameraList = document.getElementById('mgmtCameraList');
        if (!cameraList) {
            console.error('Camera list element not found!');
            return;
        }

        if (Object.keys(cameras).length === 0) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-video"></i>
                    <h3>No Cameras Found</h3>
                    <p>Add your first camera to start monitoring</p>
                </div>
            `;
            return;
        }

        let html = '';
        Object.entries(cameras).forEach(([name, cameraData]) => {
            // Handle both old format (array) and new format (object)
            const rtspUrl = Array.isArray(cameraData) ? (cameraData[0] || 'No URL') : (cameraData.rtsp_url || 'No URL');
            const isActive = Array.isArray(cameraData) ? true : (cameraData.is_active !== undefined ? cameraData.is_active : true);
            const statusClass = isActive ? 'success' : 'danger';
            const statusText = isActive ? 'Active' : 'Inactive';

            html += `
                <div class="management-camera-card">
                    <div class="management-camera-card-header">
                        <div class="management-camera-info">
                            <h4 class="management-camera-name">${name}</h4>
                            <p class="management-camera-url">${rtspUrl}</p>
                        </div>
                        <div class="management-camera-status">
                            <span class="management-status-badge management-status-${statusClass}">${statusText}</span>
                        </div>
                    </div>
                    <div class="management-camera-card-body">
                        <div class="management-camera-actions">
                            <button class="management-action-btn management-action-edit" onclick="editMgmtCamera('${name}', '${rtspUrl}')" title="Edit Camera">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                            <button class="management-action-btn management-action-${isActive ? 'deactivate' : 'activate'}" onclick="toggleMgmtCamera('${name}', ${isActive})" title="Toggle Active/Inactive">
                                <i class="fas ${isActive ? 'fa-pause' : 'fa-play'}"></i>
                                <span>${isActive ? 'Deactivate' : 'Activate'}</span>
                            </button>
                            <button class="management-action-btn management-action-delete" onclick="deleteMgmtCamera('${name}')" title="Delete Camera">
                                <i class="fas fa-trash"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        // Handle empty state
        if (Object.keys(cameras).length === 0) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-video" style="color: #ccc; font-size: 48px; margin-bottom: 16px;"></i>
                    <h3 style="color: #666;">No cameras configured</h3>
                    <p style="color: #999;">Add a camera to get started with face recognition</p>
                    <button class="management-btn management-btn-primary" onclick="addTestCamera()" style="margin-top: 16px;">
                        <i class="fas fa-plus"></i> Add Test Camera
                    </button>
                </div>
            `;
        } else {
            cameraList.innerHTML = html;
        }

        // Update stats
        const totalElement = document.getElementById('mgmtTotalCameras');
        const activeElement = document.getElementById('mgmtActiveCameras');

        const totalCameras = Object.keys(cameras).length;
        const activeCameras = Object.values(cameras).filter(cameraData => {
            // Handle both old format (array) and new format (object)
            return Array.isArray(cameraData) ? true : (cameraData.is_active !== undefined ? cameraData.is_active : true);
        }).length;

        if (totalElement) totalElement.textContent = totalCameras;
        if (activeElement) activeElement.textContent = activeCameras;

        console.log('Camera list updated with HTML:', html);
        console.log('Camera data received:', cameras);
    } catch (error) {
        console.error('Error loading cameras:', error);
        const cameraList = document.getElementById('mgmtCameraList');
        if (cameraList) {
            cameraList.innerHTML = `
                <div class="management-empty-state">
                    <i class="fas fa-exclamation-triangle" style="color: var(--danger);"></i>
                    <h3 style="color: var(--danger);">Error Loading Cameras</h3>
                    <p style="color: var(--danger);">${error.message}</p>
                </div>
            `;
        }
    }
}

// Edit Camera Function
window.editMgmtCamera = async function(cameraName, currentRtspUrl) {
    console.log(`Edit camera called with name: "${cameraName}"`);

    // Create edit form modal
    const modal = document.createElement('div');
    modal.className = 'management-modal';
    modal.innerHTML = `
        <div class="management-modal-content">
            <div class="management-modal-header">
                <h3><i class="fas fa-edit"></i> Edit Camera</h3>
                <button class="management-modal-close" onclick="this.closest('.management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="management-modal-body">
                <form id="editCameraForm">
                    <div class="management-form-group">
                        <label for="editCameraName">Camera Name</label>
                        <input type="text" id="editCameraName" value="${cameraName}" class="management-form-input" required>
                        <small>Enter a unique name for the camera</small>
                    </div>
                    <div class="management-form-group">
                        <label for="editRtspUrl">RTSP URL</label>
                        <input type="text" id="editRtspUrl" value="${currentRtspUrl}" class="management-form-input" required>
                    </div>
                </form>
            </div>
            <div class="management-modal-footer">
                <button class="management-btn management-btn-secondary" onclick="this.closest('.management-modal').remove()">Cancel</button>
                <button class="management-btn management-btn-primary" onclick="saveCameraEdit('${cameraName}')">Save Changes</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
};

// Save Camera Edit Function
window.saveCameraEdit = async function(originalCameraName) {
    const newCameraName = document.getElementById('editCameraName').value.trim();
    const newRtspUrl = document.getElementById('editRtspUrl').value.trim();

    if (!newCameraName || !newRtspUrl) {
        showNotification('Camera name and RTSP URL are required', 'error');
        return;
    }

    // Debug: Log the camera name being validated
    console.log('Validating camera name:', JSON.stringify(newCameraName));

    // Validate camera name (allow most common characters, but prevent problematic ones)
    if (/[<>:"\/\\|?*]/.test(newCameraName)) {
        console.log('Camera name validation failed for:', JSON.stringify(newCameraName));
        showNotification('Camera name cannot contain these characters: < > : " / \\ | ? *', 'error');
        return;
    }

    // Check for minimum length
    if (newCameraName.length < 1) {
        showNotification('Camera name cannot be empty', 'error');
        return;
    }

    try {
        const response = await fetch(`/face_recognition/update-camera/${encodeURIComponent(originalCameraName)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newCameraName,
                rtsp_url: newRtspUrl
            })
        });

        if (!response.ok) {
            const errorResult = await response.json();
            console.error('Camera update HTTP error:', response.status, errorResult);
            showNotification(errorResult.detail || errorResult.message || 'Failed to update camera', 'error');
            return;
        }

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Camera updated successfully!', 'success');
            document.querySelector('.management-modal').remove();
            loadMgmtCameras();
        } else {
            console.error('Camera update failed:', result);
            showNotification(result.message || result.detail || 'Failed to update camera', 'error');
        }
    } catch (error) {
        console.error('Error updating camera:', error);
        showNotification(`Error updating camera: ${error.message}`, 'error');
    }
};

// Toggle Camera Active/Inactive Function
window.toggleMgmtCamera = async function(cameraName, isCurrentlyActive) {
    console.log(`Toggle camera called with name: "${cameraName}", currently active: ${isCurrentlyActive}`);

    const action = isCurrentlyActive ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} camera "${cameraName}"?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const response = await fetch(`/face_recognition/toggle-camera/${encodeURIComponent(cameraName)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                active: !isCurrentlyActive
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            const statusText = !isCurrentlyActive ? 'activated' : 'deactivated';
            showNotification(`Camera ${statusText} successfully!`, 'success');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to toggle camera status', 'error');
        }
    } catch (error) {
        console.error('Error toggling camera status:', error);
        showNotification(`Error toggling camera status: ${error.message}`, 'error');
    }
};

// Delete Camera Function
window.deleteMgmtCamera = async function(cameraName) {
    console.log(`Delete camera called with name: "${cameraName}"`);

    // Show a more explicit warning about permanent deletion
    if (!confirm(`⚠️ WARNING: This will permanently delete camera "${cameraName}" from the database.\n\nThis action cannot be undone. Are you sure you want to continue?`)) {
        console.log('User cancelled deletion');
        return;
    }

    try {
        const url = `/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`;
        console.log(`Making DELETE request to: ${url}`);

        const response = await fetch(url, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Delete response:', result);

        if (result.status === 'success') {
            showNotification('Camera permanently deleted from database!');
            console.log('Reloading cameras after successful deletion');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to delete camera', 'error');
        }
    } catch (error) {
        console.error('Error deleting camera:', error);
        showNotification(`Error deleting camera: ${error.message}`, 'error');
    }
};

// Add Test Camera Function
window.addTestCamera = async function() {
    try {
        const response = await fetch('/face_recognition/add-test-camera', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('Test camera added successfully!', 'success');
            loadMgmtCameras();
        } else {
            showNotification(result.message || 'Failed to add test camera', 'error');
        }
    } catch (error) {
        console.error('Error adding test camera:', error);
        showNotification(`Error adding test camera: ${error.message}`, 'error');
    }
};
