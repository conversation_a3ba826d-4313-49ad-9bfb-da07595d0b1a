/* ==================== ADMIN PANEL REDESIGN - JAVASCRIPT ==================== */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Panel Redesign - Initializing...');

    // Disable old tab functionality
    disableOldTabSystem();

    // Initialize the redesigned admin panel
    initializeAdminRedesign();
});

function initializeAdminRedesign() {
    // Initialize main navigation
    initializeMainNavigation();
    
    // Initialize sub navigation
    initializeSubNavigation();
    
    // Initialize management functionality
    initializeManagementFunctionality();
    
    // Set default active section
    showSection('unknown-persons');
    
    console.log('Admin Panel Redesign - Initialization complete');
}

function initializeMainNavigation() {
    const mainNavButtons = document.querySelectorAll('.admin-nav-btn');
    
    mainNavButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            
            // Handle management button specially
            if (section === 'management') {
                toggleManagementSubNav();
                showSection('management');
                showSubsection('camera-management'); // Default to camera management
            } else {
                // Hide management sub-nav for other sections
                hideManagementSubNav();
                showSection(section);
            }
            
            // Update active state
            updateMainNavActive(this);
        });
    });
}

function initializeSubNavigation() {
    const subNavButtons = document.querySelectorAll('.admin-sub-nav-btn');
    
    subNavButtons.forEach(button => {
        button.addEventListener('click', function() {
            const subsection = this.getAttribute('data-subsection');
            showSubsection(subsection);
            updateSubNavActive(this);
        });
    });
}

function toggleManagementSubNav() {
    const subNav = document.getElementById('managementSubNav');
    const managementBtn = document.getElementById('managementMainBtn');
    
    if (subNav.style.display === 'none' || subNav.style.display === '') {
        subNav.style.display = 'flex';
        managementBtn.classList.add('active');
    } else {
        subNav.style.display = 'none';
        managementBtn.classList.remove('active');
    }
}

function hideManagementSubNav() {
    const subNav = document.getElementById('managementSubNav');
    const managementBtn = document.getElementById('managementMainBtn');
    
    subNav.style.display = 'none';
    managementBtn.classList.remove('active');
}

function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.admin-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Show the selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
        
        // Load data for the section
        loadSectionData(sectionName);
    }
}

function showSubsection(subsectionName) {
    // Hide all subsections
    const subsections = document.querySelectorAll('.admin-subsection');
    subsections.forEach(subsection => {
        subsection.style.display = 'none';
        subsection.classList.remove('active');
    });
    
    // Show the selected subsection
    const targetSubsection = document.getElementById(subsectionName + '-subsection');
    if (targetSubsection) {
        targetSubsection.style.display = 'block';
        targetSubsection.classList.add('active');
        
        // Load data for the subsection
        loadSubsectionData(subsectionName);
    }
}

function updateMainNavActive(activeButton) {
    // Remove active class from all main nav buttons
    const mainNavButtons = document.querySelectorAll('.admin-nav-btn');
    mainNavButtons.forEach(btn => {
        if (btn !== activeButton) {
            btn.classList.remove('active');
        }
    });
    
    // Add active class to clicked button
    activeButton.classList.add('active');
}

function updateSubNavActive(activeButton) {
    // Remove active class from all sub nav buttons
    const subNavButtons = document.querySelectorAll('.admin-sub-nav-btn');
    subNavButtons.forEach(btn => btn.classList.remove('active'));
    
    // Add active class to clicked button
    activeButton.classList.add('active');
}

function loadSectionData(sectionName) {
    console.log('Loading data for section:', sectionName);
    
    switch (sectionName) {
        case 'unknown-persons':
            if (typeof loadUnknowns === 'function') {
                loadUnknowns();
            }
            break;
        case 'user-registration':
            // User registration doesn't need data loading
            break;
        case 'user-management':
            if (typeof loadUsers === 'function') {
                loadUsers();
            }
            break;
        case 'management':
            // Management data is loaded by subsections
            break;
        case 'settings':
            if (typeof loadSettings === 'function') {
                loadSettings();
            }
            break;
    }
}

function loadSubsectionData(subsectionName) {
    console.log('Loading data for subsection:', subsectionName);
    
    switch (subsectionName) {
        case 'camera-management':
            if (typeof loadMgmtCameras === 'function') {
                loadMgmtCameras();
            }
            break;
        case 'webhook-management':
            if (typeof loadMgmtWebhooks === 'function') {
                loadMgmtWebhooks();
            }
            break;
        case 'whatsapp-management':
            if (typeof loadMgmtWhatsApp === 'function') {
                loadMgmtWhatsApp();
            }
            break;
        case 'sms-management':
            if (typeof loadMgmtSMS === 'function') {
                loadMgmtSMS();
            }
            break;
        case 'email-management':
            if (typeof loadMgmtEmail === 'function') {
                loadMgmtEmail();
            }
            break;
        case 'alert-management':
            if (typeof loadMgmtAlerts === 'function') {
                loadMgmtAlerts();
            }
            break;
    }
}

function initializeManagementFunctionality() {
    // Initialize management modules if they exist
    if (typeof initializeManagementTab === 'function') {
        console.log('Initializing management functionality...');
        initializeManagementTab();
    }
}

// Make functions globally available
window.showSection = showSection;
window.showSubsection = showSubsection;
window.toggleManagementSubNav = toggleManagementSubNav;
window.hideManagementSubNav = hideManagementSubNav;

// Notification function for the redesigned admin panel
function showAdminNotification(message, type = 'success') {
    // Use existing notification function if available
    if (typeof showNotification === 'function') {
        showNotification(message, type);
    } else {
        // Fallback notification
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

window.showAdminNotification = showAdminNotification;

function disableOldTabSystem() {
    // Remove old tab button event listeners
    const oldTabButtons = document.querySelectorAll('.tab-btn');
    oldTabButtons.forEach(button => {
        // Clone the button to remove all event listeners
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
    });

    // Hide old tab system elements if they exist
    const oldTabNav = document.querySelector('.admin-tabs');
    if (oldTabNav) {
        oldTabNav.style.display = 'none';
    }

    const oldTabContent = document.querySelector('.tab-content');
    if (oldTabContent) {
        oldTabContent.style.display = 'none';
    }

    console.log('Old tab system disabled');
}
