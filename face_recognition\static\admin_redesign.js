/* ==================== ADMIN PANEL REDESIGN - JAVASCRIPT ==================== */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Panel Redesign - Initializing...');

    // Wait a bit to ensure all other scripts have loaded
    setTimeout(() => {
        // Disable old tab functionality
        disableOldTabSystem();

        // Initialize the redesigned admin panel
        initializeAdminRedesign();

        // Force show the new design
        forceShowNewDesign();
    }, 100);
});

function initializeAdminRedesign() {
    // Initialize main navigation
    initializeMainNavigation();
    
    // Initialize sub navigation
    initializeSubNavigation();
    
    // Initialize management functionality
    initializeManagementFunctionality();
    
    // Test if elements exist
    const adminContent = document.querySelector('.admin-content');
    const unknownPersonsSection = document.getElementById('unknown-persons-section');
    const managementSection = document.getElementById('management-section');

    console.log('Admin content element:', adminContent);
    console.log('Unknown persons section:', unknownPersonsSection);
    console.log('Management section:', managementSection);

    // Set default active section
    console.log('Setting default section to unknown-persons');
    showSection('unknown-persons');
    
    console.log('Admin Panel Redesign - Initialization complete');
}

function initializeMainNavigation() {
    const mainNavButtons = document.querySelectorAll('.admin-nav-btn');
    
    mainNavButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            console.log('Main nav button clicked:', section);

            // Handle management button specially
            if (section === 'management') {
                console.log('Management button clicked - showing sub-nav');
                toggleManagementSubNav();
                showSection('management');
                showSubsection('camera-management'); // Default to camera management
            } else {
                // Hide management sub-nav for other sections
                console.log('Non-management button clicked - hiding sub-nav');
                hideManagementSubNav();
                showSection(section);
            }

            // Update active state
            updateMainNavActive(this);
        });
    });
}

function initializeSubNavigation() {
    const subNavButtons = document.querySelectorAll('.admin-sub-nav-btn');
    
    subNavButtons.forEach(button => {
        button.addEventListener('click', function() {
            const subsection = this.getAttribute('data-subsection');
            showSubsection(subsection);
            updateSubNavActive(this);
        });
    });
}

function toggleManagementSubNav() {
    const subNav = document.getElementById('managementSubNav');
    const managementBtn = document.getElementById('managementMainBtn');
    
    if (subNav.style.display === 'none' || subNav.style.display === '') {
        subNav.style.display = 'flex';
        managementBtn.classList.add('active');
    } else {
        subNav.style.display = 'none';
        managementBtn.classList.remove('active');
    }
}

function hideManagementSubNav() {
    const subNav = document.getElementById('managementSubNav');
    const managementBtn = document.getElementById('managementMainBtn');
    
    subNav.style.display = 'none';
    managementBtn.classList.remove('active');
}

function showSection(sectionName) {
    console.log('Showing section:', sectionName);

    // Hide all sections
    const sections = document.querySelectorAll('.admin-section');
    console.log('Found sections:', sections.length);
    sections.forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
    });

    // Show the selected section
    const targetSection = document.getElementById(sectionName + '-section');
    console.log('Target section:', targetSection);
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.style.display = 'block';
        console.log('Section shown:', sectionName);

        // Load data for the section
        loadSectionData(sectionName);
    } else {
        console.error('Section not found:', sectionName + '-section');
    }
}

function showSubsection(subsectionName) {
    console.log('Showing subsection:', subsectionName);

    // Hide all subsections
    const subsections = document.querySelectorAll('.admin-subsection');
    subsections.forEach(subsection => {
        subsection.style.display = 'none';
        subsection.classList.remove('active');
    });

    // Show the selected subsection
    const targetSubsection = document.getElementById(subsectionName + '-subsection');
    if (targetSubsection) {
        targetSubsection.style.display = 'block';
        targetSubsection.classList.add('active');

        console.log('Subsection shown:', subsectionName);

        // Load data for the subsection
        loadSubsectionData(subsectionName);
    } else {
        console.error('Subsection not found:', subsectionName + '-subsection');
    }
}

function updateMainNavActive(activeButton) {
    // Remove active class from all main nav buttons
    const mainNavButtons = document.querySelectorAll('.admin-nav-btn');
    mainNavButtons.forEach(btn => {
        if (btn !== activeButton) {
            btn.classList.remove('active');
        }
    });
    
    // Add active class to clicked button
    activeButton.classList.add('active');
}

function updateSubNavActive(activeButton) {
    // Remove active class from all sub nav buttons
    const subNavButtons = document.querySelectorAll('.admin-sub-nav-btn');
    subNavButtons.forEach(btn => btn.classList.remove('active'));
    
    // Add active class to clicked button
    activeButton.classList.add('active');
}

function loadSectionData(sectionName) {
    console.log('Loading data for section:', sectionName);
    
    switch (sectionName) {
        case 'unknown-persons':
            if (typeof loadUnknowns === 'function') {
                loadUnknowns();
            }
            break;
        case 'user-registration':
            // User registration doesn't need data loading
            break;
        case 'user-management':
            if (typeof loadUsers === 'function') {
                loadUsers();
            }
            break;
        case 'management':
            // Management data is loaded by subsections
            break;
        case 'settings':
            if (typeof loadSettings === 'function') {
                loadSettings();
            }
            break;
    }
}

function loadSubsectionData(subsectionName) {
    console.log('Loading data for subsection:', subsectionName);
    
    switch (subsectionName) {
        case 'camera-management':
            if (typeof loadMgmtCameras === 'function') {
                loadMgmtCameras();
            }
            break;
        case 'webhook-management':
            if (typeof loadMgmtWebhooks === 'function') {
                loadMgmtWebhooks();
            }
            break;
        case 'whatsapp-management':
            if (typeof loadMgmtWhatsApp === 'function') {
                loadMgmtWhatsApp();
            }
            break;
        case 'sms-management':
            if (typeof loadMgmtSMS === 'function') {
                loadMgmtSMS();
            }
            break;
        case 'email-management':
            if (typeof loadMgmtEmail === 'function') {
                loadMgmtEmail();
            }
            break;
        case 'alert-management':
            if (typeof loadMgmtAlerts === 'function') {
                loadMgmtAlerts();
            }
            break;
    }
}

function initializeManagementFunctionality() {
    // Initialize management modules if they exist
    if (typeof initializeManagementTab === 'function') {
        console.log('Initializing management functionality...');
        initializeManagementTab();
    }
}

// Make functions globally available
window.showSection = showSection;
window.showSubsection = showSubsection;
window.toggleManagementSubNav = toggleManagementSubNav;
window.hideManagementSubNav = hideManagementSubNav;

// Notification function for the redesigned admin panel
function showAdminNotification(message, type = 'success') {
    // Use existing notification function if available
    if (typeof showNotification === 'function') {
        showNotification(message, type);
    } else {
        // Fallback notification
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

window.showAdminNotification = showAdminNotification;

function disableOldTabSystem() {
    console.log('Disabling old tab system...');

    // Remove old tab button event listeners
    const oldTabButtons = document.querySelectorAll('.tab-btn');
    console.log('Found old tab buttons:', oldTabButtons.length);
    oldTabButtons.forEach(button => {
        // Clone the button to remove all event listeners
        const newButton = button.cloneNode(true);
        if (button.parentNode) {
            button.parentNode.replaceChild(newButton, button);
        }
    });

    // Hide old tab system elements if they exist
    const oldTabNav = document.querySelector('.admin-tabs');
    if (oldTabNav) {
        oldTabNav.style.display = 'none';
        console.log('Hidden old tab nav');
    }

    const oldTabContent = document.querySelector('.tab-content');
    if (oldTabContent) {
        oldTabContent.style.display = 'none';
        console.log('Hidden old tab content');
    }

    // Also hide any tab-pane elements
    const oldTabPanes = document.querySelectorAll('.tab-pane');
    oldTabPanes.forEach(pane => {
        pane.style.display = 'none';
    });
    console.log('Hidden', oldTabPanes.length, 'old tab panes');

    // Override any old tab switching functions
    if (window.switchToTab) {
        window.switchToTab = function() {
            console.log('Old switchToTab function called but disabled');
        };
    }

    console.log('Old tab system disabled');
}

function forceShowNewDesign() {
    console.log('Forcing new design to show...');

    // Ensure admin container is visible
    const adminContainer = document.querySelector('.container.admin-container');
    if (adminContainer) {
        adminContainer.style.display = 'block';
        adminContainer.style.visibility = 'visible';
        console.log('Admin container forced visible');
    }

    // Ensure admin content is visible
    const adminContent = document.querySelector('.admin-content');
    if (adminContent) {
        adminContent.style.display = 'block';
        adminContent.style.visibility = 'visible';
        console.log('Admin content forced visible');
    }

    // Ensure navigation is visible
    const adminNav = document.querySelector('.admin-main-nav');
    if (adminNav) {
        adminNav.style.display = 'flex';
        adminNav.style.visibility = 'visible';
        console.log('Admin navigation forced visible');
    }

    // Force show unknown persons section by default
    const unknownSection = document.getElementById('unknown-persons-section');
    if (unknownSection) {
        unknownSection.style.display = 'block';
        unknownSection.classList.add('active');
        console.log('Unknown persons section forced visible');
    }

    console.log('New design forced to show');
}
