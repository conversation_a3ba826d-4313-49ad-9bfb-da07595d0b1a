<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition System - Premium Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8fafc;
            height: 100vh;
            overflow: hidden;
            font-size: 14px;
            line-height: 1.5;
        }

        .dashboard-container {
            display: flex;
            height: 100vh;
            background: #ffffff;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
        }

        /* Sidebar Styles */
        .sidebar {
            width: 260px;
            background: #1e293b;
            color: #e2e8f0;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
            position: relative;
            border-right: 1px solid #334155;
        }

        .sidebar::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(180deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
        }

        .logo-section {
            padding: 24px 20px;
            border-bottom: 1px solid #334155;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            position: relative;
        }

        .logo-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, #3b82f6 50%, transparent 100%);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 22px;
            font-weight: 700;
            color: #ffffff;
            letter-spacing: -0.5px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo:hover {
            color: #3b82f6;
            transform: translateY(-1px);
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .logo:hover .logo-icon {
            transform: rotate(5deg) scale(1.05);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .logo-icon svg {
            width: 18px;
            height: 18px;
            color: white;
        }

        .logo-text {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-subtitle {
            font-size: 10px;
            font-weight: 500;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 2px;
            text-align: center;
        }

        .nav-section {
            flex: 1;
            padding: 20px 0;
        }

        .nav-group {
            margin-bottom: 32px;
        }

        .nav-group-title {
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #64748b;
            padding: 0 18px 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 10px 18px;
            margin: 1px 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            font-weight: 500;
            font-size: 13px;
            color: #cbd5e1;
            border: none;
            background: none;
            width: calc(100% - 16px);
            text-align: left;
        }

        .nav-item:hover {
            background: #334155;
            color: #f1f5f9;
        }

        .nav-item.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: #3498db;
            border-radius: 2px;
        }

        .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            opacity: 0.7;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        .footer-nav {
            padding: 20px 16px 24px;
            border-top: 1px solid #334155;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            position: relative;
            margin-top: auto;
        }

        .footer-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, #3b82f6 50%, transparent 100%);
        }

        .homepage-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #334155;
            border-radius: 8px;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .homepage-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.1) 50%, transparent 100%);
            transition: left 0.5s ease;
        }

        .homepage-btn:hover::before {
            left: 100%;
        }

        .homepage-btn:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #3b82f6;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .homepage-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .homepage-btn-icon {
            width: 18px;
            height: 18px;
            transition: all 0.3s ease;
        }

        .homepage-btn:hover .homepage-btn-icon {
            transform: scale(1.1) rotate(-5deg);
        }

        .homepage-btn-text {
            flex: 1;
            text-align: left;
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .homepage-btn-arrow {
            width: 14px;
            height: 14px;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .homepage-btn:hover .homepage-btn-arrow {
            opacity: 1;
            transform: translateX(4px);
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafbfc;
        }

        .header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            color: #dc2626;
        }

        .status-indicator.active {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #dc2626;
        }

        .status-dot.active {
            background: #16a34a;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            display: flex;
            gap: 24px;
        }

        .main-panel {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .main-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
        }

        /* Camera feeds section */
        .webcam-section {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .webcam-section h2 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            flex: 1;
        }

        .camera-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            flex-direction: column;
        }

        .camera-container h3 {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .camera-container img {
            width: 100%;
            height: auto;
            border-radius: 6px;
            background: #f1f5f9;
        }

        /* No cameras empty state */
        .no-cameras-fullscreen {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            flex: 1;
        }

        .no-cameras-content {
            text-align: center;
            max-width: 380px;
        }

        .setup-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #f59e0b;
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .camera-icon {
            width: 100px;
            height: 100px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            position: relative;
        }

        .camera-icon::after {
            content: '';
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 3px;
            background: #ef4444;
            border-radius: 2px;
            transform: rotate(45deg);
        }

        .camera-svg {
            width: 48px;
            height: 48px;
            fill: #94a3b8;
        }

        .empty-state-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .empty-state-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .configure-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .configure-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }

        .configure-btn:active {
            transform: translateY(0);
        }

        .side-panel {
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            padding: 20px;
            height: fit-content;
        }

        .side-panel-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
        }

        .live-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(16, 185, 129, 0); }
            100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
        }

        .side-panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .monitoring-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
            color: #64748b;
            text-align: center;
        }

        .stats-icon {
            width: 64px;
            height: 64px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }

        .stats-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* Live observations list */
        .live-observations {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .live-observations li {
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            font-size: 13px;
        }

        .live-observations li:last-child {
            border-bottom: none;
        }

        .observation-time {
            font-size: 11px;
            color: #64748b;
            margin: 4px 0;
        }

        .observation-status {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #16a34a;
            font-weight: 500;
        }

        /* Confirmation Dialog */
        .confirm-dialog {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            align-items: center;
            justify-content: center;
        }

        .confirm-dialog.show {
            display: flex;
        }

        .confirm-dialog > div {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            width: 90%;
        }

        .confirm-dialog h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .confirm-dialog p {
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .confirm-dialog-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .confirm-dialog button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .confirm-yes {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }

        .confirm-yes:hover {
            background: #b91c1c;
        }

        .confirm-no {
            background: white;
            color: #64748b;
        }

        .confirm-no:hover {
            background: #f8fafc;
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            z-index: 9999;
            font-weight: 500;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: opacity 0.3s, transform 0.3s;
            pointer-events: none;
            min-width: 250px;
            text-align: center;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(-50%) translateY(-10px);
        }

        .toast.success {
            background: #16a34a;
        }

        .toast.error {
            background: #dc2626;
        }

        .toast.info {
            background: #3b82f6;
        }

        /* ==================== START OF CHATBOT CSS ==================== */
        /* AI Chatbot Styles */
        .chatbot-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            z-index: 10000;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .chatbot-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(52, 152, 219, 0.4);
        }

        .chatbot-button svg {
            width: 28px;
            height: 28px;
            fill: white;
            transition: all 0.3s ease;
        }

        .chatbot-button.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .chatbot-button.active:hover {
            box-shadow: 0 12px 35px rgba(52, 152, 219, 0.4);
        }

        .chatbot-icon {
            transition: all 0.3s ease;
        }

        .chatbot-icon.hidden {
            opacity: 0;
            transform: scale(0.8) rotate(180deg);
        }

        .close-icon {
            position: absolute;
            opacity: 0;
            transform: scale(0.8) rotate(-180deg);
            transition: all 0.3s ease;
        }

        .close-icon.visible {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        @keyframes pulse {
            0% { box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3); }
            50% { box-shadow: 0 8px 25px rgba(52, 152, 219, 0.6); }
            100% { box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3); }
        }

        .chat-container {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            z-index: 10001;
            display: none;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .chat-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .chat-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .message {
            display: flex;
            margin-bottom: 16px;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .bot-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: #64748b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-content {
            background: white;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 280px;
            font-size: 14px;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 16px 0;
        }

        .quick-action {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #64748b;
            font-weight: 500;
        }

        .quick-action:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
            transform: translateY(-1px);
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 12px 16px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #94a3b8;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .chat-input:focus {
            border-color: #3498db;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .send-btn svg {
            width: 18px;
            height: 18px;
            fill: white;
        }
        /* ==================== END OF CHATBOT CSS ==================== */

        /* Responsive Design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 220px;
            }

            .side-panel {
                width: 260px;
            }

            .logo {
                font-size: 20px;
            }

            .logo-icon {
                width: 28px;
                height: 28px;
            }

            .logo-icon svg {
                width: 16px;
                height: 16px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 180px;
            }

            .content-area {
                flex-direction: column;
                gap: 16px;
            }

            .side-panel {
                width: 100%;
            }

            .header-title {
                font-size: 18px;
            }

            .empty-state-title {
                font-size: 20px;
            }

            .logo-section {
                padding: 20px 16px;
            }

            .logo {
                font-size: 18px;
                gap: 10px;
            }

            .logo-icon {
                width: 24px;
                height: 24px;
            }

            .logo-icon svg {
                width: 14px;
                height: 14px;
            }

            .logo-subtitle {
                font-size: 9px;
            }

            .footer-nav {
                padding: 16px 12px 20px;
            }

            .homepage-btn {
                padding: 10px 12px;
                font-size: 12px;
                gap: 10px;
            }

            .homepage-btn-icon {
                width: 16px;
                height: 16px;
            }

            .homepage-btn-arrow {
                width: 12px;
                height: 12px;
            }

            /* ==================== START OF CHATBOT RESPONSIVE CSS ==================== */
            /* Chatbot responsive */
            .chatbot-button {
                width: 50px;
                height: 50px;
                bottom: 20px;
                right: 20px;
            }

            .chatbot-button svg {
                width: 24px;
                height: 24px;
            }

            .chatbot-button.active:hover {
                box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
            }

            .chat-container {
                width: 320px;
                height: 450px;
                bottom: 80px;
                right: 20px;
            }

            .chat-header {
                padding: 16px;
            }

            .chat-title {
                font-size: 16px;
            }

            .chat-messages {
                padding: 16px;
            }

            .chat-input-container {
                padding: 16px;
            }
            /* ==================== END OF CHATBOT RESPONSIVE CSS ==================== */
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo-section">
                <div class="logo">
                    <div class="logo-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                    </div>
                    <div>
                        <div class="logo-text">VigilantEye</div>
                        <div class="logo-subtitle">Security System</div>
                    </div>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-group">
                    <div class="nav-group-title">Monitoring</div>
                    <button class="nav-item" id="startStreamBtn">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        Start Monitoring
                    </button>
                    <button class="nav-item" id="stopStreamBtn">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                            <rect x="6" y="6" width="12" height="12" rx="2"/>
                        </svg>
                        Stop Monitoring
                    </button>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">Reports</div>
                    <button class="nav-item" id="attendanceBtn">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        Attendance Logs
                    </button>
                </div>

                {% if user_role == "admin" %}
                <div class="nav-group">
                    <div class="nav-group-title">Administration</div>
                    <button class="nav-item" id="adminBtn">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                        Admin Panel
                    </button>
                </div>
                {% endif %}
            </div>

            <div class="footer-nav">
                <button class="homepage-btn" id="homepageBtn">
                    <svg class="homepage-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span class="homepage-btn-text">Go To Homepage</span>
                    <svg class="homepage-btn-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M5 12h14m-7-7 7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1 class="header-title">Face Recognition System</h1>
                <div class="status-indicator" id="statusIndicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">System Inactive</span>
                </div>
            </div>

            <div class="content-area">
                <div class="main-panel">
                    <!-- Camera feeds section (shown when cameras are available) -->
                    <div class="webcam-section" id="webcamSection">
                        <h2>Camera Feeds</h2>
                        <div class="video-grid" id="feeds">
                            <!-- Video feeds will be added dynamically -->
                        </div>
                    </div>

                    <!-- No cameras message (shown when no cameras are configured) -->
                    <div class="no-cameras-fullscreen" id="noCamerasFullscreen" style="display: none;">
                        <div class="setup-badge">Setup Required</div>

                        <div class="no-cameras-content">
                            <div class="camera-icon">
                                <svg class="camera-svg" viewBox="0 0 24 24">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                    <circle cx="12" cy="13" r="4"/>
                                </svg>
                            </div>

                            <h2 class="empty-state-title">No Cameras Configured</h2>
                            <p class="empty-state-description">
                                To start face recognition monitoring, you need to configure at least one camera. Add cameras through the Admin Panel to get started.
                            </p>

                            {% if user_role == "admin" %}
                            <button class="configure-btn" onclick="window.location.href='/face_recognition/admin'">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                    <circle cx="12" cy="13" r="4"/>
                                </svg>
                                Configure Cameras
                            </button>
                            {% else %}
                            <p class="empty-state-description">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 6px;">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                                Contact your administrator to configure cameras
                            </p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="side-panel">
                    <div class="side-panel-header">
                        <div class="live-indicator"></div>
                        <h3 class="side-panel-title">Live Observations</h3>
                    </div>

                    <ul class="live-observations" id="observationsList">
                        <!-- Default empty state -->
                        <div class="monitoring-stats">
                            <div class="stats-icon">
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="#bdc3c7">
                                    <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8c0 1.9-.6 3.7-1.6 5.2L21 21l-1.4 1.4-4.6-4.6C13.7 18.4 11.9 19 9 19zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6z"/>
                                </svg>
                            </div>
                            <div class="stats-text">No active monitoring sessions</div>
                        </div>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog -->
    <div id="confirmDialog" class="confirm-dialog">
        <div>
            <h3>Confirm Action</h3>
            <p id="confirmMessage">Are you sure you want to perform this action?</p>
            <div class="confirm-dialog-buttons">
                <button class="confirm-yes" id="confirmYes">Yes</button>
                <button class="confirm-no" id="confirmNo">No</button>
            </div>
        </div>
    </div>

    <!-- Toast notification container -->
    <div id="toast" class="toast"></div>

    <!-- ==================== START OF CHATBOT HTML ==================== -->
    <!-- AI Chatbot -->
    <button class="chatbot-button" id="chatbotBtn">
        <svg class="chatbot-icon" id="chatIcon" viewBox="0 0 24 24">
            <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4C22,2.89 21.1,2 20,2M6,9V7H18V9H6M14,11V13H6V11H14M16,15H6V17H16V15Z"/>
        </svg>
        <svg class="close-icon" id="closeIcon" viewBox="0 0 24 24">
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
        </svg>
    </button>

    <div class="chat-container" id="chatContainer">
        <div class="chat-header">
            <div>
                <div class="chat-title">AI Assistant</div>
                <div class="chat-subtitle">VigilantEye Support</div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="bot-avatar">🤖</div>
                <div class="message-content">
                    Hello! I'm your VigilantEye AI assistant. How can I help you with your security system today?
                </div>
            </div>

            <div class="quick-actions">
                <div class="quick-action" data-message="How do I start monitoring?">Start Monitoring</div>
                <div class="quick-action" data-message="System troubleshooting help">Troubleshooting</div>
                <div class="quick-action" data-message="How to add new faces?">Add Faces</div>
                <div class="quick-action" data-message="View attendance reports">Reports</div>
            </div>

            <div class="message bot" id="typingMessage" style="display: none;">
                <div class="bot-avatar">🤖</div>
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <input type="text" class="chat-input" id="chatInput" placeholder="Ask me anything about VigilantEye...">
                <button class="send-btn" id="sendBtn">
                    <svg viewBox="0 0 24 24">
                        <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- ==================== END OF CHATBOT HTML ==================== -->

  <script>
    // DOM Elements
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const feedsDiv = document.getElementById("feeds");
    const observationsList = document.getElementById("observationsList");
    const homepageBtn = document.getElementById("homepageBtn");
    const statusDot = document.getElementById("statusDot");
    const statusText = document.getElementById("statusText");
    const statusIndicator = document.getElementById("statusIndicator");
    const confirmDialog = document.getElementById("confirmDialog");
    const confirmYes = document.getElementById("confirmYes");
    const confirmNo = document.getElementById("confirmNo");
    const confirmMessage = document.getElementById("confirmMessage");

    // ==================== START OF CHATBOT JAVASCRIPT ====================
    // Chatbot DOM Elements
    const chatbotBtn = document.getElementById("chatbotBtn");
    const chatContainer = document.getElementById("chatContainer");
    const chatMessages = document.getElementById("chatMessages");
    const chatInput = document.getElementById("chatInput");
    const sendBtn = document.getElementById("sendBtn");
    const typingMessage = document.getElementById("typingMessage");
    const chatIcon = document.getElementById("chatIcon");
    const closeIcon = document.getElementById("closeIcon");

    // State variables
    let websocket = null;
    let showList = false;
    let activeFeeds = new Map(); // Use Map instead of Set to track camera details
    let isStreaming = false;
    let pendingAction = null;
    let camerasAvailable = false;

    // Chatbot state variables
    let isChatOpen = false;

    // Initialize chatbot on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing chatbot...');
        console.log('Chatbot elements found:', {
            chatbotBtn: !!document.getElementById("chatbotBtn"),
            chatContainer: !!document.getElementById("chatContainer"),
            chatMessages: !!document.getElementById("chatMessages"),
            chatInput: !!document.getElementById("chatInput"),
            sendBtn: !!document.getElementById("sendBtn")
        });
    });

    // Chatbot functionality
    function toggleChat() {
        console.log('Toggle chat called, current state:', isChatOpen);
        isChatOpen = !isChatOpen;

        if (chatContainer) {
            chatContainer.style.display = isChatOpen ? 'flex' : 'none';
        }

        // Toggle button appearance and icons
        if (isChatOpen) {
            if (chatbotBtn) chatbotBtn.classList.add('active');
            if (chatIcon) chatIcon.classList.add('hidden');
            if (closeIcon) closeIcon.classList.add('visible');
            if (chatInput) {
                setTimeout(() => chatInput.focus(), 100);
            }
        } else {
            if (chatbotBtn) chatbotBtn.classList.remove('active');
            if (chatIcon) chatIcon.classList.remove('hidden');
            if (closeIcon) closeIcon.classList.remove('visible');
        }
        console.log('Chat toggled, new state:', isChatOpen);
    }

    function addMessage(content, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

        const avatar = document.createElement('div');
        avatar.className = isUser ? 'user-avatar' : 'bot-avatar';
        avatar.textContent = isUser ? 'U' : '🤖';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        // Insert before typing message
        chatMessages.insertBefore(messageDiv, typingMessage);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function showTyping() {
        typingMessage.style.display = 'flex';
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function hideTyping() {
        typingMessage.style.display = 'none';
    }

    function generateResponse(message) {
        const responses = {
            'how do i start monitoring?': 'To start monitoring, click the "Start Monitoring" button in the sidebar. Make sure you have cameras configured in the Admin Panel first. The system will automatically begin face recognition on all active cameras.',
            'system troubleshooting help': 'For troubleshooting: 1) Check if cameras are properly connected, 2) Verify network connectivity, 3) Ensure sufficient lighting for face detection, 4) Restart the monitoring system if needed. Contact admin if issues persist.',
            'how to add new faces?': 'To add new faces: 1) Go to Admin Panel → Face Management, 2) Click "Add New Person", 3) Upload clear photos of the person, 4) Enter their details, 5) Save. The system will learn to recognize them automatically.',
            'view attendance reports': 'To view attendance reports, click "Attendance Logs" in the sidebar. You can filter by date range, person, or camera. Reports show entry/exit times and can be exported to CSV format.',
            'camera': 'For camera-related issues: Ensure cameras are properly connected, check network settings, verify camera permissions, and make sure they support the required video formats. Use the Admin Panel to test camera connections.',
            'face': 'Face recognition works by analyzing facial features and comparing them to stored profiles. Ensure good lighting, clear camera angles, and that faces are not obscured. The system improves accuracy over time.',
            'admin': 'Admin features include: Camera Management, Face Database, System Settings, User Management, and Attendance Reports. Access the Admin Panel through the sidebar if you have admin privileges.',
            'help': 'I can help you with: Starting/stopping monitoring, camera setup, face recognition, troubleshooting, attendance reports, and system configuration. What specific area do you need assistance with?',
            'default': 'I understand you\'re asking about the VigilantEye security system. Could you please be more specific about what you need help with? I can assist with monitoring, cameras, face recognition, reports, or troubleshooting.'
        };

        const lowerMessage = message.toLowerCase();

        // Check for exact matches first
        for (let key in responses) {
            if (key !== 'default' && (lowerMessage.includes(key) || key.includes(lowerMessage))) {
                return responses[key];
            }
        }

        // Check for specific keywords
        if (lowerMessage.includes('start') || lowerMessage.includes('begin') || lowerMessage.includes('monitoring')) {
            return responses['how do i start monitoring?'];
        }
        if (lowerMessage.includes('problem') || lowerMessage.includes('error') || lowerMessage.includes('not working')) {
            return responses['system troubleshooting help'];
        }
        if (lowerMessage.includes('report') || lowerMessage.includes('attendance') || lowerMessage.includes('log')) {
            return responses['view attendance reports'];
        }
        if (lowerMessage.includes('add') && lowerMessage.includes('face')) {
            return responses['how to add new faces?'];
        }

        return responses.default;
    }

    function sendMessage(message) {
        if (!message.trim()) return;

        console.log('Sending message:', message);

        // Add user message
        addMessage(message, true);
        chatInput.value = '';

        // Show typing indicator
        showTyping();

        // Call backend API
        fetch('http://localhost:8001/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ question: message })
        })
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }
            return res.json();
        })
        .then(data => {
            console.log('Received response:', data);
            hideTyping();

            // Check if response is a plain string (direct AI response)
            if (typeof data === 'string') {
                addMessage(data, false);
            }
            // Check if response has a result that is a string (AI-generated answer)
            else if (data.result && typeof data.result === 'string') {
                addMessage(data.result, false);
            }
            // Check if response has SQL results (database query response)
            else if (data.result && Array.isArray(data.result) && data.result.length > 0) {
                // Format the SQL result nicely
                let response = "Here are the results:\n\n";
                data.result.forEach((row, index) => {
                    response += `Record ${index + 1}:\n`;
                    Object.entries(row).forEach(([key, value]) => {
                        response += `• ${key}: ${value}\n`;
                    });
                    response += "\n";
                });
                addMessage(response, false);
            }
            // Check if response has empty SQL results
            else if (data.result && Array.isArray(data.result) && data.result.length === 0) {
                addMessage("No records found for your query.", false);
            }
            // Check if response has SQL query info
            else if (data.sql) {
                addMessage(`Query executed: ${data.sql}\n\nResult: ${JSON.stringify(data.result, null, 2)}`, false);
            }
            // Check if response has error
            else if (data.error) {
                addMessage("❗ Error: " + data.error, false);
            }
            // Check if response is an object with a message property
            else if (data.message) {
                addMessage(data.message, false);
            }
            // If none of the above, try to display the response as is
            else {
                // Convert object to string if needed
                const responseText = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);
                addMessage(responseText, false);
            }
        })
        .catch(err => {
            hideTyping();
            console.error('Chat API Error:', err);
            if (err.message.includes('Failed to fetch')) {
                addMessage("❗ Cannot connect to chat service. Please ensure the chat service is running on port 8001.", false);
            } else {
                addMessage("❗ Failed to contact backend: " + err.message, false);
            }
        });
    }

    // Chatbot event listeners - with error checking
    if (chatbotBtn) {
        chatbotBtn.addEventListener('click', toggleChat);
        console.log('Chatbot button event listener added');
    } else {
        console.error('Chatbot button not found!');
    }

    // Quick actions event listeners
    document.querySelectorAll('.quick-action').forEach(action => {
        action.addEventListener('click', () => {
            const message = action.getAttribute('data-message');
            sendMessage(message);
        });
    });

    // Send button event listener - with error checking
    if (sendBtn) {
        sendBtn.addEventListener('click', () => {
            if (chatInput) {
                const message = chatInput.value.trim();
                if (message) sendMessage(message);
            }
        });
        console.log('Send button event listener added');
    } else {
        console.error('Send button not found!');
    }

    // Enter key event listener for chat input - with error checking
    if (chatInput) {
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const message = chatInput.value.trim();
                if (message) sendMessage(message);
            }
        });
        console.log('Chat input event listener added');
    } else {
        console.error('Chat input not found!');
    }
    // ==================== END OF CHATBOT JAVASCRIPT ====================

    // Event listener for the "Go to Homepage" button
    homepageBtn.addEventListener("click", () => {
      window.location.href = "/";
    });

    // Function to check if cameras are available
    async function checkCamerasAvailable() {
      try {
        const response = await fetch('/face_recognition/get-cameras');
        if (response.ok) {
          const cameras = await response.json();
          camerasAvailable = Object.keys(cameras).length > 0;
          updateCameraDisplay();
          return camerasAvailable;
        }
      } catch (error) {
        console.error("Error checking cameras:", error);
        camerasAvailable = false;
        updateCameraDisplay();
      }
      return false;
    }

    // Function to update camera display based on availability and streaming status
    function updateCameraDisplay() {
      const noCamerasFullscreen = document.getElementById('noCamerasFullscreen');
      const webcamSection = document.getElementById('webcamSection');

      // Show camera feeds only if cameras are available AND streaming is active
      if (camerasAvailable && isStreaming) {
        noCamerasFullscreen.style.display = 'none';
        webcamSection.style.display = 'block';
      } else {
        noCamerasFullscreen.style.display = 'flex';
        webcamSection.style.display = 'none';

        // Update the message based on the situation
        const setupBadge = noCamerasFullscreen.querySelector('.setup-badge');
        const emptyStateTitle = noCamerasFullscreen.querySelector('.empty-state-title');
        const emptyStateDescription = noCamerasFullscreen.querySelector('.empty-state-description');
        const configureBtn = noCamerasFullscreen.querySelector('.configure-btn');
        const adminContactMsg = noCamerasFullscreen.querySelector('.empty-state-description:last-child');

        if (!camerasAvailable) {
          // No cameras configured
          setupBadge.textContent = 'Setup Required';
          emptyStateTitle.textContent = 'No Cameras Configured';
          emptyStateDescription.textContent = 'To start face recognition monitoring, you need to configure at least one camera. Add cameras through the Admin Panel to get started.';

          // Show configure button for admin, hide for regular users
          if (configureBtn) configureBtn.style.display = 'inline-flex';
          if (adminContactMsg) adminContactMsg.style.display = 'block';
        } else if (!isStreaming) {
          // Cameras available but not streaming
          setupBadge.textContent = 'System Inactive';
          emptyStateTitle.textContent = 'Monitoring Not Active';
          emptyStateDescription.textContent = 'Click "Start Monitoring" to begin face recognition surveillance. The system will monitor all configured cameras for face detection.';

          // Hide configure button and admin contact message when cameras are available
          if (configureBtn) configureBtn.style.display = 'none';
          if (adminContactMsg) adminContactMsg.style.display = 'none';
        }
      }
    }

    // Function to update the status indicator
    function updateStatus(active) {
      if (active) {
        statusDot.classList.remove("inactive");
        statusDot.classList.add("active");
        statusIndicator.classList.add("active");
        statusText.textContent = "System Active";
        startStreamBtn.classList.add("active");
        stopStreamBtn.classList.remove("active");
      } else {
        statusDot.classList.remove("active");
        statusDot.classList.add("inactive");
        statusIndicator.classList.remove("active");
        statusText.textContent = "System Inactive";
        startStreamBtn.classList.remove("active");
        stopStreamBtn.classList.add("active");
      }

      // Update camera display when status changes
      updateCameraDisplay();
    }

    // Function to show confirmation dialog
    function showConfirmDialog(message, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.classList.add("show");

      // Store the callback for later use
      pendingAction = onConfirm;

      // Set up event listeners for the buttons
      confirmYes.onclick = () => {
        confirmDialog.classList.remove("show");
        if (pendingAction) pendingAction();
        pendingAction = null;
      };

      confirmNo.onclick = () => {
        confirmDialog.classList.remove("show");
        pendingAction = null;
      };
    }




    // Function to start the video stream
    async function startStream() {
      try {
        // First check if cameras are available
        const hasCamera = await checkCamerasAvailable();
        if (!hasCamera) {
          showToast("No cameras configured. Please add cameras in the Admin Panel.", "error");
          setTimeout(() => {
            showToast("Go to Admin Panel → Camera Management to add cameras", "info");
          }, 3000);
          return;
        }

        // First make sure any existing streams are properly stopped
        if (isStreaming) {
          await stopStream(false); // Silent stop - don't show messages
        }

        showToast("Starting monitoring system...", "info");

        // First get the current threshold setting
        const settingsResponse = await fetch('/face_recognition/get-settings');
        let threshold = 0.7; // Default value

        if (settingsResponse.ok) {
          const settings = await settingsResponse.json();
          threshold = settings.face_threshold;
          console.log(`Using threshold value: ${threshold}`);
        } else {
          console.warn("Could not get threshold settings, using default value");
        }

        // Start the detection with the current threshold
        const response = await fetch('/face_recognition/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();

        if (response.ok) {
          console.log(`Stream started successfully with threshold: ${result.threshold}`);
          isStreaming = true;
          updateStatus(true);
          startWebSocketFeed();
          showToast(`Monitoring system started successfully (${result.cameras_count} cameras)`, "success");
        } else {
          // Handle specific error types
          if (result.error_type === "no_cameras") {
            showToast(result.message, "error");
            // Show additional guidance
            setTimeout(() => {
              showToast("Go to Admin Panel → Camera Management to add cameras", "info");
            }, 3000);
          } else {
            console.error("Failed to start stream:", result.message);
            showToast(result.message || "Failed to start monitoring system", "error");
          }
        }
      } catch (error) {
        console.error("Error starting stream:", error);
        showToast(`Error: ${error.message}`, "error");
        updateStatus(false);
      }
    }

// Toast notification function - exactly as in original file
function showToast(message, type = "info") {
  console.log(`Showing toast: ${message} (${type})`);

  // Get or create the toast element
  let toast = document.getElementById("toast");
  if (!toast) {
    toast = document.createElement("div");
    toast.id = "toast";
    toast.className = "toast";
    document.body.appendChild(toast);
    console.log("Toast element created since it wasn't found");
  }

  // Clear any existing timeout
  if (window.toastTimeout) {
    clearTimeout(window.toastTimeout);
    toast.classList.remove("show");
  }

  // Force a reflow to ensure transition works
  void toast.offsetWidth;

  // Set toast content and style
  toast.textContent = message;
  toast.className = `toast ${type}`;

  // Set background color based on type with actual color values
  switch(type) {
    case "success":
      toast.style.backgroundColor = "#16a34a";
      break;
    case "error":
      toast.style.backgroundColor = "#dc2626";
      break;
    case "info":
      toast.style.backgroundColor = "#3b82f6";
      break;
    default:
      toast.style.backgroundColor = "#3b82f6";
  }

  // Make sure z-index is high enough
  toast.style.zIndex = "9999";

  // Show the toast
  toast.classList.add("show");
  console.log("Toast should be visible now with message:", message);

  // Hide after 5 seconds
  window.toastTimeout = setTimeout(() => {
    toast.classList.remove("show");
    console.log("Toast hidden");
  }, 5000);
}

    // Test function to verify toast functionality (can be called from browser console)
    function testToast() {
      showToast("Test message - Info", "info");
      setTimeout(() => showToast("Test message - Success", "success"), 1500);
      setTimeout(() => showToast("Test message - Error", "error"), 3000);
    }

    // Function to close the WebSocket connection without stopping the backend monitoring
    function closeWebSocketOnly() {
      if (websocket) {
        // Use a proper WebSocket closing sequence
        const ws = websocket;
        websocket = null; // Clear reference first

        ws.onclose = null; // Remove the onclose handler to avoid triggering it when we close
        ws.onerror = null; // Remove error handler
        ws.onmessage = null; // Remove message handler

        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close(1000, "Deliberate close"); // Normal closure
        }
      }

      // Clean up UI elements
      feedsDiv.innerHTML = ""; // Clear video feeds
      observationsList.innerHTML = `
        <div class="monitoring-stats">
          <div class="stats-icon">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="#bdc3c7">
              <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8c0 1.9-.6 3.7-1.6 5.2L21 21l-1.4 1.4-4.6-4.6C13.7 18.4 11.9 19 9 19zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6z"/>
            </svg>
          </div>
          <div class="stats-text">No active monitoring sessions</div>
        </div>
      `; // Clear observations and restore empty state
      activeFeeds.clear(); // Clear tracked feeds
    }

    // Function to stop the video stream
    async function stopStream(showMessages = true) {
      try {
        // First close the WebSocket connection
        closeWebSocketOnly();

        // Only call the backend stop if we think streaming is active
        if (isStreaming) {
          if (showMessages) showToast("Stopping monitoring system...", "info");

          const response = await fetch('/face_recognition/stop', { method: 'POST' });
          if (response.ok) {
            if (showMessages) {
              console.log("Stream stopped successfully");
              showToast("Monitoring system stopped successfully", "success");
            }
          } else if (showMessages) {
            console.error("Failed to stop stream on server");
            showToast("Failed to stop monitoring system", "error");
          }
        }

        // Update state
        isStreaming = false;
        updateStatus(false);

        // Clear the localStorage state
        localStorage.removeItem('faceRecognitionActive');

      } catch (error) {
        if (showMessages) {
          console.error("Error stopping stream:", error);
          showToast(`Error: ${error.message}`, "error");
        }
        isStreaming = false;
        updateStatus(false);
      }
    }

    // Function to start the WebSocket connection and handle incoming frames
    function startWebSocketFeed() {
      // Use window.location to construct WebSocket URL dynamically
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/face_recognition/ws`;

      console.log(`Connecting to WebSocket at: ${wsUrl}`);
      websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log("WebSocket connection established.");
        showToast("Camera connection established", "success");
      };

      websocket.onmessage = (event) => {
        if (typeof event.data === 'string') {
          // Parse the message
          const message = event.data;
          const parts = message.split(':', 3); // Split only on the first two delimiters

          if (parts.length < 3) {
            console.error("Invalid message format:", message);
            return;
          }

          const cameraId = parts[0];
          const cameraName = parts[1];
          const data = parts.slice(2).join(':'); // In case the base64 data contains colons

          // Store camera information in the Map
          activeFeeds.set(cameraId, { name: cameraName, lastUpdate: Date.now() });

          // Update video feed
          let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
          if (!cameraContainer) {
            // Create a new container for this camera
            cameraContainer = document.createElement("div");
            cameraContainer.id = `camera-container-${cameraId}`;
            cameraContainer.classList.add("camera-container");

            // Add camera label
            const cameraLabel = document.createElement("h3");
            cameraLabel.textContent = cameraName;
            cameraContainer.appendChild(cameraLabel);

            // Add the video feed
            const imgElem = document.createElement("img");
            imgElem.id = `feed-${cameraId}`;
            imgElem.alt = "Processed Video Feed";
            cameraContainer.appendChild(imgElem);

            // Append the camera container to the feeds
            feedsDiv.appendChild(cameraContainer);
          }

          // Update the video feed image
          const imgElem = document.querySelector(`#feed-${cameraId}`);
          if (imgElem) {
            imgElem.src = `data:image/jpeg;base64,${data}`;
            imgElem.onerror = function() {
              console.error(`Error loading image for camera ${cameraName}`);
              this.src = ''; // Clear the invalid image
            };
          }

          // Update live observations - clear empty state first
          const monitoringStats = observationsList.querySelector('.monitoring-stats');
          if (monitoringStats) {
            monitoringStats.remove();
          }

          let observationElem = document.querySelector(`#observation-${cameraId}`);
          if (!observationElem) {
            // Create a new list item if it doesn't exist
            observationElem = document.createElement("li");
            observationElem.id = `observation-${cameraId}`;
            observationsList.appendChild(observationElem);
          }

          // Format observations with timestamp
          const now = new Date();
          const timeString = now.toLocaleTimeString();
          observationElem.innerHTML = `
            <strong>${cameraName}</strong>
            <div class="observation-time">${timeString}</div>
            <div class="observation-status">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 4px;">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Face Detection Active
            </div>
          `;
        }
      };

      websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
        showToast("Connection error. Please refresh the page.", "error");
        isStreaming = false;
        updateStatus(false);
      };

      websocket.onclose = (event) => {
        console.log(`WebSocket connection closed: Code ${event.code}`);
        if (isStreaming && event.code !== 1000) {
          // Not a normal closure and we think we should be streaming
          showToast("Connection closed unexpectedly. Please refresh the page.", "error");
          isStreaming = false;
          updateStatus(false);
        }
      };
    }

    // Event listeners for start and stop buttons
    startStreamBtn.addEventListener('click', () => {
      if (!isStreaming) {
        startStream();
      } else {
        showToast("Monitoring system is already running", "info");
      }
    });

    stopStreamBtn.addEventListener('click', () => {
      if (isStreaming) {
        showConfirmDialog("Are you sure you want to stop the monitoring system?", () => {
          stopStream(true);
        });
      } else {
        showToast("Monitoring system is already stopped", "info");
      }
    });

    // Store monitoring state in localStorage when navigating to other pages
    // We'll only stop the WebSocket connection but keep the backend monitoring running
    window.addEventListener('beforeunload', (event) => {
      // Check if this is a page navigation or browser close
      if (event.clientY < 0 ||
          event.altKey ||
          event.ctrlKey ||
          event.metaKey ||
          event.shiftKey) {
        // This is likely a browser close, so stop the stream completely
        stopStream(false);
      } else {
        // This is likely a navigation, so store the state and just close the WebSocket
        localStorage.setItem('faceRecognitionActive', isStreaming.toString());

        // Close only the WebSocket connection without stopping the backend monitoring
        closeWebSocketOnly();
      }
    });

    // Camera management and user registration functionality moved to admin panel

    // Event listener for the "Logs" button
    const logBtn = document.getElementById("attendanceBtn");
    logBtn.addEventListener("click", () => {
      // Store the current monitoring state before navigating
      localStorage.setItem('faceRecognitionActive', isStreaming.toString());

      // Close only the WebSocket connection without stopping the backend monitoring
      if (isStreaming) {
        closeWebSocketOnly();
      }

      window.location.href = "/face_recognition/attendance";
    });

    // Event listener for the "Admin" button
    const adminBtn = document.getElementById("adminBtn");
    adminBtn.addEventListener("click", () => {
      // Store the current monitoring state before navigating
      localStorage.setItem('faceRecognitionActive', isStreaming.toString());

      // Close only the WebSocket connection without stopping the backend monitoring
      if (isStreaming) {
        closeWebSocketOnly();
      }

      window.location.href = "/face_recognition/admin";
    });

    // Initialize the page
    updateStatus(false);

    // Check cameras availability on page load
    checkCamerasAvailable();

    // Check if monitoring was active when navigating away from the page
    document.addEventListener('DOMContentLoaded', () => {
      // Check cameras first
      checkCamerasAvailable().then(() => {
        const wasActive = localStorage.getItem('faceRecognitionActive') === 'true';
        if (wasActive && camerasAvailable) {
          console.log("Resuming monitoring from previous session");
          // Start the stream automatically
          startStream();
        } else if (wasActive && !camerasAvailable) {
          console.log("Cannot resume monitoring - no cameras available");
          localStorage.removeItem('faceRecognitionActive');
        }
      });
    });
  </script>
</body>
</html>