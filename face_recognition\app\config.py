import os
import yaml
from pydantic_settings import BaseSettings
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def load_yaml_config() -> dict:
    """Load configuration from YAML file based on environment"""
    # Determine which config file to use based on environment
    environment = os.getenv('ENVIRONMENT', 'development')

    if environment == 'production':
        config_path = "config.production.yaml"
    elif environment == 'local':
        config_path = "config.local.yaml"
    else:
        config_path = "config.yaml"

    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
                logger.info(f"Loaded configuration from {config_path} for {environment} environment")
                return config or {}
        else:
            logger.warning(f"Configuration file {config_path} not found, trying fallback")
            # Try fallback config file
            fallback_path = "config.yaml" if config_path != "config.yaml" else "config.production.yaml"
            if os.path.exists(fallback_path):
                with open(fallback_path, 'r') as file:
                    config = yaml.safe_load(file)
                    logger.info(f"Loaded fallback configuration from {fallback_path}")
                    return config or {}
            else:
                logger.warning("No configuration files found, using defaults")
                return {}
    except Exception as e:
        logger.error(f"Error loading configuration from {config_path}: {e}")
        return {}

# Load YAML configuration
yaml_config = load_yaml_config()

class Settings(BaseSettings):
    # Environment setting
    ENVIRONMENT: str = "development"

    # Application settings
    APP_NAME: str = yaml_config.get('app', {}).get('name', 'Face Recognition System')
    APP_VERSION: str = yaml_config.get('app', {}).get('version', '1.0.0')
    APP_HOST: str = yaml_config.get('app', {}).get('host', '0.0.0.0')
    APP_PORT: int = yaml_config.get('app', {}).get('port', 8000)
    APP_DEBUG: bool = yaml_config.get('app', {}).get('debug', False)
    APP_RELOAD: bool = yaml_config.get('app', {}).get('reload', False)

    # Database settings
    DB_USER: str = yaml_config.get('database', {}).get('user', 'root')
    DB_PASSWORD: str = ""  # Must be set via environment variable
    DB_HOST: str = yaml_config.get('database', {}).get('host', 'localhost')
    DB_NAME: str = yaml_config.get('database', {}).get('name', 'face_recognition_db')
    DB_PORT: int = yaml_config.get('database', {}).get('port', 3306)

    # Face recognition settings
    FACE_THRESHOLD: float = yaml_config.get('face_recognition', {}).get('threshold', 0.7)
    FACE_MODEL_REPO: str = yaml_config.get('face_recognition', {}).get('model_repo', 'arnabdhar/YOLOv8-Face-Detection')
    FACE_MODEL_FILENAME: str = yaml_config.get('face_recognition', {}).get('model_filename', 'model.pt')

    # Qdrant settings
    QDRANT_URL: str = yaml_config.get('qdrant', {}).get('url', 'http://localhost:6333')
    QDRANT_COLLECTION_NAME: str = yaml_config.get('qdrant', {}).get('collection_name', 'face_embeddings')
    QDRANT_VECTOR_SIZE: int = yaml_config.get('qdrant', {}).get('vector_size', 512)

    # File paths
    DATASET_PATH: str = yaml_config.get('paths', {}).get('dataset', 'Dataset')
    STATIC_PATH: str = yaml_config.get('paths', {}).get('static', 'static')
    TEMPLATES_PATH: str = yaml_config.get('paths', {}).get('templates', 'templates')

    # Logging settings
    LOG_LEVEL: str = yaml_config.get('logging', {}).get('level', 'INFO')
    LOG_FORMAT: str = yaml_config.get('logging', {}).get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Security settings
    SECRET_KEY: Optional[str] = None  # Will be set from .env

    # CORS settings
    CORS_ORIGINS: list = yaml_config.get('cors', {}).get('origins', ['*'])
    CORS_CREDENTIALS: bool = yaml_config.get('cors', {}).get('credentials', True)
    CORS_METHODS: list = yaml_config.get('cors', {}).get('methods', ['*'])
    CORS_HEADERS: list = yaml_config.get('cors', {}).get('headers', ['*'])

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra environment variables

settings = Settings()