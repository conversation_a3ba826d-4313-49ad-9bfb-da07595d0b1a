# Face Recognition System - Deployment Guide

This guide explains how to deploy the Face Recognition System in both local development and production environments.

## Configuration Overview

The application uses a two-tier configuration system:
- **YAML files**: Non-sensitive configuration (config.yaml for local, config.production.yaml for production)
- **Environment files**: Sensitive data like passwords and secrets (.env for local, .env.production for production)

## Local Development Setup

### 1. Configuration Files
- Use `config.yaml` for application settings
- Use `.env` for environment variables
- Both files are already configured for local development

### 2. Start Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python -m app.main

# Or using uvicorn directly
uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload
```

### 3. Using Docker Compose (Local)
```bash
# Start local development environment
docker-compose -f docker-compose.local.yml up -d

# View logs
docker-compose -f docker-compose.local.yml logs -f

# Stop services
docker-compose -f docker-compose.local.yml down
```

## Production Deployment

### 1. Prepare Configuration Files

#### Copy and configure production environment file:
```bash
cp .env.production .env
```

#### Edit `.env` file with your production values:
```bash
# Update these values for production
DB_PASSWORD=your-secure-production-password
SECRET_KEY=generate-a-very-secure-secret-key
DB_HOST=mysql  # or your database server IP
QDRANT_URL=http://qdrant:6333  # or your Qdrant server URL
```

#### Update `config.production.yaml`:
```yaml
cors:
  origins: ["https://yourdomain.com", "https://www.yourdomain.com"]

security:
  allowed_hosts: ["yourdomain.com", "www.yourdomain.com"]
```

### 2. Docker Compose Deployment
```bash
# Set environment to production
export ENVIRONMENT=production

# Start production services
docker-compose up -d

# View logs
docker-compose logs -f face-recognition

# Stop services
docker-compose down
```

### 3. Manual Server Deployment

#### Install dependencies:
```bash
pip install -r requirements.txt
```

#### Set environment variables:
```bash
export ENVIRONMENT=production
```

#### Run with Gunicorn (recommended for production):
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## Environment Variables Reference

### Required Environment Variables
- `ENVIRONMENT`: Set to "production" for production deployment
- `DB_PASSWORD`: Database password
- `SECRET_KEY`: Application secret key

### Optional Environment Variables
- `DB_HOST`: Database host (default from YAML)
- `DB_PORT`: Database port (default from YAML)
- `DB_USER`: Database user (default from YAML)
- `DB_NAME`: Database name (default from YAML)
- `QDRANT_URL`: Qdrant server URL (default from YAML)

## Configuration Hierarchy

The application loads configuration in this order (later values override earlier ones):
1. YAML file defaults
2. Environment variables from .env file
3. System environment variables

## Security Considerations

### For Production:
1. **Never commit .env files** to version control
2. **Generate strong SECRET_KEY** using: `python -c "import secrets; print(secrets.token_urlsafe(32))"`
3. **Use strong database passwords**
4. **Configure proper CORS origins** (not "*")
5. **Use HTTPS** in production
6. **Restrict allowed hosts**

### Database Security:
- Use dedicated database user with minimal privileges
- Enable SSL/TLS for database connections
- Regular database backups

## Monitoring and Logging

### Log Levels:
- Development: DEBUG
- Production: INFO or WARNING

### Log Files:
Logs are written to stdout by default. For production, consider:
- Redirecting to log files
- Using log rotation
- Centralized logging (ELK stack, etc.)

## Troubleshooting

### Common Issues:

1. **Configuration not loading**:
   - Check ENVIRONMENT variable is set correctly
   - Verify YAML file syntax
   - Check file permissions

2. **Database connection issues**:
   - Verify database credentials in .env
   - Check database server is running
   - Verify network connectivity

3. **Qdrant connection issues**:
   - Check QDRANT_URL in configuration
   - Verify Qdrant server is running
   - Check network connectivity

### Debug Mode:
Set `APP_DEBUG=true` in environment variables for detailed error messages (development only).

## Scaling Considerations

### For High Load:
1. **Use multiple workers**: Increase Gunicorn workers
2. **Load balancer**: Use Nginx or similar
3. **Database optimization**: Connection pooling, read replicas
4. **Caching**: Redis for session/cache data
5. **CDN**: For static files

### Resource Requirements:
- **Minimum**: 2GB RAM, 2 CPU cores
- **Recommended**: 4GB RAM, 4 CPU cores
- **Storage**: 10GB+ for models and datasets
