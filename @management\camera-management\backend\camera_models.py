# Camera Management Database Models
from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON>olean, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from pydantic import BaseModel
from datetime import datetime, timezone
from typing import Optional

Base = declarative_base()

# SQLAlchemy Models
class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    def __repr__(self):
        return f"<Camera(id={self.id}, name={self.name}, is_active={self.is_active})>"

class CameraPermission(Base):
    __tablename__ = 'camera_permissions'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    camera_name = Column(String(100), ForeignKey('cameras.name'), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="camera_permissions")
    camera = relationship("Camera", back_populates="user_permissions")

    def __repr__(self):
        return f"<CameraPermission(id={self.id}, user_id={self.user_id}, camera_name={self.camera_name})>"

# Pydantic Models for API
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

class CameraCreate(BaseModel):
    name: str
    rtsp_url: str
    is_active: Optional[bool] = True

class CameraUpdate(BaseModel):
    name: Optional[str] = None
    rtsp_url: Optional[str] = None
    is_active: Optional[bool] = None

class CameraResponse(BaseModel):
    id: int
    name: str
    rtsp_url: str
    is_active: bool

    class Config:
        orm_mode = True

class CameraPermissionCreate(BaseModel):
    user_id: int
    camera_name: str

class CameraPermissionResponse(BaseModel):
    id: int
    user_id: int
    camera_name: str
    created_at: datetime

    class Config:
        orm_mode = True
