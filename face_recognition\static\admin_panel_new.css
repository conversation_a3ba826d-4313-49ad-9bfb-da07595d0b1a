/* Professional Admin Panel - Unified Design */
:root {
    /* Professional Color Palette */
    --primary-dark: #1e293b;
    --secondary-dark: #334155;
    --tertiary-dark: #475569;
    --accent-blue: #3b82f6;
    --accent-blue-hover: #2563eb;
    --accent-cyan: #06b6d4;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --text-light: #f1f5f9;
    --text-muted: #94a3b8;
    --text-dark: #64748b;
    --border-color: #e2e8f0;
    --content-bg: #f8fafc;
    --card-bg: #ffffff;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.2s ease-in-out;
    --border-radius: 6px;
    --border-radius-lg: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--content-bg);
    color: var(--text-dark);
    line-height: 1.5;
    font-size: 14px;
}

/* Main Admin Container */
.admin-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    display: flex;
    flex-direction: column;
}

/* Professional Header */
.admin-header {
    background: var(--primary-dark);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 100;
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60px;
}

/* Logo Section */
.admin-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: var(--text-light);
}

.admin-logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-hover) 100%);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.admin-logo-icon svg {
    width: 18px;
    height: 18px;
    color: white;
}

.admin-logo-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-light);
}

.admin-logo-subtitle {
    font-size: 10px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status and Actions */
.admin-status-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.admin-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    color: var(--success-color);
}

.admin-status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.admin-back-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius);
    color: var(--accent-blue);
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: var(--transition);
}

.admin-back-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateX(-2px);
}

/* Professional Navigation */
.admin-navigation {
    background: var(--secondary-dark);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    z-index: 90;
}

.admin-nav-container {
    max-width: 1200px;
    margin: 0 auto;
}

.admin-main-nav {
    display: flex;
    align-items: center;
    padding: 0 24px;
    gap: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.admin-main-nav::-webkit-scrollbar {
    display: none;
}

/* Unified Navigation Tabs */
.admin-nav-tab {
    display: flex;
    align-items: center;
    padding: 14px 20px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    white-space: nowrap;
    font-size: 13px;
    font-weight: 500;
    border-radius: 0;
    min-width: 140px;
    justify-content: center;
    text-decoration: none;
}

.admin-nav-tab-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-nav-tab i {
    font-size: 14px;
    opacity: 0.8;
}

.admin-nav-tab:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--text-light);
}

.admin-nav-tab:hover i {
    opacity: 1;
}

.admin-nav-tab.active {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-hover) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.admin-nav-tab.active i {
    opacity: 1;
}

/* Sub-Navigation for Management */
.admin-sub-nav {
    background: var(--tertiary-dark);
    border-bottom: 1px solid var(--border-color);
    display: none;
    animation: slideDown 0.3s ease-out;
}

.admin-sub-nav.show {
    display: block;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-sub-nav-container {
    display: flex;
    align-items: center;
    padding: 0 24px;
    gap: 0;
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.admin-sub-nav-container::-webkit-scrollbar {
    display: none;
}

.admin-sub-nav-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 16px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--border-radius);
    min-width: 120px;
    justify-content: center;
}

.admin-sub-nav-tab:hover {
    background: rgba(6, 182, 212, 0.1);
    color: var(--text-light);
}

.admin-sub-nav-tab.active {
    background: linear-gradient(135deg, var(--accent-cyan) 0%, #0891b2 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.admin-sub-nav-tab i {
    font-size: 12px;
}

/* Content Area */
.admin-content {
    flex: 1;
    background: var(--content-bg);
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 120px);
}

.admin-content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
}

/* Tab Content */
.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional Cards */
.card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 16px 20px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.card-body {
    padding: 20px;
}

/* Additional Professional Styling */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-hover) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    transition: var(--transition);
    background: var(--card-bg);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--primary-dark);
    font-size: 13px;
}

/* Loading States */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--text-muted);
}

.loading i {
    font-size: 24px;
    margin-bottom: 12px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 8px;
    color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header-content {
        padding: 12px 16px;
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .admin-status-section {
        width: 100%;
        justify-content: space-between;
    }

    .admin-main-nav,
    .admin-sub-nav-container {
        padding: 0 16px;
    }

    .admin-content-container {
        padding: 16px;
    }

    .admin-nav-tab {
        min-width: 120px;
        padding: 12px 16px;
        font-size: 12px;
    }

    .admin-sub-nav-tab {
        min-width: 100px;
        padding: 10px 12px;
        font-size: 11px;
    }

    .admin-logo-title {
        font-size: 16px;
    }

    .admin-logo-subtitle {
        font-size: 9px;
    }
}
