<style>
    :root {
      --primary-color: #8b5cf6;
      --secondary-color: #7c3aed;
      --accent-color: #a855f7;
      --success-color: #10b981;
      --danger-color: #ef4444;
      --warning-color: #f59e0b;
      --info-color: #3b82f6;
      --light-color: #f8fafc;
      --dark-color: #1e293b;
      --bg-light: #f1f5f9;
      --text-dark: #1e293b;
      --text-light: #64748b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      color: var(--text-dark);
      line-height: 1.6;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    .container {
      max-width: 100%;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    .header {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
      color: white;
      padding: 20px 0;
      margin-bottom: 0;
      border-radius: 0;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .header-title {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .header-title i {
      font-size: 1.5rem;
      color: #fbbf24;
    }

    .header-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 15px;
    }

    .page-title {
      text-align: center;
      color: var(--dark-color);
      margin: 0;
      padding: 0;
      font-size: 2.5rem;
      font-weight: 700;
      display: none; /* Hide since we have header title */
    }

    .admin-container {
      padding: 0 20px 40px 20px;
    }

    /* Admin Tabs */
    .admin-tabs {
      display: flex;
      justify-content: center;
      margin: 30px auto;
      background: white;
      border-radius: 12px;
      padding: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      flex-wrap: wrap;
      gap: 4px;
      max-width: 1200px;
      border: 1px solid rgba(139, 92, 246, 0.1);
    }

    .tab-btn {
      background: transparent;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: var(--transition);
      font-weight: 600;
      /* color: var(--text-light); */
      color: #bc82fa;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.875rem;
      min-width: 140px;
      justify-content: center;
      position: relative;
    }

    .tab-btn:hover {
      background: rgba(139, 92, 246, 0.1);
      color: var(--primary-color);
      transform: translateY(-1px);
    }

    .tab-btn.active {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    }

    .tab-btn i {
      font-size: 1.1rem;
    }

    /* Tab Content */
    .tab-content {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin: 0 auto 30px auto;
      max-width: 1200px;
      border: 1px solid rgba(139, 92, 246, 0.1);
    }

    .tab-pane {
      display: none;
      padding: 40px;
      min-height: 400px;
    }

    .tab-pane.active {
      display: block;
    }

    /* Cards */
    .card {
      background: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
      margin-bottom: 20px;
      overflow: hidden;
    }

    .card-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: 20px;
      border-bottom: none;
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
    }

    .card-body {
      padding: 20px;
    }

    /* Forms */
    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      font-size: 1rem;
      transition: var(--transition);
      background: white;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    /* Buttons */
    .btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      text-decoration: none;
      justify-content: center;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-secondary {
      background: linear-gradient(135deg, var(--text-light), var(--dark-color));
      color: white;
    }

    .btn-secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(127, 140, 141, 0.3);
    }

    .btn-success {
      background: linear-gradient(135deg, var(--success-color), #229954);
      color: white;
    }

    .btn-success:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--danger-color), #c0392b);
      color: white;
    }

    .btn-danger:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--warning-color), #d68910);
      color: white;
    }

    .btn-warning:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
    }

    /* Loading and Empty States */
    .loading {
      text-align: center;
      padding: 60px 40px;
      color: var(--text-light);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 300px;
    }

    .loading i {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: var(--primary-color);
      animation: spin 1s linear infinite;
    }

    .loading p {
      font-size: 1rem;
      font-weight: 500;
      margin: 0;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: var(--text-light);
    }

    .empty-state i {
      font-size: 3rem;
      margin-bottom: 20px;
      color: var(--primary-color);
    }

    .empty-state h3 {
      font-size: 1.5rem;
      margin-bottom: 10px;
      color: var(--text-dark);
    }

    /* Header Content */
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      padding: 10px 16px;
      border-radius: 8px;
      text-decoration: none;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    /* Management Mode Styles */
    body.management-active .admin-container {
      display: none !important;
    }

    body.management-active #management-tab {
      display: block !important;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    }

    /* Cards and Content Styling - Match face_recognition.html */
    .card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(59, 130, 246, 0.1);
      margin-bottom: 1.5rem;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .card:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }

    .card-header {
      padding: 1.5rem;
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
    }

    .card-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .card-body {
      padding: 1.5rem;
    }

    /* Form Styling */
    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      background: white;
    }

    .form-control:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Button Styling - Match face_recognition.html */
    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      justify-content: center;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
      color: white;
    }

    .btn-secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }

    .btn-success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
    }

    .btn-success:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .btn-danger {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
    }

    .btn-danger:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }

    .btn-warning {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
    }

    .btn-warning:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    .btn-sm {
      padding: 0.5rem 1rem;
      font-size: 0.75rem;
    }

    /* Table Styling */
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    .table th,
    .table td {
      padding: 0.75rem 1rem;
      text-align: left;
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    }

    .table th {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
      font-weight: 600;
      color: #374151;
      font-size: 0.875rem;
    }

    .table td {
      color: #6b7280;
      font-size: 0.875rem;
    }

    .table tbody tr:hover {
      background: rgba(59, 130, 246, 0.02);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 3rem 1rem;
      color: #6b7280;
    }

    .empty-state i {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #3b82f6;
    }

    .empty-state h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #374151;
      margin: 0 0 0.5rem 0;
    }

    .empty-state p {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0 0 1.5rem 0;
    }

    /* Loading State */
    .loading {
      text-align: center;
      padding: 3rem 1rem;
      color: #6b7280;
    }

    .loading i {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: #3b82f6;
    }

    .loading p {
      font-size: 0.875rem;
      margin: 0;
    }
    
    /* Management Container Styles */
    .management-container {
      display: flex;
      height: 100vh;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
      overflow: hidden;
      position: relative;
    }

    .management-back-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      z-index: 10001;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 0.75rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }

    .management-back-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }

    #management-tab {
      display: none;
    }

    #management-tab.active {
      display: block;
    }

    /* Management Sidebar */
    .management-sidebar {
      width: 240px;
      background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
      display: flex;
      flex-direction: column;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(20px);
      border-right: 1px solid rgba(59, 130, 246, 0.1);
    }

    /* Management Content Area */
    .management-content-area {
      flex: 1;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    /* Management Sidebar Logo */
    .management-sidebar-logo {
      padding: 1rem 1.25rem;
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      background: rgba(30, 41, 59, 0.3);
      backdrop-filter: blur(10px);
    }

    .management-logo-container {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .management-logo-icon-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .management-logo-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1rem;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
      position: relative;
      overflow: hidden;
    }

    .management-logo-title {
      font-size: 1rem;
      font-weight: 700;
      color: #f8fafc;
      margin: 0;
      letter-spacing: -0.25px;
    }

    .management-logo-subtitle {
      font-size: 0.6875rem;
      color: #64748b;
      font-weight: 500;
      margin: 0;
    }

    /* Navigation Menu */
    .management-nav-container {
      flex: 1;
      padding: 0.75rem 0;
      overflow-y: auto;
    }

    .management-nav-header {
      padding: 0 1.25rem 0.5rem 1.25rem;
    }

    .management-nav-section-title {
      font-size: 0.625rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 1px;
      color: #94a3b8;
      margin-bottom: 0.5rem;
    }

    .management-nav-menu {
      padding: 0 0.5rem;
    }

    .management-nav-item {
      position: relative;
      display: block;
      margin-bottom: 0.25rem;
      border-radius: 12px;
      overflow: hidden;
      background: none;
      border: none;
      cursor: pointer;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
      width: 100%;
      text-decoration: none;
    }

    .management-nav-item-content {
      display: flex;
      align-items: center;
      gap: 0.625rem;
      padding: 0.625rem 0.875rem;
      position: relative;
      z-index: 2;
    }

    .management-nav-icon-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 8px;
      background: rgba(30, 41, 59, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.1);
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .management-nav-icon {
      font-size: 0.8rem;
      color: #cbd5e1;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .management-nav-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: #cbd5e1;
      margin: 0;
      line-height: 1.2;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .management-nav-subtitle {
      font-size: 0.6rem;
      color: #64748b;
      margin: 0;
      line-height: 1.2;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .management-nav-item:hover {
      transform: translateX(4px);
      background: rgba(30, 41, 59, 0.9);
    }

    .management-nav-item.active {
      background: rgba(59, 130, 246, 0.1);
      transform: translateX(4px);
    }

    .management-nav-item.active .management-nav-icon-container {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-color: #3b82f6;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .management-nav-item.active .management-nav-icon {
      color: white;
    }

    .management-nav-item.active .management-nav-title {
      color: #f8fafc;
      font-weight: 700;
    }

    .management-nav-item.active .management-nav-subtitle {
      color: #3b82f6;
    }

    /* Management Content Header */
    .management-content-header {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      padding: 1.5rem 2rem;
      backdrop-filter: blur(10px);
    }

    .management-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .management-page-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #f8fafc;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .management-page-subtitle {
      font-size: 0.875rem;
      color: #cbd5e1;
      margin: 0.25rem 0 0 0;
    }

    .management-header-stats {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .management-stat-card {
      background: rgba(30, 41, 59, 0.5);
      border: 1px solid rgba(59, 130, 246, 0.1);
      border-radius: 12px;
      padding: 0.75rem 1rem;
      text-align: center;
      backdrop-filter: blur(10px);
    }

    .management-stat-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: #3b82f6;
      margin: 0;
    }

    .management-stat-label {
      font-size: 0.75rem;
      color: #64748b;
      margin: 0;
    }

    /* Management Content Body */
    .management-content-body {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
      background: rgba(15, 23, 42, 0.3);
    }

    .management-module-section {
      display: none;
    }

    .management-module-section.active {
      display: block;
    }

    /* Management Cards */
    .management-premium-card {
      background: rgba(30, 41, 59, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.1);
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      backdrop-filter: blur(20px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.05);
    }

    .management-card-header {
      margin-bottom: 1.5rem;
    }

    .management-card-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.5rem;
    }

    .management-card-title h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #f8fafc;
      margin: 0;
    }

    .management-card-description {
      font-size: 0.875rem;
      color: #cbd5e1;
      margin: 0;
    }

    /* Management Forms */
    .management-form-premium {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .management-form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .management-form-label {
      font-size: 0.875rem;
      font-weight: 600;
      color: #f8fafc;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .management-form-input {
      background: rgba(15, 23, 42, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 8px;
      padding: 0.75rem 1rem;
      color: #f8fafc;
      font-size: 0.875rem;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .management-form-input:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .management-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 0.75rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .management-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .management-btn-full {
      width: 100%;
    }

    /* Empty State */
    .management-empty-state {
      text-align: center;
      padding: 3rem 1rem;
      color: #64748b;
    }

    .management-empty-state i {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #3b82f6;
    }

    .management-empty-state h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #cbd5e1;
      margin: 0 0 0.5rem 0;
    }

    .management-empty-state p {
      font-size: 0.875rem;
      color: #64748b;
      margin: 0;
    }

    /* Notification Styles - Exactly like crowd_detection */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      min-width: 300px;
      max-width: 500px;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      font-family: 'Inter', sans-serif;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification-success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
    }

    .notification-error {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
    }

    .notification-warning {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
    }

    .notification-info {
      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
      color: white;
    }

    .notification-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .notification-content i {
      font-size: 18px;
      flex-shrink: 0;
    }

    .notification-content span {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
    }
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #1abc9c;
      --danger-color: #e74c3c;
      --warning-color: #f39c12;
      --success-color: #2ecc71;
      --text-light: #ecf0f1;
      --text-dark: #2c3e50;
      --bg-light: #f5f7fa;
      --bg-dark: #34495e;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    .header {
      background-color: var(--primary-color);
      color: var(--text-light);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--shadow);
    }

    .header-title {
      font-size: 20px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #c0392b;
      transform: translateY(-2px);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: #27ae60;
      transform: translateY(-2px);
    }

    .page-title {
      text-align: center;
      margin: 30px 0;
      color: var(--primary-color);
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);
    }

    .page-title:after {
      content: '';
      position: absolute;
      width: 60px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px 40px;
    }

    .card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .card-images img {
      max-width: 140px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .card-images img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .form-container {
      background-color: var(--bg-light);
      padding: 20px;
      border-radius: 8px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: white;
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .empty-state {
      text-align: center;
      padding: 50px 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .empty-state i {
      font-size: 48px;
      color: var(--secondary-color);
      margin-bottom: 20px;
    }

    .empty-state h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #666;
      margin-bottom: 20px;
    }

    .loading {
      text-align: center;
      padding: 30px;
      color: var(--primary-color);
    }

    /* Admin Tabs */
    .admin-tabs {
      display: flex;
      background-color: var(--bg-light);
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 25px;
      box-shadow: var(--shadow);
    }



    .tab-content {
      position: relative;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    /* Form Styling */
    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: var(--bg-light);
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .file-input-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .file-status {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }

    /* Camera List Section */
    .camera-list-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-header h3 {
      font-size: 18px;
      color: var(--primary-color);
      margin: 0;
    }

    .camera-list, .users-list {
      background-color: var(--bg-light);
      border-radius: 8px;
      padding: 15px;
      min-height: 100px;
    }

    .camera-table, .users-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .camera-table th, .users-table th,
    .camera-table td, .users-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .camera-table th, .users-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .camera-table th:first-child, .users-table th:first-child {
      border-top-left-radius: 8px;
    }

    .camera-table th:last-child, .users-table th:last-child {
      border-top-right-radius: 8px;
    }

    .camera-table tr:last-child td, .users-table tr:last-child td {
      border-bottom: none;
    }

    .camera-table tr:hover, .users-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    .camera-url, .user-email {
      font-size: 12px;
      color: #666;
      word-break: break-all;
    }

    .btn-sm {
      padding: 6px 10px;
      font-size: 12px;
    }

    /* User Management Styles */
    .user-management-controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .search-box {
      display: flex;
      gap: 10px;
      width: 60%;
    }

    .search-box input {
      flex-grow: 1;
    }

    .user-actions {
      display: flex;
      gap: 5px;
    }

    .user-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #eee;
      transition: var(--transition);
    }

    .user-image:hover {
      transform: scale(1.1);
      border-color: var(--secondary-color);
    }

    /* Attendance section styling */
    .attendance-section {
      margin: 15px 0;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .attendance-section h4 {
      margin-top: 0;
      margin-bottom: 10px;
      color: var(--primary-color);
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .attendance-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .attendance-table th,
    .attendance-table td {
      padding: 8px 12px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }

    .attendance-table th {
      background-color: var(--primary-color);
      color: white;
    }

    .attendance-table tr:last-child td {
      border-bottom: none;
    }

    .attendance-table tr:hover td {
      background-color: rgba(52, 152, 219, 0.05);
    }

    /* Clustering Styles */
    .unknown-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      background-color: white;
      padding: 15px;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .control-buttons {
      display: flex;
      gap: 10px;
    }

    .cluster-settings {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .cluster-settings .form-group {
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .cluster-settings .form-control {
      width: 70px;
      padding: 8px;
    }

    .cluster-settings label {
      margin-bottom: 0;
      white-space: nowrap;
      cursor: help;
    }

    .cluster-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.85rem;
      color: #666;
      background-color: rgba(243, 156, 18, 0.1);
      padding: 6px 10px;
      border-radius: 4px;
      border-left: 3px solid var(--warning-color);
    }

    .cluster-card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
      border-left: 5px solid var(--secondary-color);
    }

    .cluster-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .cluster-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .cluster-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .cluster-count {
      background-color: var(--secondary-color);
      color: white;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .cluster-faces {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .cluster-faces img {
      max-width: 120px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .cluster-faces img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Modal Styling */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      animation: fadeIn 0.3s ease;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 30px;
      border: none;
      width: 500px;
      max-width: 90%;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      position: relative;
      animation: slideIn 0.3s ease;
    }

    .modal-content h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 25px;
      text-align: center;
      position: relative;
    }

    .modal-content h3:after {
      content: '';
      position: absolute;
      width: 50px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .modal-content button {
      margin-bottom: 10px;
    }

    /* Camera Container */
    #camera-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 2000;
      transition: opacity 0.3s ease, visibility 0.3s ease;
      display: none;
    }

    .camera-content {
      background-color: white;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 500px;
      width: 90%;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .camera-content h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 24px;
    }

    .camera-buttons {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 15px;
      margin-bottom: 15px;
    }

    #video, #image-preview {
      width: 100%;
      max-width: 400px;
      border-radius: 8px;
      box-shadow: var(--shadow);
      background-color: #f0f0f0;
      margin: 0 auto;
      display: block;
    }

    #video {
      height: 300px;
      object-fit: cover;
    }

    #image-preview {
      margin-top: 15px;
      border: 3px solid var(--success-color);
      max-height: 300px;
      object-fit: contain;
    }

    /* Only apply animation to the spinner icon */
    .fa-spinner.fa-spin {
      font-size: 40px;
      animation: spin 1s linear infinite;
    }

    /* Explicitly disable animation for all other icons */
    .fas:not(.fa-spin) {
      animation: none !important;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Confirmation Dialog */
    .confirm-dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1100;
      display: none;
      justify-content: center;
      align-items: center;
    }

    .confirm-dialog::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      backdrop-filter: blur(3px);
    }

    .confirm-dialog h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: var(--primary-color);
      position: relative;
      font-size: 24px;
    }

    .confirm-dialog p {
      margin-bottom: 20px;
      color: var(--text-dark);
      position: relative;
      font-size: 16px;
      line-height: 1.5;
    }

    .confirm-dialog-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      width: 400px;
      max-width: 90%;
      text-align: center;
      position: relative;
      z-index: 1101;
      animation: dialogFadeIn 0.3s ease;
    }

    @keyframes dialogFadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .confirm-dialog-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      position: relative;
      margin-top: 25px;
    }

    .confirm-dialog-buttons button {
      min-width: 100px;
      padding: 10px 15px;
      font-weight: 500;
    }

    /* Toast Notification */
    .toast {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      background-color: var(--primary-color);
      color: white;
      padding: 12px 25px;
      border-radius: 8px;
      z-index: 1200;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: opacity 0.3s, transform 0.3s;
      pointer-events: none;
    }

    .toast.show {
      opacity: 1;
      transform: translate(-50%, -10px);
    }

    .toast.success {
      background-color: var(--success-color);
    }

    .toast.error {
      background-color: var(--danger-color);
    }

    .toast.info {
      background-color: var(--secondary-color);
    }

    /* Responsive Design */
    /* Card Images and Image Container Styles */
    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
    }

    .image-container {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .image-container:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .card-images img, .cluster-faces img, .image-container img {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #eee;
      transition: var(--transition);
    }

    .image-actions {
      position: absolute;
      top: 5px;
      right: 5px;
      display: flex;
      gap: 5px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .image-container:hover .image-actions {
      opacity: 1;
    }

    .delete-image-btn, .move-image-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .delete-image-btn {
      background-color: rgba(220, 53, 69, 0.8);
    }

    .move-image-btn {
      background-color: rgba(0, 123, 255, 0.8);
    }

    .delete-image-btn i, .move-image-btn i {
      font-size: 12px;
    }

    .delete-image-btn:hover {
      background-color: rgba(220, 53, 69, 1);
    }

    .move-image-btn:hover {
      background-color: rgba(0, 123, 255, 1);
    }

    /* Cluster options in move modal */
    .clusters-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 15px 0;
      max-height: 300px;
      overflow-y: auto;
    }

    .cluster-option {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background-color: var(--light-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
      font-size: 14px;
    }

    .cluster-option:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .cluster-option i {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    /* Settings Tiles */
    .settings-tiles {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 25px;
    }

    .settings-tile {
      flex: 1;
      min-width: 150px;
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      box-shadow: var(--shadow);
      cursor: pointer;
      transition: var(--transition);
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .settings-tile i {
      font-size: 24px;
      color: var(--primary-color);
    }

    .settings-tile span {
      font-weight: 500;
    }

    .settings-tile:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .settings-tile.active {
      border-color: var(--secondary-color);
      background-color: rgba(52, 152, 219, 0.05);
    }

    .settings-section {
      display: none;
      animation: fadeIn 0.3s ease;
    }

    .settings-section.active {
      display: block;
    }

    /* Settings Sidebar */
    .settings-sidebar {
      display: flex;
      flex-direction: column;
      width: 250px;
      background-color: var(--bg-light);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: var(--shadow);
      margin-right: 25px;
      float: left;
    }

    .settings-sidebar-item {
      padding: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      transition: var(--transition);
      border-left: 3px solid transparent;
    }

    .settings-sidebar-item i {
      font-size: 18px;
      width: 24px;
      text-align: center;
      color: var(--primary-color);
    }

    .settings-sidebar-item:hover {
      background-color: rgba(52, 152, 219, 0.1);
    }

    .settings-sidebar-item.active {
      background-color: white;
      border-left-color: var(--secondary-color);
      font-weight: 500;
    }

    .settings-subsection {
      display: none;
      animation: fadeIn 0.3s ease;
      margin-left: 275px;
    }

    .settings-subsection.active {
      display: block;
    }

    @media (max-width: 768px) {
      .settings-sidebar {
        width: 100%;
        float: none;
        margin-right: 0;
        margin-bottom: 20px;
        flex-direction: row;
        overflow-x: auto;
      }

      .settings-sidebar-item {
        border-left: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
      }

      .settings-sidebar-item.active {
        border-left-color: transparent;
        border-bottom-color: var(--secondary-color);
      }

      .settings-subsection {
        margin-left: 0;
      }
    }

    /* Webhook, WhatsApp, SMS, and Email Lists */
    .webhook-list,
    .whatsapp-list,
    .sms-list,
    .email-list {
      background-color: var(--bg-light);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }

    .webhook-table,
    .whatsapp-table,
    .sms-table,
    .email-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .webhook-table th,
    .webhook-table td,
    .whatsapp-table th,
    .whatsapp-table td,
    .sms-table th,
    .sms-table td,
    .email-table th,
    .email-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .webhook-table th,
    .whatsapp-table th,
    .sms-table th,
    .email-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .webhook-table th:first-child,
    .whatsapp-table th:first-child,
    .sms-table th:first-child,
    .email-table th:first-child {
      border-top-left-radius: 8px;
    }

    .webhook-table th:last-child,
    .whatsapp-table th:last-child,
    .sms-table th:last-child,
    .email-table th:last-child {
      border-top-right-radius: 8px;
    }

    .webhook-table tr:last-child td,
    .whatsapp-table tr:last-child td,
    .sms-table tr:last-child td,
    .email-table tr:last-child td {
      border-bottom: none;
    }

    .webhook-table tr:hover,
    .whatsapp-table tr:hover,
    .sms-table tr:hover,
    .email-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    .webhook-url,
    .whatsapp-phone,
    .sms-phone,
    .email-address {
      font-size: 14px;
      word-break: break-all;
      font-family: monospace;
    }

    .webhook-description,
    .whatsapp-name,
    .sms-name,
    .email-name {
      font-size: 14px;
      color: #666;
    }

    .webhook-template {
      font-size: 12px;
      font-family: monospace;
      max-width: 200px;
      word-break: break-word;
    }

    .webhook-template span {
      cursor: help;
    }

    .webhook-status,
    .whatsapp-status,
    .sms-status,
    .email-status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .webhook-status.active,
    .whatsapp-status.active,
    .sms-status.active,
    .email-status.active {
      background-color: rgba(46, 204, 113, 0.2);
      color: #27ae60;
    }

    .webhook-status.inactive,
    .whatsapp-status.inactive,
    .sms-status.inactive,
    .email-status.inactive {
      background-color: rgba(231, 76, 60, 0.2);
      color: #c0392b;
    }

    .webhook-actions,
    .whatsapp-actions,
    .sms-actions,
    .email-actions {
      display: flex;
      gap: 5px;
    }

    @media (max-width: 768px) {
      .form-actions {
        flex-direction: column;
      }

      .card-images img, .image-container, .cluster-faces img, .unknown-image {
        max-width: 100px;
        max-height: 100px;
      }

      .settings-tiles {
        flex-direction: column;
      }

      .settings-tile {
        min-width: 100%;
      }
    }

    /* Template Editor Styles - Compact Professional Layout */
    .template-editor-compact {
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: white;
      padding: 20px;
    }

    .template-layout {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
      align-items: start;
    }

    .template-input-column {
      display: flex;
      flex-direction: column;
    }

    .template-input-column label {
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--primary-color);
    }

    .template-textarea {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      resize: vertical;
      min-height: 160px;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .template-reference-column {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      height: fit-content;
    }

    .variables-reference h6 {
      margin: 0 0 10px 0;
      color: var(--primary-color);
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .variables-compact {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 20px;
    }

    .var-tag {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      font-weight: bold;
      user-select: all;
      cursor: text;
      border: 1px solid #bbdefb;
    }

    .example-section {
      margin-bottom: 20px;
    }

    .example-template {
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
    }

    .example-template pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      color: #333;
      line-height: 1.4;
    }

    .preview-section {
      margin-bottom: 0;
    }

    .template-preview-compact {
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      min-height: 60px;
      max-height: 120px;
      overflow-y: auto;
    }

    .template-preview-compact pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      white-space: pre-wrap;
      word-wrap: break-word;
      color: #333;
    }



    /* Webhook Header Styles */
    .webhook-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      border: 1px solid #dee2e6;
    }

    .section-info h3 {
      margin: 0 0 5px 0;
      color: var(--primary-color);
      font-size: 24px;
    }

    .section-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    /* Webhook Form Section Styles */
    .webhook-form-section {
      background-color: white;
      border: 1px solid #dee2e6;
      border-radius: 12px;
      margin-bottom: 25px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-bottom: 1px solid #dee2e6;
      border-radius: 12px 12px 0 0;
    }

    .form-header h4 {
      margin: 0;
      color: var(--primary-color);
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .webhook-form-section form {
      padding: 25px;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #dee2e6;
    }

    /* Webhook Cards Styles */
    .webhook-cards {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-top: 20px;
    }

    .webhook-card {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .webhook-card:hover {
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
      transform: translateY(-2px);
    }

    .webhook-card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #dee2e6;
    }

    .webhook-info {
      flex: 1;
    }

    .webhook-details {
      display: flex;
      gap: 15px;
      margin-top: 8px;
      flex-wrap: wrap;
    }

    .webhook-method,
    .webhook-headers {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 4px 8px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-size: 0.85em;
      color: #495057;
    }

    .webhook-method {
      background-color: #e3f2fd;
      border-color: #bbdefb;
      color: #1976d2;
    }

    /* Method-specific colors for webhook cards */
    .webhook-method-get {
      background-color: rgba(97, 175, 254, 0.1);
      border-color: #61affe;
      color: #61affe;
    }

    .webhook-method-post {
      background-color: rgba(73, 204, 144, 0.1);
      border-color: #49cc90;
      color: #49cc90;
    }

    .webhook-method-put {
      background-color: rgba(252, 161, 48, 0.1);
      border-color: #fca130;
      color: #fca130;
    }

    .webhook-method-patch {
      background-color: rgba(80, 227, 194, 0.1);
      border-color: #50e3c2;
      color: #50e3c2;
    }

    .webhook-method-delete {
      background-color: rgba(249, 62, 62, 0.1);
      border-color: #f93e3e;
      color: #f93e3e;
    }

    .webhook-headers {
      background-color: #f3e5f5;
      border-color: #ce93d8;
      color: #7b1fa2;
    }

    /* Postman-style URL container */
    .postman-url-container {
      display: flex;
      border: 1px solid #ced4da;
      border-radius: 4px;
      overflow: hidden;
      background-color: white;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .postman-url-container:focus-within {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .method-dropdown {
      border: none;
      background-color: #f8f9fa;
      color: #495057;
      font-weight: 600;
      font-size: 14px;
      padding: 8px 12px;
      min-width: 100px;
      border-right: 1px solid #dee2e6;
      outline: none;
      cursor: pointer;
      text-align: center;
    }

    .method-dropdown:focus {
      background-color: #e9ecef;
      outline: none;
    }

    .method-dropdown option {
      font-weight: 600;
      padding: 5px;
    }

    .url-input {
      flex: 1;
      border: none;
      padding: 8px 12px;
      font-size: 14px;
      outline: none;
      background-color: white;
    }

    .url-input:focus {
      outline: none;
    }

    .url-input::placeholder {
      color: #6c757d;
      font-style: italic;
    }

    /* Method-specific colors (like Postman) */
    .method-dropdown option[value="GET"] {
      color: #61affe;
    }

    .method-dropdown option[value="POST"] {
      color: #49cc90;
    }

    .method-dropdown option[value="PUT"] {
      color: #fca130;
    }

    .method-dropdown option[value="PATCH"] {
      color: #50e3c2;
    }

    .method-dropdown option[value="DELETE"] {
      color: #f93e3e;
    }

    .webhook-url-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      font-family: 'Courier New', monospace;
      word-break: break-all;
    }

    .webhook-description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }

    .webhook-status-badge {
      margin-left: 20px;
    }

    .webhook-status {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .webhook-status.active {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .webhook-status.inactive {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .webhook-template-section {
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
    }

    .webhook-template-section h5 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .template-preview-card {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      position: relative;
    }

    .template-content {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #333;
      line-height: 1.4;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 150px;
      overflow-y: auto;
    }

    .view-full-template {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 4px 8px;
      font-size: 11px;
    }

    .webhook-actions {
      padding: 20px;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      background-color: #f8f9fa;
    }

    .webhook-actions .btn {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .template-layout {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .template-reference-column {
        order: -1;
      }

      .variables-compact {
        justify-content: center;
      }

      .webhook-card-header {
        flex-direction: column;
        gap: 15px;
      }

      .webhook-status-badge {
        margin-left: 0;
        align-self: flex-start;
      }

      .webhook-actions {
        flex-direction: column;
        gap: 8px;
      }

      .webhook-actions .btn {
        justify-content: center;
      }
    }

    /* Template Modal Styles */
    .template-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .template-modal-content {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 80%;
      max-height: 80%;
      width: 600px;
      display: flex;
      flex-direction: column;
    }

    .template-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px 12px 0 0;
    }

    .template-modal-header h3 {
      margin: 0;
      color: var(--primary-color);
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .template-modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .template-modal-close:hover {
      background-color: #f8f9fa;
      color: #333;
    }

    .template-modal-body {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
    }

    .template-modal-code {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      color: #333;
      line-height: 1.5;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #dee2e6;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .template-modal-footer {
      padding: 20px;
      border-top: 1px solid #dee2e6;
      display: flex;
      justify-content: flex-end;
      background-color: #f8f9fa;
      border-radius: 0 0 12px 12px;
    }

    /* Camera Permissions Modal Styles */
    .camera-permissions-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .camera-permissions-content {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
    }

    .camera-permissions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
      border-radius: 12px 12px 0 0;
    }

    .camera-permissions-header h3 {
      margin: 0;
      color: #2e7d32;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .camera-permissions-body {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
    }

    .camera-permissions-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .camera-permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background-color: #f8f9fa;
      transition: all 0.2s ease;
    }

    .camera-permission-item:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
    }

    .camera-info {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .camera-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .camera-url {
      font-size: 12px;
      color: #666;
      font-family: monospace;
      word-break: break-all;
    }

    .permission-toggle {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .permission-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }

    .permission-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .permission-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }

    .permission-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .permission-slider {
      background-color: #4caf50;
    }

    input:checked + .permission-slider:before {
      transform: translateX(26px);
    }

    .permission-status {
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .permission-status.allowed {
      color: #4caf50;
    }

    .permission-status.denied {
      color: #f44336;
    }

    .camera-permissions-footer {
      padding: 20px;
      border-top: 1px solid #dee2e6;
      display: flex;
      justify-content: space-between;
      background-color: #f8f9fa;
      border-radius: 0 0 12px 12px;
    }

    .permissions-summary {
      display: flex;
      align-items: center;
      gap: 15px;
      font-size: 14px;
      color: #666;
    }

    .summary-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .summary-count {
      font-weight: 600;
      color: #333;
    }
  </style>