# Face Recognition System Configuration - LOCAL DEVELOPMENT
# This file contains non-sensitive configuration settings for local development

app:
  name: "Face Recognition System"
  version: "1.0.0"
  host: "127.0.0.1"
  port: 8000
  debug: true
  reload: true

database:
  user: "root"
  host: "host.docker.internal"  # Access host MySQL from Docker container
  name: "face_recognition_db"
  port: 3306  # Using existing local MySQL from MySQL Workbench
  # Password is set via environment variable DB_PASSWORD

face_recognition:
  threshold: 0.6
  model_repo: "arnabdhar/YOLOv8-Face-Detection"
  model_filename: "model.pt"

qdrant:
  url: "http://localhost:6333"
  collection_name: "face_embeddings"
  vector_size: 512

paths:
  dataset: "Dataset"
  static: "static"
  templates: "templates"

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

cors:
  origins: ["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8000", "http://127.0.0.1:8000", "http://localhost:8001", "http://127.0.0.1:8001"]
  credentials: true
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  headers: ["*"]
