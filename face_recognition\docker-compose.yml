services:
  # Face Recognition Application
  face-recognition:
    build: .
    container_name: face-recognition-app
    runtime: nvidia
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    env_file:
      - .env.production
    volumes:
      - ./Dataset:/app/Dataset
      - ./static:/app/static
      - ./templates:/app/templates
      - ./config.production.yaml:/app/config.production.yaml
    depends_on:
      - mysql
      - qdrant
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    restart: unless-stopped
    networks:
      - face-recognition-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: face-recognition-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - face-recognition-network

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: face-recognition-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - face-recognition-network

  # # Redis (optional, for caching)
  # redis:
  #   image: redis:7-alpine
  #   container_name: face-recognition-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - face-recognition-network

  # Nginx (optional, for reverse proxy)
  # nginx:
  #   image: nginx:alpine
  #   container_name: face-recognition-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #     - ./ssl:/etc/nginx/ssl
  #   depends_on:
  #     - face-recognition
  #   restart: unless-stopped
  #   networks:
  #     - face-recognition-network

volumes:
  mysql_data:
  qdrant_data:
  # redis_data:

networks:
  face-recognition-network:
    driver: bridge
