# Configuration Management Guide

This guide explains where and how to change different configuration settings in the Face Recognition System.

## Configuration Hierarchy

The application loads configuration in this order (later values override earlier ones):
1. **YAML file defaults** (config.yaml or config.production.yaml)
2. **Environment variables from .env file**
3. **System environment variables**

## Where to Change What

### 🔧 **Non-Sensitive Application Settings** → Change in YAML files

**Local Development**: Edit `config.yaml`
**Production**: Edit `config.production.yaml`

Examples of settings to change in YAML:
- Application name, version
- Host and port (if not using environment variables)
- Face recognition threshold
- File paths
- Logging levels
- CORS origins
- Qdrant collection settings

```yaml
# Example: config.yaml
app:
  name: "My Face Recognition System"
  host: "127.0.0.1"
  port: 8080
  debug: true

face_recognition:
  threshold: 0.8  # Change recognition sensitivity

logging:
  level: "DEBUG"  # Change log level

cors:
  origins: ["http://localhost:3000", "http://localhost:8080"]
```

### 🔐 **Sensitive Data & Secrets** → Change in .env files

**Local Development**: Edit `.env`
**Production**: Edit `.env` (copy from `.env.production` template)

Examples of settings to change in .env:
- Database passwords
- Secret keys
- API tokens
- SSL certificate paths

```bash
# Example: .env
DB_PASSWORD=your-new-database-password
SECRET_KEY=your-new-secret-key
HUGGINGFACE_TOKEN=your-api-token
```

### 🌍 **Environment Variables Override** → Set system environment variables

You can override any setting using environment variables:

```bash
# Override YAML settings
export APP_HOST=0.0.0.0
export APP_PORT=9000
export FACE_THRESHOLD=0.6

# Override database settings
export DB_HOST=remote-database-server
export DB_PORT=5432
```

## Common Configuration Changes

### 1. **Change Database Connection**

**For different database server:**
```yaml
# In config.yaml or config.production.yaml
database:
  host: "your-database-server.com"
  port: 3306
  user: "your-db-user"
  name: "your-database-name"
```

**For database password:**
```bash
# In .env file
DB_PASSWORD=your-new-password
```

### 2. **Change Application Port**

**Method 1: YAML file**
```yaml
# In config.yaml
app:
  port: 9000
```

**Method 2: Environment variable**
```bash
# In .env file or system environment
APP_PORT=9000
```

### 3. **Change Face Recognition Sensitivity**

```yaml
# In config.yaml or config.production.yaml
face_recognition:
  threshold: 0.8  # Higher = more strict, Lower = more lenient
```

### 4. **Change Qdrant Server**

```yaml
# In config.yaml or config.production.yaml
qdrant:
  url: "http://your-qdrant-server:6333"
```

### 5. **Change CORS Settings for Production**

```yaml
# In config.production.yaml
cors:
  origins: ["https://yourdomain.com", "https://www.yourdomain.com"]
  credentials: true
```

### 6. **Change File Paths**

```yaml
# In config.yaml or config.production.yaml
paths:
  dataset: "/custom/path/to/Dataset"
  static: "/custom/path/to/static"
  templates: "/custom/path/to/templates"
```

## Environment-Specific Changes

### **Local Development Changes**
1. Edit `config.yaml` for application settings
2. Edit `.env` for sensitive data
3. Keep debug mode enabled

### **Production Changes**
1. Edit `config.production.yaml` for application settings
2. Copy `.env.production` to `.env` and edit for sensitive data
3. Ensure debug mode is disabled
4. Set proper CORS origins
5. Use strong passwords and secret keys

## Quick Reference: File Locations

| Setting Type | Local Development | Production |
|-------------|------------------|------------|
| App Settings | `config.yaml` | `config.production.yaml` |
| Secrets | `.env` | `.env` (from `.env.production`) |
| Docker Settings | `docker-compose.local.yml` | `docker-compose.yml` |

## Best Practices

### ✅ **DO:**
- Change non-sensitive settings in YAML files
- Change passwords and secrets in .env files
- Use environment variables for deployment-specific overrides
- Keep .env files out of version control
- Use strong passwords in production

### ❌ **DON'T:**
- Hardcode passwords in YAML files
- Commit .env files to git
- Use the same passwords for development and production
- Leave debug mode enabled in production

## Validation

After making changes, you can validate your configuration:

```bash
# Check if configuration loads correctly
python -c "from app.config import settings; print('Config loaded successfully')"

# Start with configuration validation
python start.py --env development --skip-checks
```

## Troubleshooting

### **Configuration not loading:**
1. Check YAML syntax with online validator
2. Verify file permissions
3. Check ENVIRONMENT variable is set correctly

### **Database connection issues:**
1. Verify DB_PASSWORD in .env file
2. Check database host and port in YAML
3. Ensure database server is running

### **Environment variable not working:**
1. Check variable name matches exactly (case-sensitive)
2. Restart application after changing .env file
3. Verify .env file is in the correct location
