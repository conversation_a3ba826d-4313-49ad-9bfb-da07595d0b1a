# Face Recognition System - Git Ignore File

# CRITICAL: Environment files (NEVER commit these - contain passwords and secrets)
# .env
# .env.local
# .env.production
# .env.staging

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Application specific
Dataset/*/
!Dataset/.gitkeep
temp/
tmp/

# Model files (large files)
*.pt
*.pth
*.onnx
*.h5
*.pkl

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Local configuration overrides
config.local.yaml
config.override.yaml

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Package files
*.jar

# Applications
*.app
*.exe
*.war

