# Camera Management Service

This folder contains all code related to camera management functionality.

## Files Structure:
- **html/** - HTML templates for camera management UI
- **css/** - CSS styles for camera management components
- **js/** - JavaScript functions for camera operations
- **backend/** - Backend API endpoints for camera CRUD operations

## Functionality:
- Add new cameras with RTSP URLs
- Edit existing camera configurations
- Delete cameras
- Toggle camera active/inactive status
- List all configured cameras
- Real-time camera status monitoring

## API Endpoints:
- POST /add-camera - Add new camera
- GET /get-cameras - Get all cameras
- PUT /update-camera/{name} - Update camera
- DELETE /delete-camera/{name} - Delete camera

## Database Models:
- Camera model with name, rtsp_url, is_active fields
