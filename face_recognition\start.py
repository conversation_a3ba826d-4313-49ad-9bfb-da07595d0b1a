#!/usr/bin/env python3
"""
Face Recognition System Startup Script
This script provides an easy way to start the application with proper configuration.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required files exist"""
    required_files = [
        "app/main.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"Error: Missing required files: {', '.join(missing_files)}")
        sys.exit(1)

def check_config_files(environment):
    """Check if configuration files exist"""
    if environment == "production":
        config_file = "config.production.yaml"
        env_file = ".env"
    else:
        config_file = "config.yaml"
        env_file = ".env"
    
    if not Path(config_file).exists():
        print(f"Warning: Configuration file {config_file} not found")
        print("The application will use default settings")
    
    if not Path(env_file).exists():
        print(f"Warning: Environment file {env_file} not found")
        if environment == "production":
            print("For production deployment, please create .env file from .env.production template")
        else:
            print("For local development, you may need to create .env file")

def install_dependencies():
    """Install Python dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        sys.exit(1)

def start_application(environment, host, port, reload, workers):
    """Start the application"""
    # Set environment variable
    os.environ["ENVIRONMENT"] = environment
    
    print(f"Starting Face Recognition System...")
    print(f"Environment: {environment}")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Reload: {reload}")
    
    if environment == "production" and workers > 1:
        # Use Gunicorn for production with multiple workers
        try:
            import gunicorn
            cmd = [
                "gunicorn",
                "app.main:app",
                "-w", str(workers),
                "-k", "uvicorn.workers.UvicornWorker",
                "--bind", f"{host}:{port}",
                "--access-logfile", "-",
                "--error-logfile", "-"
            ]
            print(f"Starting with Gunicorn: {' '.join(cmd)}")
            subprocess.run(cmd)
        except ImportError:
            print("Gunicorn not installed. Installing...")
            subprocess.run([sys.executable, "-m", "pip", "install", "gunicorn"])
            subprocess.run(cmd)
    else:
        # Use Uvicorn for development or single worker
        try:
            import uvicorn
            uvicorn.run(
                "app.main:app",
                host=host,
                port=port,
                reload=reload,
                log_level="info" if environment == "production" else "debug"
            )
        except ImportError:
            print("Uvicorn not installed. Please install requirements.txt")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Face Recognition System Startup Script")
    parser.add_argument("--env", choices=["development", "production"], 
                       default="development", help="Environment to run in")
    parser.add_argument("--host", default="127.0.0.1", 
                       help="Host to bind to (default: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, 
                       help="Port to bind to (default: 8000)")
    parser.add_argument("--reload", action="store_true", 
                       help="Enable auto-reload (development only)")
    parser.add_argument("--workers", type=int, default=1, 
                       help="Number of worker processes (production only)")
    parser.add_argument("--install-deps", action="store_true", 
                       help="Install dependencies before starting")
    parser.add_argument("--skip-checks", action="store_true", 
                       help="Skip file checks")
    
    args = parser.parse_args()
    
    # Override host for production
    if args.env == "production" and args.host == "127.0.0.1":
        args.host = "0.0.0.0"
    
    # Disable reload for production
    if args.env == "production":
        args.reload = False
    
    if not args.skip_checks:
        check_requirements()
        check_config_files(args.env)
    
    if args.install_deps:
        install_dependencies()
    
    start_application(args.env, args.host, args.port, args.reload, args.workers)

if __name__ == "__main__":
    main()
