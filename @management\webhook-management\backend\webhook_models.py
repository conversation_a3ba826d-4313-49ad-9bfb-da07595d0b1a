# Webhook Management Database Models
from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel
from datetime import datetime, timezone
from typing import Optional

Base = declarative_base()

# SQLAlchemy Models
class Webhook(Base):
    __tablename__ = 'webhooks'

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String(255), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    http_method = Column(String(10), default='POST')
    headers = Column(Text, nullable=True)
    body_template = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Webhook(id={self.id}, url={self.url}, method={self.http_method}, active={self.is_active})>"

# Pydantic Models for API
class WebhookCreate(BaseModel):
    url: str
    description: Optional[str] = None
    http_method: Optional[str] = 'POST'
    headers: Optional[dict] = None
    body_template: Optional[str] = None

class WebhookUpdate(BaseModel):
    url: Optional[str] = None
    description: Optional[str] = None
    http_method: Optional[str] = None
    headers: Optional[dict] = None
    body_template: Optional[str] = None
    is_active: Optional[bool] = None

class WebhookResponse(BaseModel):
    id: int
    url: str
    description: Optional[str]
    http_method: str
    headers: Optional[str]
    body_template: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
