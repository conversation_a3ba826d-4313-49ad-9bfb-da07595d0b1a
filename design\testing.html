<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Admin Panel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 25%, #dee2e6 50%, #ced4da 75%, #adb5bd 100%);
            min-height: 100vh;
            color: #212529;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-bottom: 3px solid #3498db;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(44, 62, 80, 0.2);
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #ffffff;
            font-size: 1.75rem;
            font-weight: 600;
        }

        .header-title i {
            color: #3498db;
            font-size: 2rem;
        }

        .back-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: 2px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f618d);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .nav-tabs {
            background: #ffffff;
            margin: 2rem;
            border-radius: 12px 12px 0 0;
            display: flex;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .nav-tab {
            flex: 1;
            padding: 1.2rem 1.5rem;
            background: #f8f9fa;
            border: none;
            border-right: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-tab:last-child {
            border-right: none;
        }

        .nav-tab.active {
            background: #3498db;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            background: #e9ecef;
            color: #495057;
        }

        .nav-tab i {
            font-size: 1.1rem;
        }

        .main-container {
            margin: 0 2rem 2rem;
            background: #ffffff;
            border-radius: 0 0 12px 12px;
            padding: 2.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            border-top: none;
        }

        .persons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 2rem;
        }

        .person-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border-color: #3498db;
        }

        .person-header {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .person-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            border: 3px solid #3498db;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
        }

        .person-info {
            flex: 1;
        }

        .person-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .person-id {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .delete-btn {
            background: #dc3545;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .delete-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
        }

        .attendance-section {
            margin-bottom: 2rem;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .section-title i {
            color: #3498db;
        }

        .attendance-table {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
        }

        .table-header {
            background: #2c3e50;
            color: white;
            display: grid;
            grid-template-columns: 1fr 1fr;
            padding: 1rem 1.2rem;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            padding: 1rem 1.2rem;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.9rem;
            color: #495057;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .camera-tag {
            background: #17a2b8;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
        }

        .assign-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #ffffff;
            color: #495057;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-input::placeholder {
            color: #adb5bd;
        }

        .assign-btn {
            background: #28a745;
            border: none;
            color: white;
            padding: 0.85rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 0.95rem;
        }

        .assign-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        .user-actions {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            background: #ffffff;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            border-color: #3498db;
            color: #3498db;
            background: rgba(52, 152, 219, 0.05);
        }

        .action-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .action-btn i {
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .main-container {
                margin: 0 1rem 1rem;
                padding: 1.5rem;
            }
            
            .nav-tabs {
                margin: 1rem;
                flex-wrap: wrap;
            }
            
            .nav-tab {
                flex-basis: calc(50% - 1px);
                font-size: 0.8rem;
                padding: 1rem;
            }
            
            .persons-grid {
                grid-template-columns: 1fr;
            }
            
            .assign-section {
                grid-template-columns: 1fr;
            }
            
            .user-actions {
                flex-direction: column;
            }
        }

        /* Status indicators */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-active {
            background: #28a745;
        }

        .status-pending {
            background: #ffc107;
        }

        .status-inactive {
            background: #6c757d;
        }

        /* Loading animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .person-card {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeOut {
            to { opacity: 0; transform: scale(0.95); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">
            <i class="fas fa-shield-alt"></i>
            Face Recognition Admin Panel
        </div>
        <a href="#" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Dashboard
        </a>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active">
            <i class="fas fa-user-slash"></i>
            <span>Unknown Persons</span>
        </button>
        <button class="nav-tab">
            <i class="fas fa-user-plus"></i>
            <span>User Registration</span>
        </button>
        <button class="nav-tab">
            <i class="fas fa-users-cog"></i>
            <span>User Management</span>
        </button>
        <button class="nav-tab">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </button>
        <button class="nav-tab">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </button>
    </div>

    <div class="main-container">
        <div class="persons-grid">
            <!-- Person Card 1 -->
            <div class="person-card">
                <div class="person-header">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cdefs%3E%3ClinearGradient id='grad1' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%233498db;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%232c3e50;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='80' height='80' fill='url(%23grad1)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23fff' font-size='12' font-weight='600'%3EPhoto%3C/text%3E%3C/svg%3E" alt="Person" class="person-image">
                    <div class="person-info">
                        <div class="person-name">Unknown Person #001</div>
                        <div class="person-id">
                            <span class="status-indicator status-pending"></span>
                            ID: UP-001 • Status: Pending Assignment
                        </div>
                    </div>
                    <button class="delete-btn" title="Delete Record">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="attendance-section">
                    <div class="section-title">
                        <i class="fas fa-clock"></i>
                        Detection History
                    </div>
                    <div class="attendance-table">
                        <div class="table-header">
                            <div>Date & Time</div>
                            <div>Camera Location</div>
                        </div>
                        <div class="table-row">
                            <div>2025-06-26 09:15:42</div>
                            <div><span class="camera-tag">Main Entrance</span></div>
                        </div>
                        <div class="table-row">
                            <div>2025-06-26 14:32:18</div>
                            <div><span class="camera-tag">Reception Area</span></div>
                        </div>
                    </div>
                </div>

                <div class="user-actions">
                    <button class="action-btn active">
                        <i class="fas fa-user"></i>
                        <span>Single User</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-user-friends"></i>
                        <span>Multiple Users</span>
                    </button>
                </div>

                <div class="assign-section">
                    <input type="text" class="form-input" placeholder="Full Name" required>
                    <input type="email" class="form-input" placeholder="Email Address" required>
                </div>

                <button class="assign-btn">
                    <i class="fas fa-user-check"></i>
                    Assign Identity
                </button>
            </div>

            <!-- Person Card 2 -->
            <div class="person-card">
                <div class="person-header">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cdefs%3E%3ClinearGradient id='grad2' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%2317a2b8;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%232c3e50;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='80' height='80' fill='url(%23grad2)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23fff' font-size='12' font-weight='600'%3EPhoto%3C/text%3E%3C/svg%3E" alt="Person" class="person-image">
                    <div class="person-info">
                        <div class="person-name">Unknown Person #002</div>
                        <div class="person-id">
                            <span class="status-indicator status-pending"></span>
                            ID: UP-002 • Status: Pending Assignment
                        </div>
                    </div>
                    <button class="delete-btn" title="Delete Record">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="attendance-section">
                    <div class="section-title">
                        <i class="fas fa-clock"></i>
                        Detection History
                    </div>
                    <div class="attendance-table">
                        <div class="table-header">
                            <div>Date & Time</div>
                            <div>Camera Location</div>
                        </div>
                        <div class="table-row">
                            <div>2025-06-26 11:28:15</div>
                            <div><span class="camera-tag">Parking Lot</span></div>
                        </div>
                    </div>
                </div>

                <div class="user-actions">
                    <button class="action-btn active">
                        <i class="fas fa-user"></i>
                        <span>Single User</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-user-friends"></i>
                        <span>Multiple Users</span>
                    </button>
                </div>

                <div class="assign-section">
                    <input type="text" class="form-input" placeholder="Full Name" required>
                    <input type="email" class="form-input" placeholder="Email Address" required>
                </div>

                <button class="assign-btn">
                    <i class="fas fa-user-check"></i>
                    Assign Identity
                </button>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Form functionality
        document.querySelectorAll('.assign-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.person-card');
                const nameInput = card.querySelector('input[placeholder="Full Name"]');
                const emailInput = card.querySelector('input[placeholder="Email Address"]');
                const username = nameInput.value.trim();
                const email = emailInput.value.trim();
                
                if (username && email && isValidEmail(email)) {
                    showNotification('success', `✓ Identity assigned successfully!\nUser: ${username}\nEmail: ${email}`);
                    
                    // Update status
                    const statusIndicator = card.querySelector('.status-indicator');
                    const statusText = card.querySelector('.person-id');
                    statusIndicator.className = 'status-indicator status-active';
                    statusText.innerHTML = statusText.innerHTML.replace('Pending Assignment', 'Assigned');
                    
                    // Reset form
                    nameInput.value = '';
                    emailInput.value = '';
                } else {
                    showNotification('error', '⚠ Please enter a valid name and email address.');
                }
            });
        });

        // Delete functionality
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const personName = this.closest('.person-card').querySelector('.person-name').textContent;
                if (confirm(`Are you sure you want to delete the record for "${personName}"?\n\nThis action cannot be undone.`)) {
                    const card = this.closest('.person-card');
                    card.style.animation = 'fadeOut 0.3s ease forwards';
                    setTimeout(() => {
                        card.remove();
                        showNotification('success', 'Record deleted successfully.');
                    }, 300);
                }
            });
        });

        // Action button functionality
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.person-card');
                const buttons = card.querySelectorAll('.action-btn');
                buttons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Utility functions
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = message.replace(/\n/g, '<br>');
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'fadeOut 0.3s ease forwards';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';

        // Enhanced form validation
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.style.borderColor = '#dc3545';
                } else if (this.type === 'email' && this.value && !isValidEmail(this.value)) {
                    this.style.borderColor = '#dc3545';
                } else {
                    this.style.borderColor = '#e9ecef';
                }
            });

            input.addEventListener('input', function() {
                this.style.borderColor = '#e9ecef';
            });
        });
    </script>
</body>
</html>