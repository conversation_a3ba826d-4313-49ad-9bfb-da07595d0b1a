services:
  # Face Recognition Application - Local Development
  face-recognition:
    build: .
    container_name: face-recognition-app-local
    runtime: nvidia
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=local
      - TZ=Asia/Kolkata  # Set to your local timezone
    env_file:
      - .env
    volumes:
      - ./Dataset:/app/Dataset
      - ./static:/app/static
      - ./templates:/app/templates
      - ./config.local.yaml:/app/config.local.yaml
      - ./app:/app/app  # Mount source code for development
      - ./models:/app/app/models  # Mount models directory for dlib shape predictor
    depends_on:
      - qdrant-local
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"  # Allow access to host MySQL
    networks:
      - face-recognition-local-network

  # Qdrant Vector Database - Local Development
  qdrant-local:
    image: qdrant/qdrant:latest
    container_name: face-recognition-qdrant-local
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_local_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - face-recognition-local-network

  alarm-service:
    build: ../alarm-service
    container_name: alarm-service-local
    ports:
      - "8001:8000"
    environment:
      - TZ=Asia/Kolkata  # Set to your local timezone
    networks:
      - face-recognition-local-network

volumes:
  qdrant_local_data:

networks:
  face-recognition-local-network:
    driver: bridge



