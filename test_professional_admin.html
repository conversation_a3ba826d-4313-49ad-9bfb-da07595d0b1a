<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Admin Panel Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Professional Combat-Style Admin Panel CSS */
        :root {
            --primary-dark: #0f172a;
            --secondary-dark: #1e293b;
            --tertiary-dark: #334155;
            --accent-blue: #3b82f6;
            --accent-blue-dark: #1d4ed8;
            --accent-cyan: #3498db;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-light: #e2e8f0;
            --text-muted: #cbd5e1;
            --text-dark: #64748b;
            --border-color: #334155;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
            color: var(--text-light);
            min-height: 100vh;
            font-size: 14px;
        }

        .admin-dashboard {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 50%, var(--primary-dark) 100%);
        }

        /* Header */
        .admin-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .admin-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .admin-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
        }

        .admin-logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-dark) 100%);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .admin-logo-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-light);
        }

        .admin-logo-subtitle {
            font-size: 11px;
            color: var(--text-dark);
            text-transform: uppercase;
        }

        .admin-status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 20px;
            font-size: 12px;
            color: var(--success-color);
        }

        .admin-status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--success-color);
        }

        /* Navigation */
        .admin-navigation {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--tertiary-dark) 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .admin-main-nav {
            display: flex;
            align-items: center;
            padding: 0 24px;
            gap: 4px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .admin-nav-tab {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            transition: var(--transition);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            min-width: 160px;
            justify-content: center;
        }

        .admin-nav-tab:hover {
            background: rgba(59, 130, 246, 0.1);
            color: var(--text-light);
        }

        .admin-nav-tab.active {
            background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-dark) 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .admin-nav-tab-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-nav-icon {
            font-size: 16px;
        }

        .admin-nav-label {
            font-size: 14px;
            font-weight: 500;
        }

        /* Sub Navigation */
        .admin-sub-nav {
            background: linear-gradient(135deg, var(--tertiary-dark) 0%, var(--secondary-dark) 100%);
            border-bottom: 1px solid var(--border-color);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .admin-sub-nav-container {
            display: flex;
            align-items: center;
            padding: 0 24px;
            gap: 2px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .admin-sub-nav-tab {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: none;
            border: none;
            color: var(--text-dark);
            cursor: pointer;
            transition: var(--transition);
            border-radius: var(--border-radius);
            font-size: 13px;
            font-weight: 500;
        }

        .admin-sub-nav-tab:hover {
            background: rgba(52, 152, 219, 0.1);
            color: var(--text-light);
        }

        .admin-sub-nav-tab.active {
            background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--accent-blue) 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        /* Content */
        .admin-content {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 60vh;
            padding: 24px;
        }

        .admin-content-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .tab-pane {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .tab-pane.active {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .test-status {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--success-color);
            padding: 16px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-dashboard">
        <!-- Header -->
        <div class="admin-header">
            <div class="admin-header-content">
                <div class="admin-logo-section">
                    <a href="#" class="admin-logo">
                        <div class="admin-logo-icon">
                            <i class="fas fa-eye" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div class="admin-logo-text">
                            <div class="admin-logo-title">VigilantEye</div>
                            <div class="admin-logo-subtitle">Command Center</div>
                        </div>
                    </a>
                </div>
                
                <div class="admin-status-section">
                    <div class="admin-status-indicator active">
                        <div class="admin-status-dot active"></div>
                        <span class="admin-status-text">System Operational</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="admin-navigation">
            <div class="admin-nav-container">
                <div class="admin-main-nav">
                    <button class="admin-nav-tab active" data-tab="unknown-persons">
                        <div class="admin-nav-tab-content">
                            <i class="fas fa-user-question admin-nav-icon"></i>
                            <span class="admin-nav-label">Unknown Persons</span>
                        </div>
                    </button>
                    <button class="admin-nav-tab" data-tab="user-registration">
                        <div class="admin-nav-tab-content">
                            <i class="fas fa-user-plus admin-nav-icon"></i>
                            <span class="admin-nav-label">User Registration</span>
                        </div>
                    </button>
                    <button class="admin-nav-tab" data-tab="user-management">
                        <div class="admin-nav-tab-content">
                            <i class="fas fa-users-cog admin-nav-icon"></i>
                            <span class="admin-nav-label">User Management</span>
                        </div>
                    </button>
                    <button class="admin-nav-tab" data-tab="management">
                        <div class="admin-nav-tab-content">
                            <i class="fas fa-tools admin-nav-icon"></i>
                            <span class="admin-nav-label">System Management</span>
                        </div>
                    </button>
                    <button class="admin-nav-tab" data-tab="settings">
                        <div class="admin-nav-tab-content">
                            <i class="fas fa-cog admin-nav-icon"></i>
                            <span class="admin-nav-label">Settings</span>
                        </div>
                    </button>
                </div>

                <!-- Management Sub-Navigation -->
                <div class="admin-sub-nav" id="management-sub-nav" style="display: none;">
                    <div class="admin-sub-nav-container">
                        <button class="admin-sub-nav-tab active" data-management-tab="camera-management">
                            <i class="fas fa-video"></i>
                            <span>Camera Management</span>
                        </button>
                        <button class="admin-sub-nav-tab" data-management-tab="webhook-management">
                            <i class="fas fa-link"></i>
                            <span>Webhook Management</span>
                        </button>
                        <button class="admin-sub-nav-tab" data-management-tab="whatsapp-management">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp Management</span>
                        </button>
                        <button class="admin-sub-nav-tab" data-management-tab="sms-management">
                            <i class="fas fa-sms"></i>
                            <span>SMS Management</span>
                        </button>
                        <button class="admin-sub-nav-tab" data-management-tab="email-management">
                            <i class="fas fa-envelope"></i>
                            <span>Email Management</span>
                        </button>
                        <button class="admin-sub-nav-tab" data-management-tab="alert-management">
                            <i class="fas fa-bell"></i>
                            <span>Alert Management</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="admin-content">
            <div class="admin-content-container">
                <div class="tab-content">
                    <div class="tab-pane active" id="unknown-persons-tab">
                        <div class="test-status">
                            <h3>✅ Unknown Persons Tab Active</h3>
                            <p>Professional combat-style design is working correctly!</p>
                        </div>
                    </div>
                    <div class="tab-pane" id="user-registration-tab">
                        <div class="test-status">
                            <h3>✅ User Registration Tab Active</h3>
                            <p>Navigation system is functioning properly!</p>
                        </div>
                    </div>
                    <div class="tab-pane" id="user-management-tab">
                        <div class="test-status">
                            <h3>✅ User Management Tab Active</h3>
                            <p>Horizontal navigation design is responsive!</p>
                        </div>
                    </div>
                    <div class="tab-pane" id="management-tab">
                        <div class="test-status">
                            <h3>✅ System Management Tab Active</h3>
                            <p>Sub-navigation for 6 management services is working!</p>
                            <p><strong>Active Sub-Service:</strong> <span id="active-sub-service">Camera Management</span></p>
                        </div>
                    </div>
                    <div class="tab-pane" id="settings-tab">
                        <div class="test-status">
                            <h3>✅ Settings Tab Active</h3>
                            <p>All navigation functionality preserved!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test navigation functionality
        function setupNavigation() {
            const mainTabs = document.querySelectorAll('.admin-nav-tab');
            const subTabs = document.querySelectorAll('.admin-sub-nav-tab');
            const managementSubNav = document.getElementById('management-sub-nav');
            const activeSubService = document.getElementById('active-sub-service');

            // Main navigation
            mainTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active from all main tabs
                    mainTabs.forEach(t => t.classList.remove('active'));
                    // Add active to clicked tab
                    tab.classList.add('active');

                    // Hide all tab panes
                    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
                    
                    // Show corresponding tab pane
                    const tabId = tab.getAttribute('data-tab');
                    const tabPane = document.getElementById(`${tabId}-tab`);
                    if (tabPane) {
                        tabPane.classList.add('active');
                    }

                    // Show/hide management sub-navigation
                    if (tabId === 'management') {
                        managementSubNav.style.display = 'block';
                    } else {
                        managementSubNav.style.display = 'none';
                    }
                });
            });

            // Sub navigation
            subTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active from all sub tabs
                    subTabs.forEach(t => t.classList.remove('active'));
                    // Add active to clicked tab
                    tab.classList.add('active');

                    // Update active sub-service display
                    const serviceName = tab.querySelector('span').textContent;
                    if (activeSubService) {
                        activeSubService.textContent = serviceName;
                    }
                });
            });
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', setupNavigation);
    </script>
</body>
</html>
