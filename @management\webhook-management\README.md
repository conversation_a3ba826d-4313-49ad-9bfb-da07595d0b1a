# Webhook Management Service

This folder contains all code related to webhook management functionality.

## Files Structure:
- **html/** - HTML templates for webhook management UI
- **css/** - CSS styles for webhook management components
- **js/** - JavaScript functions for webhook operations
- **backend/** - Backend API endpoints for webhook CRUD operations

## Functionality:
- Add new webhooks with URLs and HTTP methods
- Edit existing webhook configurations
- Delete webhooks
- Toggle webhook active/inactive status
- List all configured webhooks
- Support for custom headers and body templates

## API Endpoints:
- POST /webhooks - Create new webhook
- GET /webhooks - Get all webhooks
- GET /webhooks/{id} - Get specific webhook
- PUT /webhooks/{id} - Update webhook
- DELETE /webhooks/{id} - Delete webhook
- PUT /webhooks/{id}/toggle - Toggle webhook status

## Database Models:
- Webhook model with url, description, http_method, headers, body_template, is_active fields
