<!-- for this -->
 <button class="tab-btn active" data-tab="unknown-persons">
  <i class="fas fa-user-question" style="animation: none;"></i> Unknown Persons
</button>

<!-- this -->

<!-- Premium Admin Panel HTML Structure -->
<button class="tab-btn active" data-tab="unknown-persons">
  <i class="fas fa-user-question"></i> 
  <span>Unknown Persons</span>
</button>
<!-- ------------------------------------------------------ -->

<!-- for this -->

 <div class="tab-pane active" id="unknown-persons-tab">
  <div id="unknownContainer" class="loading">
    <i class="fas fa-spinner fa-spin"></i>
    <p>Loading data...</p>
  </div>
</div>


<!-- this -->
<div class="tab-pane active" id="unknown-persons-tab">
  <div class="section-header">
    <h2 class="section-title">
      <i class="fas fa-user-question"></i>
      Unknown Persons
    </h2>
    <p class="section-subtitle">Manage and register unidentified individuals detected by the system</p>
  </div>
  
  <div id="unknownContainer" class="unknown-persons-grid loading">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Loading unknown persons...</p>
    </div>
  </div>
</div>

<!-- ------------------------------------------------------ -->

<!-- for this -->
 <div id="registerUnknownModal" class="modal">
  <div class="modal-content">
    <span class="close" onclick="document.getElementById('registerUnknownModal').style.display='none'">&times;</span>
    <h2>Register Unknown Person</h2>
    <form id="registerUnknownForm" enctype="multipart/form-data">
      <input type="hidden" id="registerUnknownClusterId" name="cluster_id">
      <div class="form-group">
        <label for="regUnknownUsername"><i class="fas fa-user"></i> Username</label>
        <input type="text" id="regUnknownUsername" name="username" class="form-control" placeholder="Enter username" required>
      </div>
      <div class="form-group">
        <label for="regUnknownEmail"><i class="fas fa-envelope"></i> Email</label>
        <input type="email" id="regUnknownEmail" name="email" class="form-control" placeholder="Enter email address" required>
      </div>
      <div class="form-group">
        <label for="captured_image_unknown"><i class="fas fa-camera"></i> Profile Image Preview</label>
        <img id="registerUnknownImagePreview" src="" alt="Unknown Person Image" class="image-preview" style="display: none;">
        <input type="hidden" id="captured_image_unknown" name="captured_image">
      </div>
      <button type="submit" class="btn btn-success">
        <i class="fas fa-user-plus"></i> Register
      </button>
    </form>
  </div>
</div>

<!-- this -->
<!-- Enhanced Register Unknown Modal -->
<div id="registerUnknownModal" class="modal">
  <div class="modal-backdrop" onclick="document.getElementById('registerUnknownModal').style.display='none'"></div>
  <div class="modal-content enhanced-modal">
    <div class="modal-header">
      <h2 class="modal-title">
        <i class="fas fa-user-plus"></i>
        Register Unknown Person
      </h2>
      <button class="close-btn" onclick="document.getElementById('registerUnknownModal').style.display='none'">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="modal-body">
      <form id="registerUnknownForm" enctype="multipart/form-data">
        <input type="hidden" id="registerUnknownClusterId" name="cluster_id">
        
        <div class="profile-preview-section">
          <div class="image-preview-container">
            <img id="registerUnknownImagePreview" src="" alt="Unknown Person Image" class="profile-preview-image" style="display: none;">
            <div class="image-overlay">
              <i class="fas fa-camera"></i>
              <span>Profile Image</span>
            </div>
          </div>
          <input type="hidden" id="captured_image_unknown" name="captured_image">
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="regUnknownUsername" class="form-label">
              <i class="fas fa-user"></i>
              Username
            </label>
            <input type="text" id="regUnknownUsername" name="username" class="form-input" placeholder="Enter username" required>
          </div>
          
          <div class="form-group">
            <label for="regUnknownEmail" class="form-label">
              <i class="fas fa-envelope"></i>
              Email Address
            </label>
            <input type="email" id="regUnknownEmail" name="email" class="form-input" placeholder="Enter email address" required>
          </div>
        </div>
      </form>
    </div>
    
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" onclick="document.getElementById('registerUnknownModal').style.display='none'">
        Cancel
      </button>
      <button type="submit" form="registerUnknownForm" class="btn btn-primary">
        <i class="fas fa-user-plus"></i>
        Register Person
      </button>
    </div>
  </div>
</div>
<!-- ------------------------------------------------------ -->

<!-- for this -->
 
 <div id="moveImageModal" class="modal">
  <div class="modal-content" style="max-width: 500px;">
    <span class="close" onclick="document.getElementById('moveImageModal').style.display='none'">&times;</span>
    <h2>Move Image to Another Cluster</h2>
    <p>Select the target cluster to move this image to:</p>
    <div id="clustersList" class="clusters-list">
      <div class="loading">
        <i class="fas fa-spinner fa-spin"></i> Loading clusters...
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" onclick="document.getElementById('moveImageModal').style.display='none'">Cancel</button>
    </div>
  </div>
</div>

<!-- this -->
<!-- Enhanced Move Image Modal -->
<div id="moveImageModal" class="modal">
  <div class="modal-backdrop" onclick="document.getElementById('moveImageModal').style.display='none'"></div>
  <div class="modal-content enhanced-modal">
    <div class="modal-header">
      <h2 class="modal-title">
        <i class="fas fa-arrows-alt"></i>
        Move Images to Cluster
      </h2>
      <button class="close-btn" onclick="document.getElementById('moveImageModal').style.display='none'">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="modal-body">
      <p class="modal-description">Select the target cluster to move this person's images to:</p>
      <div id="clustersList" class="clusters-grid">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <p>Loading clusters...</p>
        </div>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn btn-secondary" onclick="document.getElementById('moveImageModal').style.display='none'">
        Cancel
      </button>
    </div>
  </div>
</div>