import threading
import queue
import time
import base64
import cv2
import datetime
import logging
import requests
from concurrent.futures import ThreadPoolExecutor
from ultralytics import YOLO
from . import models
from .utils import (generate_encoding, calculate_cosine_similarity)
import numpy as np
import uuid
from .models import Encoding, User
from app import database
from scipy.spatial.distance import cosine
from sqlalchemy.orm import Session
from app.database import sessionmaker, SessionLocal
from facenet_pytorch import InceptionResnetV1
from .utils import load_cameras, save_person_image
from .services.qdrant_service import QdrantService
import torch
import os
# Set up logging
logger = logging.getLogger(__name__)
from app.database import SessionLocal



# Note: This module uses YOLO's built-in tracker instead of DeepSORT for better performance
# Note: This module uses PyTorch FaceNet (InceptionResnetV1) instead of Keras FaceNet for face encoding

class FaceDetection:
    def __init__(self, model_path, db: Session):
        # Check CUDA availability with more detailed logging
        # logger.info(f"PyTorch version: {torch.__version__}")
        # logger.info(f"CUDA available: {torch.cuda.is_available()}")

        # if torch.cuda.is_available():
        #     logger.info(f"CUDA version: {torch.version.cuda}")
        #     logger.info(f"CUDA device count: {torch.cuda.device_count()}")
        #     logger.info(f"CUDA device name: {torch.cuda.get_device_name(0)}")

        # Try to initialize YOLO model on CUDA if available
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # logger.info(f"Attempting to initialize YOLO model on: {self.device}")

        try:
            # Try to load YOLO with CUDA
            self.model = YOLO(model_path)
            # logger.info(f"YOLO model initialized successfully")
        except Exception as e:
            # logger.error(f"Error initializing YOLO model: {e}")
            # Fallback to CPU explicitly if there was an error
            self.model = YOLO(model_path)
            # logger.info("Fallback: YOLO model initialized on CPU")

        # Initialize FaceNet on GPU if available
        # logger.info(f"Using device for FaceNet: {self.device}")
        self.embedder = InceptionResnetV1(pretrained='vggface2').to(self.device).eval()

        # Verify FaceNet model is on correct device
        # logger.info(f"FaceNet model device: {next(self.embedder.parameters()).device}")

        # Rest of initialization
        self.db = db
        self.running = False
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        self.camera_threads = []
        self.process_threads = []
        self.lock = threading.Lock()
        self.cameraname = []
        self.db_encodings_cache = {}
        self.db_users_cache = {}
        self.threshold = None
        self.unknown_track_map = {}
        # Use Docker service name when in container, localhost when running locally
        self.alarm_service = "http://localhost:8002/webhook" #"http://alarm-service:8000/webhook"#"http://localhost:8002/webhook" #"http://alarm-service:8000/webhook" #  for local
        # self.webhook_url = "https://webhook.site/4dff0b3e-8e7e-450e-a778-4e0ebefb8429"

        # Background task queues for non-blocking operations
        self.attendance_queue = queue.Queue()
        self.image_save_queue = queue.Queue()
        self.webhook_queue = queue.Queue()

        # Background worker threads
        self.attendance_worker = None
        self.image_worker = None
        self.webhook_worker = None
        self.workers_running = False

        # Initialize the Qdrant service
        try:
            self.qdrant_client = QdrantService()
            # logger.info("Qdrant service initialized successfully")
        except Exception as e:
            # logger.error(f"Failed to initialize Qdrant service: {str(e)}")
            self.qdrant_client = None

    def start(self, threshold=0.5):
        """Starts the face detection system.

        Args:
            threshold (float, optional): Similarity threshold for face recognition.
                                        Defaults to 0.5 if not provided.
        """
        if self.running:
            # logger.info("Face detection system is already running")
            return

        # Initialize threshold with the provided value
        self.threshold = threshold
        # logger.info(f"Face detection started with threshold: {self.threshold}")

        # Cache database entries to avoid repeated queries
        self.db_encodings_cache = {enc.user_id: np.array(enc.encoding) for enc in self.db.query(Encoding).all()}
        self.db_users_cache = {user.user_id: user.username for user in self.db.query(User).all()}

        # Load cameras from the database
        # Ensure we're using a fresh database session
        try:
            # Check if the database session is active
            if not self.db.is_active:
                # logger.warning("Database session is not active, creating a new one")

                self.db = SessionLocal()

            # Load cameras with the refreshed session
            cameras = load_cameras(self.db)
            # print(cameras)
            if not cameras:
                # logger.warning("No cameras configured in the database. Please add cameras first.")
                return

            # logger.info(f"Successfully loaded {len(cameras)} cameras from the database")
        except Exception as e:
            # logger.error(f"Error loading cameras from database: {e}")
            return

        # Clear previous state
        self.stop_connections()

        # Get all camera names and URLs
        all_camera_names = list(cameras.keys())
        # print(all_camera_names)
        # logger.info(f"All camera names: {all_camera_names}")

        # Initialize cameras and keep track of which ones successfully open
        self.cameraname = []  # Will only contain names of successfully opened cameras
        successful_cameras = []

        # First, collect all camera URLs with their names
        camera_urls_with_names = []
        for cam_name in all_camera_names:
            url = cameras[cam_name][0]
            if url.isdigit():
                url = int(url)
            camera_urls_with_names.append((cam_name, url))

        # print(camera_urls_with_names)

        # logger.info(f"Attempting to connect to {len(camera_urls_with_names)} cameras")

        # Try to initialize each camera
        for cam_name, cam_url in camera_urls_with_names:
            try:
                cap = cv2.VideoCapture(cam_url)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                if cap.isOpened():
                    # logger.info(f"Camera {cam_name} ({cam_url}) opened successfully.")
                    self.cap_devices.append(cap)
                    self.frame_queues.append(queue.Queue(maxsize=10))
                    self.cameraname.append(cam_name)  # Only add name if camera opened successfully
                    successful_cameras.append(cam_name)
                else:
                    logger.error(f"Failed to open camera: {cam_name} ({cam_url})")
                    # Don't add to cap_devices or frame_queues if failed
            except Exception as e:
                
                logger.error(f"Error initializing camera {cam_name} ({cam_url}): {e}")

        # Log summary of camera initialization
        # logger.info(f"Successfully opened {len(self.cap_devices)} out of {len(camera_urls_with_names)} cameras")
        # logger.info(f"Working cameras: {successful_cameras}")

        # Ensure frames array matches the number of successfully opened cameras
        self.frames = [None] * len(self.cap_devices)

        if not self.running and self.cap_devices:
            self.running = True

            # Start background worker threads
            self.start_background_workers()

            # Start threads for each camera
            for idx, cap in enumerate(self.cap_devices):
                read_thread = self.thread_pool.submit(self.read_frames, idx, cap)
                process_thread = self.thread_pool.submit(self.process_frames, idx)
                self.camera_threads.append(read_thread)
                self.process_threads.append(process_thread)
            # logger.info(f"Started {len(self.camera_threads)} camera threads")
        else:
            # logger.warning("No cameras were successfully initialized")
            pass

    def stop_connections(self):
        """Clean up camera connections without stopping the entire system."""
        for cap in self.cap_devices:
            if cap and cap.isOpened():
                cap.release()
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        self.cameraname = []  # Reset camera names list as well

    def stop(self):
        """Stops the face detection system."""
        if not self.running:
            return

        # logger.info("Stopping face detection system...")
        self.running = False

        # Stop background workers
        self.stop_background_workers()

        # Give threads time to exit gracefully
        time.sleep(0.5)

        # Release all camera resources
        self.stop_connections()

        # logger.info("Face detection system stopped")

    def read_frames(self, idx, cap):
        """Reads frames from the camera and adds them to the queue."""
        reconnect_attempts = 0
        max_reconnect_attempts = 5
        # print(idx)
        while self.running:
            try:
                ret, frame = cap.read()
                if not ret:
                    # logger.warning(f"Failed to read frame from camera {idx}")
                    reconnect_attempts += 1

                    if reconnect_attempts > max_reconnect_attempts:
                        # logger.error(f"Too many failures for camera {idx}, stopping capture")
                        break

                    time.sleep(1)  # Wait before retry
                    continue

                reconnect_attempts = 0  # Reset on successful frame

                # Resize frame for consistency
                frame = cv2.resize(frame, (1080, 780))

                with self.lock:
                    if idx < len(self.frame_queues):  # Make sure the index is still valid
                        if self.frame_queues[idx].full():
                            self.frame_queues[idx].get()  # Remove the oldest frame
                        self.frame_queues[idx].put(frame)
                    else:
                        break  # This camera is no longer in our list, exit thread

            except Exception as e:
                # logger.error(f"Error in read_frames for camera {idx}: {e}")
                time.sleep(0.1)  # Prevent CPU spin in case of errors

    def process_frames(self, idx):
        """Processes frames and performs face detection and recognition."""
        # CUDA availability is checked when needed in the tracking configuration

        # Tracking configuration for face detection model
        tracking_config = {
            'tracker': 'bytetrack.yaml',  # Using ByteTrack tracker
            'persist': True,            # Persist tracks between frames
            'conf': 0.3,               # Lower confidence threshold to detect more faces
            'iou': 0.45,                # Lower IOU threshold for NMS (allows more overlapping detections)
            'max_det': 100,             # Maximum number of detections per frame (significantly increased)
            'verbose': False,           # Disable verbose output
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',  # Use CUDA if available
        }

        # Flag to track if we need to fall back to CPU
        using_fallback = False

        tracked_faces = {}
        retry_tracker = {}
        retry_limit = 10

        while self.running:
            try:
                if idx >= len(self.frame_queues):
                    break  # Exit if this camera is no longer valid

                if self.frame_queues[idx].empty():
                    time.sleep(0.01)  # Short sleep when no frames
                    continue

                frame = self.frame_queues[idx].get()

                # Make a copy for processing
                processing_frame = frame.copy()

                # Use YOLO's built-in tracker with optimized settings for multiple face detection
                try:
                    # First detect all faces in the frame
                    detection_results = self.model(processing_frame, stream=False, verbose=False,
                                                  conf=tracking_config['conf'],
                                                  iou=tracking_config['iou'],
                                                  max_det=tracking_config['max_det'])

                    # Log how many faces were detected
                    if len(detection_results) > 0:
                        num_faces = len(detection_results[0].boxes)
                        # logger.info(f"Camera {idx}: Detected {num_faces} faces before tracking")

                    # Then track the detected faces
                    results = self.model.track(processing_frame, stream=True, **tracking_config)
                    # logger.info(f"YOLO tracker called successfully for camera {idx}")
                except Exception as e:
                    # logger.error(f"Error in YOLO tracker for camera {idx}: {e}")
                    continue

                # Process each result from the tracker
                for result in results:
                    # Check if boxes exist
                    if not hasattr(result, 'boxes'):
                        # logger.warning(f"Camera {idx}: No boxes attribute in result")
                        continue

                    # Check if there are any detections
                    if len(result.boxes) == 0:
                        # logger.warning(f"Camera {idx}: No detections in this frame")
                        continue

                    # Check if track IDs exist
                    if not hasattr(result.boxes, 'id') or result.boxes.id is None:
                        # logger.warning(f"Camera {idx}: No valid track IDs found in this frame")
                        continue

                    # Get boxes and their IDs
                    boxes = result.boxes
                    track_ids = boxes.id.int().cpu().tolist()

                    # Log the number of detections for debugging
                    if len(track_ids) > 0:
                        # logger.info(f"Camera {idx}: Successfully tracking {len(track_ids)} faces")

                        # Log confidence scores for debugging
                        conf_scores = boxes.conf.cpu().tolist()
                        # logger.info(f"Camera {idx}: Confidence scores: {[round(score, 2) for score in conf_scores]}")
                    else:
                        # logger.warning(f"Camera {idx}: No track IDs found despite having boxes")
                        continue

                    # Process each tracked box
                    for i, track_id in enumerate(track_ids):
                        # Skip boxes with low confidence
                        if boxes.conf[i] < tracking_config['conf']:
                            continue

                        # Get box coordinates
                        x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()

                        # Make sure coordinates are valid
                        x1, y1, x2, y2 = max(0, int(x1)), max(0, int(y1)), min(frame.shape[1], int(x2)), min(frame.shape[0], int(y2))
                        if x2 <= x1 or y2 <= y1:
                            continue  # Skip invalid boxes

                        # Check face size
                        face_width, face_height = x2 - x1, y2 - y1

                        if face_width < 40 or face_height < 40:
                            continue  # Skip faces that are too small

                        # aspect_ratio = face_width / face_height

                        # if aspect_ratio < 0.75:
                            # continue  # Likely looking sideways

                        face_image = frame[y1:y2, x1:x2]
                        if face_image.size == 0:
                            continue  # Skip if face crop is empty

                        if track_id not in tracked_faces:
                            try:
                                face_encoding = self.generate_encoding(face_image)
                                tracked_faces[track_id] = {'encoding': face_encoding, 'last_seen': time.time()}
                                retry_tracker[track_id] = 0
                            except Exception as e:
                                # logger.error(f"Error generating encoding: {e}")
                                continue
                        else:
                            tracked_faces[track_id]['last_seen'] = time.time()

                        face_encoding = tracked_faces[track_id]['encoding']

                        # Try Qdrant matching first, fall back to local if it fails
                        if self.qdrant_client:
                            most_similar_user, highest_similarity, user_id = self.match_face_from_qdrant(face_encoding)
                        else:
                            # Fallback to local database matching if Qdrant is unavailable
                            most_similar_user, highest_similarity = self.match_face(face_encoding)
                            user_id = self.get_user_id(most_similar_user) if most_similar_user else None

                        # Check if this is a known unknown person (already in Qdrant but with unknown_ prefix)
                        is_known_unknown = most_similar_user and most_similar_user.startswith("unknown_")

                        # Log the match details for debugging
                        # logger.debug(f"Match details: user={most_similar_user}, similarity={highest_similarity:.4f}, threshold={self.threshold:.4f}, is_known_unknown={is_known_unknown}")

                        # Only consider it a registered user if it's not an unknown and similarity is above threshold
                        if most_similar_user and not is_known_unknown and highest_similarity >= self.threshold:
                            track_name = most_similar_user
                            color = (0, 255, 0)  # Green for recognized faces

                            if user_id:
                                # Get camera name for this camera index
                                camera_name = self.cameraname[idx] if idx < len(self.cameraname) else None

                                # Queue attendance logging for recognized user (non-blocking)
                                self.attendance_queue.put(('user_attendance', (user_id, camera_name)))

                                # logger.info(f"Recognized user: {most_similar_user} (ID: {user_id}) with similarity {highest_similarity} on camera: {camera_name}")

                                # Queue image saving if the face image is valid (non-blocking)
                                if face_image.size > 0 and face_image.shape[0] >= 40 and face_image.shape[1] >= 40:
                                    self.image_save_queue.put(('save_person_image', (face_image, track_name)))
                        else:
                            # Check if this is a known unknown person (already in Qdrant with unknown_ prefix)
                            if is_known_unknown and highest_similarity >= self.threshold:
                                # This is a previously seen unknown person
                                # Extract the persistent_id from the username (format: unknown_<persistent_id_prefix>)
                                persistent_id_prefix = most_similar_user.replace("unknown_", "")

                                # Find the full persistent_id from the user_id (which should be the persistent_id for unknowns)
                                persistent_unknown_id = user_id

                                # Update the track map
                                self.unknown_track_map[track_id] = persistent_unknown_id

                                track_name = f"unknown_{persistent_id_prefix}"  # display short UUID in frame
                                color = (0, 0, 255)  # Red for unknown faces

                                # Log this match for debugging
                                # logger.info(f"Recognized known unknown person: {track_name} with similarity {highest_similarity:.4f}")

                                # Queue attendance logging for this unknown person (non-blocking)
                                camera_name = self.cameraname[idx] if idx < len(self.cameraname) else None
                                self.attendance_queue.put(('unknown_attendance', (persistent_unknown_id, camera_name, face_image)))

                                # Save the face image for this known unknown person (for other purposes)
                                # if face_image.size > 0 and face_image.shape[0] >= 50 and face_image.shape[1] >= 50:
                                    # We don't need to save the image again here since log_unknown_appearance already does it
                                    # pass

                            elif retry_tracker.get(track_id, 0) < retry_limit:
                                retry_tracker[track_id] = retry_tracker.get(track_id, 0) + 1
                                try:
                                    # Try to get a better encoding for better matching
                                    tracked_faces[track_id]['encoding'] = self.generate_encoding(face_image)
                                except Exception as e:
                                    logger.error(f"Error regenerating encoding: {e}")
                                continue
                            else:
                                # Assign a persistent UUID to this track_id if not already assigned
                                if track_id not in self.unknown_track_map:
                                    persistent_unknown_id = str(uuid.uuid4())
                                    self.unknown_track_map[track_id] = persistent_unknown_id
                                else:
                                    persistent_unknown_id = self.unknown_track_map[track_id]

                                track_name = f"unknown_{persistent_unknown_id[:8]}"  # display short UUID in frame
                                color = (0, 0, 255)  # Red for unknown faces

                                # Always save the unknown person to the database
                                # We'll save the image when we create the database record

                                # Process the unknown person
                                try:
                                    # Check if this persistent_id already exists in the database
                                    existing_unknown = self.db.query(models.Unknown).filter(
                                        models.Unknown.persistent_id == persistent_unknown_id
                                    ).first()

                                    if not existing_unknown:
                                        # Only add if it doesn't exist
                                        unknown_encoding = self.generate_encoding(face_image)

                                        # Queue image saving for unknown person (non-blocking)
                                        self.image_save_queue.put(('save_unknown_image', (face_image, persistent_unknown_id)))

                                        # For now, set image_path to None since saving is async
                                        # The actual path will be handled in the background worker
                                        image_path = None

                                        # Create the Unknown entry with the image path
                                        unknown_entry = models.Unknown(
                                            persistent_id=persistent_unknown_id,
                                            encoding=unknown_encoding.tolist(),
                                            image_path=image_path
                                        )
                                        self.db.add(unknown_entry)
                                        self.db.commit()
                                        # logger.info(f"Logged unknown person {persistent_unknown_id}")

                                        # Queue attendance logging for this new unknown person (non-blocking)
                                        camera_name = self.cameraname[idx] if idx < len(self.cameraname) else None
                                        self.attendance_queue.put(('unknown_attendance', (persistent_unknown_id, camera_name, face_image)))

                                        # Optionally, also store this unknown face in Qdrant for future matching
                                        if self.qdrant_client:
                                            try:
                                                # Store unknown faces in a separate collection or with a special tag
                                                self.qdrant_client.add_face_embedding(
                                                    user_id=persistent_unknown_id,
                                                    username=f"unknown_{persistent_unknown_id[:8]}",
                                                    encoding=unknown_encoding
                                                )
                                            except Exception as qdrant_err:
                                                logger.error(f"Failed to add unknown face to Qdrant: {qdrant_err}")
                                    else:
                                        # logger.info(f"Unknown person {persistent_unknown_id} already exists in database, skipping")

                                        # Even if the unknown person exists, we should still queue their appearance logging
                                        camera_name = self.cameraname[idx] if idx < len(self.cameraname) else None
                                        self.attendance_queue.put(('unknown_attendance', (persistent_unknown_id, camera_name, face_image)))
                                except Exception as e:
                                    logger.error(f"Failed to log unknown person: {e}")
                                    self.db.rollback()  # Rollback the transaction in case of error

                        # Draw bounding box and name
                        cv2.rectangle(processing_frame, (x1, y1), (x2, y2), color, 2)
                        cv2.putText(processing_frame, track_name, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

                # Clean up old tracks
                current_time = time.time()
                old_track_count = len(tracked_faces)
                tracked_faces = {k: v for k, v in tracked_faces.items()
                                if current_time - v['last_seen'] < 5.0}  # Keep tracks for 5 seconds

                # Log tracking information
                # if old_track_count > 0:
                    # logger.info(f"Camera {idx}: Tracking {len(tracked_faces)} faces (removed {old_track_count - len(tracked_faces)} old tracks)")

                # Convert processed frame to JPEG
                _, buffer = cv2.imencode('.jpg', processing_frame)
                encoded_frame = base64.b64encode(buffer).decode('utf-8')

                with self.lock:
                    if idx < len(self.frames):  # Check if the index is still valid
                        self.frames[idx] = encoded_frame

            except RuntimeError as e:
                # Check if this is the NMS CUDA error
                if "Could not run 'torchvision::nms'" in str(e) and "CUDA" in str(e) and not using_fallback:
                    logger.warning("CUDA NMS failed, falling back to CPU for NMS operations")
                    # Update config to use CPU
                    tracking_config['device'] = 'cpu'
                    using_fallback = True
                    continue  # Retry with new config
                else:
                    # For other errors, log and continue
                    logger.error(f"Error in process_frames for camera {idx}: {e}")
                    time.sleep(0.1)  # Prevent CPU spin in case of errors

            except Exception as e:
                logger.error(f"Error in process_frames for camera {idx}: {e}")
                time.sleep(0.1)  # Prevent CPU spin in case of errors

    def start_background_workers(self):
        """Start background worker threads for non-blocking operations."""
        if self.workers_running:
            return

        self.workers_running = True

        # Start attendance logging worker
        self.attendance_worker = threading.Thread(target=self._attendance_worker, daemon=True)
        self.attendance_worker.start()

        # Start image saving worker
        self.image_worker = threading.Thread(target=self._image_worker, daemon=True)
        self.image_worker.start()

        # Start webhook worker
        self.webhook_worker = threading.Thread(target=self._webhook_worker, daemon=True)
        self.webhook_worker.start()

        # logger.info("Background worker threads started")

    def stop_background_workers(self):
        """Stop background worker threads."""
        if not self.workers_running:
            return

        self.workers_running = False

        # Add sentinel values to queues to wake up workers
        self.attendance_queue.put(None)
        self.image_save_queue.put(None)
        self.webhook_queue.put(None)

        # Wait for workers to finish
        if self.attendance_worker and self.attendance_worker.is_alive():
            self.attendance_worker.join(timeout=2.0)
        if self.image_worker and self.image_worker.is_alive():
            self.image_worker.join(timeout=2.0)
        if self.webhook_worker and self.webhook_worker.is_alive():
            self.webhook_worker.join(timeout=2.0)

        logger.info("Background worker threads stopped")

    def _attendance_worker(self):
        """Background worker for attendance logging."""
        while self.workers_running:
            try:
                # Get task from queue with timeout
                task = self.attendance_queue.get(timeout=1.0)

                # Check for sentinel value (None means stop)
                if task is None:
                    break

                # Process the attendance logging task
                task_type, data = task

                if task_type == 'user_attendance':
                    user_id, camera_name = data
                    self._process_user_attendance(user_id, camera_name)
                elif task_type == 'unknown_attendance':
                    persistent_id, camera_name, face_image = data
                    self._process_unknown_attendance(persistent_id, camera_name, face_image)

                self.attendance_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in attendance worker: {e}")

    def _image_worker(self):
        """Background worker for image saving."""
        while self.workers_running:
            try:
                # Get task from queue with timeout
                task = self.image_save_queue.get(timeout=1.0)

                # Check for sentinel value (None means stop)
                if task is None:
                    break

                # Process the image saving task
                task_type, data = task

                if task_type == 'save_person_image':
                    face_image, track_name = data
                    self._process_save_person_image(face_image, track_name)
                elif task_type == 'save_unknown_image':
                    face_image, persistent_id = data
                    self._process_save_unknown_image(face_image, persistent_id)

                self.image_save_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in image worker: {e}")

    def _webhook_worker(self):
        """Background worker for webhook notifications."""
        while self.workers_running:
            try:
                # Get task from queue with timeout
                task = self.webhook_queue.get(timeout=1.0)

                # Check for sentinel value (None means stop)
                if task is None:
                    break
                # Process the webhook task
                self._process_webhook_notification(task)
                self.webhook_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in webhook worker: {e}")

    def _process_user_attendance(self, user_id, camera_name):
        """Process user attendance logging in background thread."""
        try:
            # Create a new database session for this thread
            db_session = SessionLocal()

            # Check if the user has not been logged in the past minute
            time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
            existing_attendance = db_session.query(models.Attendance).filter(
                models.Attendance.user_id == user_id,
                models.Attendance.timestamp >= time_window_start
            ).first()

            if not existing_attendance:
                # Create attendance record with camera name
                attendance_record = models.Attendance(
                    user_id=user_id,
                    timestamp=datetime.datetime.now(),
                    camera_name=camera_name or "System Camera"
                )
                db_session.add(attendance_record)
                db_session.commit()
                logger.info(f"Logged attendance for user ID: {user_id} from camera: {camera_name or 'System Camera'}")
                # Queue webhook notification for registered user
                webhook_data = {
                    'section': 'face_recognition',
                    'event': 'user_detected',
                    'user_id': user_id,
                    'camera_name': camera_name,
                    'timestamp': datetime.datetime.now().isoformat()
                }
                print(webhook_data)
                requests.post(self.webhook_url, json=webhook_data, timeout=10)
                response = requests.post(self.alarm_service, json=webhook_data, timeout=10)
                print(response.json())
                print("response:", response.text)
                print("response status code:", response.status_code)

            db_session.close()

        except Exception as e:
            logger.error(f"Error processing user attendance: {e}")
            if 'db_session' in locals():
                db_session.rollback()
                db_session.close()

    def _process_unknown_attendance(self, persistent_id, camera_name, face_image):
        """Process unknown person attendance logging in background thread."""
        try:
            # Create a new database session for this thread
            db_session = SessionLocal()

            # First, find the unknown record to get its ID
            unknown = db_session.query(models.Unknown).filter(
                models.Unknown.persistent_id == persistent_id
            ).first()

            if not unknown:
                logger.warning(f"Unknown record not found for persistent_id: {persistent_id}")
                db_session.close()
                return

            # Use the unknown ID for the unknown attendance
            unknown_id = unknown.id

            # Check if they have not been logged in the past minute
            time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
            existing_attendance = db_session.query(models.UnknownAttendance).filter(
                models.UnknownAttendance.unknown_id == unknown_id,
                models.UnknownAttendance.timestamp >= time_window_start
            ).first()

            if not existing_attendance:
                # Save the face image if provided and get the image path immediately
                image_path = None
                if face_image is not None and face_image.size > 0:
                    # Save the image synchronously to get the path for attendance record
                    from .utils import save_unknown_image
                    image_path = save_unknown_image(face_image, persistent_id)
                    if image_path:
                        logger.info(f"Saved image for unknown attendance: {image_path}")

                # Create unknown attendance record with camera name and image path
                attendance_record = models.UnknownAttendance(
                    unknown_id=unknown_id,
                    timestamp=datetime.datetime.now(),
                    camera_name=camera_name or "System Camera",
                    image_path=image_path
                )
                db_session.add(attendance_record)
                db_session.commit()
                logger.info(f"Logged appearance for unknown ID: {unknown_id} from camera: {camera_name or 'System Camera'}")
                # Queue webhook notification for unknown person
                webhook_data = {
                    'section': 'face_recognition',
                    'event': 'unknown_detected',        
                    'user_id': 'unknown',
                    'camera_name': camera_name,
                    'timestamp': datetime.datetime.now().isoformat()
                }
                print(webhook_data)
                requests.post(self.webhook_url, json=webhook_data, timeout=10)
                requests.post(self.alarm_service, json=webhook_data, timeout=10)


            db_session.close()

        except Exception as e:
            logger.error(f"Error processing unknown attendance: {e}")
            if 'db_session' in locals():
                db_session.rollback()
                db_session.close()

    def _process_save_person_image(self, face_image, track_name):
        """Process saving person image in background thread."""
        try:
            if face_image.size > 0 and face_image.shape[0] >= 40 and face_image.shape[1] >= 40:
                save_person_image(face_image, track_name)
                logger.debug(f"Saved image for person: {track_name}")
        except Exception as e:
            logger.error(f"Error saving person image: {e}")

    def _process_save_unknown_image(self, face_image, persistent_id):
        """Process saving unknown person image in background thread."""
        try:
            if face_image.size > 0 and face_image.shape[0] >= 40 and face_image.shape[1] >= 40:
                from .utils import save_unknown_image
                image_path = save_unknown_image(face_image, persistent_id)
                if image_path:
                    logger.debug(f"Saved image for unknown person {persistent_id} at {image_path}")
                return image_path
        except Exception as e:
            logger.error(f"Error saving unknown image: {e}")
        return None


    def generate_encoding(self, face_image):
        """Generates a face encoding using PyTorch FaceNet with CUDA acceleration and face alignment."""
        try:
            # Import the align_face function from utils
            from .utils import align_face

            # Apply face alignment
            aligned_face = align_face(face_image)

            # Convert BGR (OpenCV) to RGB
            aligned_face = cv2.cvtColor(aligned_face, cv2.COLOR_BGR2RGB)

            # Resize and preprocess the image (alignment already resizes to 160x160)
            if aligned_face.shape[0] != 160 or aligned_face.shape[1] != 160:
                aligned_face = cv2.resize(aligned_face, (160, 160))

            # Convert to PyTorch tensor and normalize
            face_tensor = torch.from_numpy(aligned_face).float()
            face_tensor = face_tensor.permute(2, 0, 1)  # Convert from HWC to CHW format
            face_tensor = face_tensor / 255.0  # Normalize to [0, 1]

            # Convert to tensor and move to GPU if available
            face_tensor = face_tensor.unsqueeze(0).to(self.device)

            # Generate embedding using GPU
            with torch.no_grad():
                embedding = self.embedder(face_tensor).cpu().numpy().flatten()

            return embedding
        except Exception as e:
            logger.error(f"Error generating face encoding: {e}")
            # Return a zero vector as fallback
            return np.zeros(512)

    def match_face(self, face_encoding):
        """Finds the most similar face in the database (local fallback method)."""
        highest_similarity = -1
        most_similar_user = None

        for user_id, db_encoding in self.db_encodings_cache.items():
            similarity = self.calculate_cosine_similarity(face_encoding, db_encoding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                most_similar_user = self.db_users_cache.get(user_id)

        return most_similar_user, highest_similarity

    def match_face_from_qdrant(self, face_encoding):
        """Search for the most similar face embedding in Qdrant with payloads."""
        try:
            # Convert to list if it's a numpy array
            if isinstance(face_encoding, np.ndarray):
                query_vector = face_encoding.tolist()
            else:
                query_vector = face_encoding

            # Search in Qdrant with configurable top_k
            search_result = self.qdrant_client.search(
                collection_name="face_embeddings",
                query_vector=query_vector,
                top_k=5  # Get top 5 matches for more robust matching
            )

            if not search_result:
                logger.warning("No matches found in Qdrant")
                return None, -1, None

            # Get the top match
            top_match = search_result[0]
            username = top_match.payload.get("username")
            user_id = top_match.payload.get("user_id")
            similarity_score = top_match.score  # Cosine similarity score

            # logger.debug(f"Top match: {username} (ID: {user_id}) with similarity {similarity_score}")

            # Enhanced confidence check - if the top match is significantly better than
            # the second match, we can be more confident
            if len(search_result) > 1:
                second_best_score = search_result[1].score
                confidence_gap = similarity_score - second_best_score
                # logger.debug(f"Match confidence gap: {confidence_gap}")

                # Check if the top match is an unknown person
                is_unknown = username and username.startswith("unknown_")

                # If the gap is small, we might want to be more cautious
                if confidence_gap < 0.1 and similarity_score < self.threshold + 0.1:
                    # logger.info(f"Match ambiguous: top score {similarity_score}, second {second_best_score}")

                    # If this is an unknown person match and it's ambiguous,
                    # we should still return it if it's above the threshold
                    if is_unknown and similarity_score >= self.threshold:
                        # logger.info(f"Accepting ambiguous unknown person match: {username}")
                        pass
                    else:
                        # For regular users, we could be more cautious with ambiguous matches
                        # Optionally increase the threshold for ambiguous matches
                        # We could return None here for very ambiguous matches
                        pass

            return username, similarity_score, user_id

        except Exception as e:
            # logger.error(f"Qdrant search failed: {e}")
            # Fall back to local matching
            most_similar_user, highest_similarity = self.match_face(face_encoding)
            user_id = self.get_user_id(most_similar_user) if most_similar_user else None
            return most_similar_user, highest_similarity, user_id

    def get_user_id(self, username: str):
        """Look up user ID by username using the cache."""
        for user_id, user_name in self.db_users_cache.items():
            if user_name == username:
                return user_id
        return None

    def log_attendance(self, user_id, camera_name=None):
        """Logs attendance if the user has not been logged in the past minute."""
        try:
            # Check if the session is in a valid state
            if self.db.is_active:
                time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
                if not self.db.query(models.Attendance).filter(
                    models.Attendance.user_id == user_id,
                    models.Attendance.timestamp >= time_window_start
                ).first():
                    # Create attendance record with camera name
                    attendance_record = models.Attendance(
                        user_id=user_id,
                        timestamp=datetime.datetime.now(),
                        camera_name=camera_name or "System Camera"  # Use provided camera name or default
                    )
                    self.db.add(attendance_record)
                    self.db.commit()
                    logger.info(f"Logged attendance for user ID: {user_id} from camera: {camera_name or 'System Camera'}")
            else:
                # If session is not active, create a new one
                logger.warning("Session is not active, creating a new one")
                self.db = database.SessionLocal()
                self.log_attendance(user_id, camera_name)  # Retry with new session
        except Exception as e:
            logger.error(f"Error logging attendance: {e}")
            try:
                self.db.rollback()  # Rollback the transaction in case of error
            except Exception as rollback_error:
                logger.error(f"Error during rollback: {rollback_error}")
                # If rollback fails, create a new session
                self.db = database.SessionLocal()

    def log_unknown_appearance(self, persistent_id, camera_name=None, face_image=None):
        """Logs appearance of an unknown person if they have not been logged in the past minute."""
        try:
            # First, find the unknown record to get its ID
            unknown = self.db.query(models.Unknown).filter(
                models.Unknown.persistent_id == persistent_id
            ).first()

            if not unknown:
                logger.warning(f"Unknown record not found for persistent_id: {persistent_id}")
                return

            # Use the unknown ID for the unknown attendance
            unknown_id = unknown.id

            # Check if the session is in a valid state
            if self.db.is_active:
                time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
                existing_attendance = self.db.query(models.UnknownAttendance).filter(
                    models.UnknownAttendance.unknown_id == unknown_id,
                    models.UnknownAttendance.timestamp >= time_window_start
                ).first()

                if not existing_attendance:
                    # Save the face image if provided
                    image_path = None
                    if face_image is not None and face_image.size > 0:
                        from .utils import save_unknown_image
                        # The save_unknown_image function now returns the web path directly
                        image_path = save_unknown_image(face_image, persistent_id)
                        if image_path:
                            logger.info(f"Saved image for unknown ID: {unknown_id} at {image_path}")

                    # Create unknown attendance record with camera name and image path
                    attendance_record = models.UnknownAttendance(
                        unknown_id=unknown_id,
                        timestamp=datetime.datetime.now(),
                        camera_name=camera_name or "System Camera",  # Use provided camera name or default
                        image_path=image_path  # Add the image path to the attendance record
                    )
                    self.db.add(attendance_record)
                    self.db.commit()
                    logger.info(f"Logged appearance for unknown ID: {unknown_id} from camera: {camera_name or 'System Camera'}")
            else:
                # If session is not active, create a new one
                logger.warning("Session is not active, creating a new one")
                self.db = database.SessionLocal()
                self.log_unknown_appearance(persistent_id, camera_name, face_image)  # Retry with new session
        except Exception as e:
            logger.error(f"Error logging unknown appearance: {e}")
            try:
                self.db.rollback()  # Rollback the transaction in case of error
            except Exception as rollback_error:
                logger.error(f"Error during rollback: {rollback_error}")
                # If rollback fails, create a new session
                self.db = database.SessionLocal()

    def calculate_cosine_similarity(self, enc1, enc2):
        """Calculate cosine similarity between two encodings."""
        enc1 = np.array(enc1)
        enc2 = np.array(enc2)

        if np.linalg.norm(enc1) == 0 or np.linalg.norm(enc2) == 0:
            return 0  # Handle division by zero case

        cosine_similarity = np.dot(enc1, enc2) / (np.linalg.norm(enc1) * np.linalg.norm(enc2))
        return cosine_similarity

    def reload_encodings_and_users(self):
        """Refresh cached encodings and usernames from the database."""
        self.db_encodings_cache = {enc.user_id: np.array(enc.encoding) for enc in self.db.query(Encoding).all()}
        self.db_users_cache = {user.user_id: user.username for user in self.db.query(User).all()}
        logger.info(f"User and encoding cache reloaded. {len(self.db_encodings_cache)} encodings, {len(self.db_users_cache)} users.")

    def add_user_to_qdrant(self, user_id, username, encoding):
        """Add a user to Qdrant for future face matching"""
        if not self.qdrant_client:
            logger.error("Qdrant client not available")
            return False

        try:
            success = self.qdrant_client.add_face_embedding(
                user_id=user_id,
                username=username,
                encoding=encoding
            )
            if success:
                logger.info(f"Successfully added user {username} to Qdrant")
            return success
        except Exception as e:
            logger.error(f"Failed to add user to Qdrant: {e}")
            return False

    def sync_database_to_qdrant(self):
        """Sync all users from the database to Qdrant"""
        if not self.qdrant_client:
            logger.error("Qdrant client not available")
            return False

        logger.info("Starting database to Qdrant synchronization...")
        users = self.db.query(User).all()
        success_count = 0
        failure_count = 0

        for user in users:
            # Get the corresponding encoding
            encoding = self.db.query(Encoding).filter(Encoding.user_id == user.user_id).first()
            if not encoding:
                logger.warning(f"No encoding found for user {user.username}")
                continue

            try:
                success = self.add_user_to_qdrant(
                    user_id=user.user_id,
                    username=user.username,
                    encoding=encoding.encoding
                )
                if success:
                    success_count += 1
                else:
                    failure_count += 1
            except Exception as e:
                logger.error(f"Error adding user {user.username} to Qdrant: {e}")
                failure_count += 1

        logger.info(f"Synchronization complete: {success_count} successes, {failure_count} failures")
        return success_count > 0

