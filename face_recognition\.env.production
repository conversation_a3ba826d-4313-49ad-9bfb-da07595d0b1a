# Environment Variables for Face Recognition System - PRODUCTION DEPLOYMENT
# IMPORTANT: Keep this file secure and never commit to version control
# Copy this file to .env on your production server and update the values

# Application Environment
ENVIRONMENT=production

# Database Configuration - Production
DB_PASSWORD=1234
DB_USER=root
DB_HOST=mysql
DB_NAME=face_recognition_db
DB_PORT=3306

# Security - Production (CHANGE THESE VALUES!)
SECRET_KEY=generate-a-very-secure-secret-key-for-production-use

# Qdrant Configuration - Production
QDRANT_URL=http://qdrant:6333

# HuggingFace Token (if needed for private models)
# HUGGINGFACE_TOKEN=your-production-token-here

# SSL/TLS Configuration (if using HTTPS)
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# Optional: Override YAML settings via environment variables
# APP_HOST=0.0.0.0
# APP_PORT=8000
# APP_DEBUG=false

# Email Configuration (if needed for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Monitoring and Logging
# SENTRY_DSN=your-sentry-dsn-here
# LOG_FILE_PATH=/var/log/face_recognition/app.log
