// for this
// that

// Premium Admin Panel JavaScript with Enhanced UI
function initializeAdminTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');

            const targetTab = this.getAttribute('data-tab');
            const targetPane = document.getElementById(`${targetTab}-tab`);
            
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // Load specific tab content with enhanced animations
            switch(targetTab) {
                case 'unknown-persons':
                    loadUnknownPersonsData();
                    break;
                case 'user-registration':
                    loadUserRegistrationSection();
                    break;
                case 'user-management':
                    loadUserManagementData();
                    break;
                case 'camera-management':
                    loadCameraManagementData();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        });
    });

    // Initialize first active tab
    const firstTabBtn = document.querySelector('.tab-btn.active');
    if (firstTabBtn) {
        const initialTabPaneId = `${firstTabBtn.getAttribute('data-tab')}-tab`;
        const initialTabPane = document.getElementById(initialTabPaneId);
        if (initialTabPane) {
            initialTabPane.classList.add('active');
            if (firstTabBtn.getAttribute('data-tab') === 'unknown-persons') {
                loadUnknownPersonsData();
            }
        }
    }
}

// ----------------------------------------------------
// for this
// that


async function loadUnknownPersonsData() {
    const unknownContainer = document.getElementById('unknownContainer');
    if (!unknownContainer) return;

    // Enhanced loading state
    unknownContainer.className = 'unknown-persons-grid loading';
    unknownContainer.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading unknown persons...</p>
        </div>
    `;

    try {
        const response = await fetch('/face_recognition/unknown_persons');
        const data = await response.json();

        if (response.ok) {
            unknownContainer.className = 'unknown-persons-grid';
            
            if (data.unknown_persons && data.unknown_persons.length > 0) {
                unknownContainer.innerHTML = '';
                
                // Create cards with staggered animation
                data.unknown_persons.forEach((person, index) => {
                    setTimeout(() => {
                        const card = createPersonCard(person);
                        unknownContainer.appendChild(card);
                    }, index * 100); // Staggered animation
                });
            } else {
                unknownContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-user-question"></i>
                        <h3>No Unknown Persons</h3>
                        <p>Currently, there are no unknown persons detected by the system.</p>
                    </div>
                `;
            }
        } else {
            showEnhancedNotification(data.detail || 'Failed to load unknown persons', 'error');
            unknownContainer.innerHTML = createErrorState(data.detail || 'Failed to fetch unknown persons data.');
        }
    } catch (error) {
        console.error('Error loading unknown persons:', error);
        showEnhancedNotification('Error loading unknown persons: ' + error.message, 'error');
        unknownContainer.innerHTML = createErrorState('Could not connect to the server. Please try again later.');
    }
}

function createPersonCard(person) {
    const card = document.createElement('div');
    card.className = 'person-card';
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    
    card.innerHTML = `
        <div class="person-image-container">
            <img src="${person.image_url}" alt="Unknown Person" class="person-image" loading="lazy">
            <span class="person-count">${person.count} detections</span>
        </div>
        <div class="person-info">
            <h3>Unknown Person ${person.cluster_id}</h3>
            <p><i class="fas fa-clock"></i> Last seen: ${formatDateTime(person.last_seen)}</p>
            
            <div class="attendance-section">
                <div class="attendance-header">
                    <i class="fas fa-history"></i>
                    <span>Recent Activity</span>
                </div>
                <table class="attendance-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-calendar"></i> Date & Time</th>
                            <th><i class="fas fa-camera"></i> Camera</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${formatDateTime(person.last_seen)}</td>
                            <td>Camera 1</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="registration-options">
                <div class="option-btn" onclick="showRegisterOptions('${