<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Admin Panel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 80% 70%, rgba(37, 99, 235, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border-bottom: 3px solid #3b82f6;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8, #2563eb, #3b82f6);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 900;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 1rem;
            letter-spacing: -0.02em;
        }

        .header i {
            color: #3b82f6;
            font-size: 2.25rem;
            filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.4));
        }

        .back-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: #ffffff;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.5);
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        /* Navigation Tabs */
        .nav-tabs {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .tab-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .tab-button {
            background: rgba(59, 130, 246, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.3);
            padding: 1rem 2rem;
            border-radius: 12px;
            color: #cbd5e1;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            position: relative;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .tab-button:hover {
            color: #ffffff;
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        }

        .tab-button.active {
            color: #ffffff;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-color: #3b82f6;
            box-shadow: 0 4px 25px rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
        }

        .tab-button i {
            font-size: 1.25rem;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .content-panel {
            display: none;
        }

        .content-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        .panel-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 16px 64px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .panel-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8, #2563eb, #3b82f6);
            border-radius: 20px 20px 0 0;
        }

        .panel-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
        }

        /* Panel Headers */
        .panel-header {
            font-size: 2rem;
            font-weight: 900;
            margin-bottom: 2rem;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .panel-header i {
            color: #3b82f6;
            font-size: 2.25rem;
        }

        /* Unknown Persons Panel */
        .unknown-person {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 2rem 0;
            border-bottom: 2px solid #e2e8f0;
        }

        .unknown-person:last-child {
            border-bottom: none;
        }

        .person-info {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .person-image {
            width: 100px;
            height: 100px;
            border-radius: 20px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 3px solid #3b82f6;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
        }

        .person-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .person-details h3 {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }

        .attendance-info {
            color: #64748b;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .attendance-info i {
            color: #3b82f6;
        }

        .attendance-table {
            width: 100%;
            margin-top: 1rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .attendance-table th,
        .attendance-table td {
            text-align: left;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .attendance-table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 700;
            color: #1e293b;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }

        .attendance-table td {
            background: #ffffff;
            color: #374151;
            font-weight: 600;
        }

        .delete-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .delete-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
        }

        /* Forms */
        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 700;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.125rem;
        }

        .form-label i {
            color: #3b82f6;
            font-size: 1.25rem;
        }

        .form-input {
            width: 100%;
            padding: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #ffffff;
            color: #374151;
            font-weight: 600;
        }

        .form-input::placeholder {
            color: #94a3b8;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: #ffffff;
        }

        .file-upload {
            border: 3px dashed #3b82f6;
            border-radius: 16px;
            padding: 3rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .file-upload:hover {
            background: rgba(59, 130, 246, 0.05);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
        }

        .file-upload i {
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 1rem;
        }

        .file-upload h3 {
            color: #1e293b;
            margin-bottom: 0.5rem;
            font-weight: 700;
            font-size: 1.25rem;
        }

        .file-upload p {
            color: #64748b;
            font-weight: 600;
        }

        /* Buttons */
        .register-btn, .save-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 1.25rem 2.5rem;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.125rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .register-btn:hover, .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        /* User Management */
        .user-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .search-input {
            flex: 1;
            padding: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            background: #ffffff;
            color: #374151;
            font-weight: 600;
        }

        .search-input::placeholder {
            color: #94a3b8;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: #ffffff;
        }

        .search-btn, .refresh-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 1.25rem 2rem;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .search-btn:hover, .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 16px 64px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .user-table th,
        .user-table td {
            text-align: left;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .user-table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 700;
            color: #1e293b;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }

        .user-table td {
            background: #ffffff;
            color: #374151;
            font-weight: 600;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.25rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .view-btn {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        .edit-btn {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 2px solid #10b981;
        }

        .delete-btn-small {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 2px solid #ef4444;
        }

        .action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Settings */
        .settings-section {
            margin-bottom: 3rem;
        }

        .settings-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 3px solid rgba(59, 130, 246, 0.2);
        }

        .settings-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
        }

        .settings-header h2 {
            font-size: 2rem;
            font-weight: 900;
            color: #1e293b;
        }

        .threshold-control {
            margin: 3rem 0;
            padding: 2rem;
            background: #f8fafc;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
        }

        .threshold-control h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1e293b;
        }

        .threshold-control p {
            color: #64748b;
            margin-bottom: 2rem;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .slider-container {
            position: relative;
            margin: 2rem 0;
        }

        .threshold-slider {
            width: 100%;
            height: 12px;
            border-radius: 6px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
        }

        .threshold-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
            border: 3px solid #ffffff;
        }

        .threshold-value {
            position: absolute;
            right: 0;
            top: -3rem;
            font-weight: 700;
            color: #ffffff;
            font-size: 1.5rem;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            padding: 0.75rem 1.25rem;
            border-radius: 12px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        /* Management Panel */
        .management-placeholder {
            text-align: center;
            padding: 4rem;
            color: #64748b;
        }

        .management-placeholder i {
            font-size: 4rem;
            margin-bottom: 2rem;
            color: #3b82f6;
        }

        .management-placeholder h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1e293b;
        }

        .management-placeholder p {
            font-size: 1.125rem;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .nav-tabs {
                padding: 1rem;
            }

            .tab-list {
                justify-content: center;
                gap: 0.25rem;
            }

            .tab-button {
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
            }

            .main-content {
                padding: 1rem;
            }

            .user-actions {
                flex-direction: column;
            }

            .person-info {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .settings-header {
                flex-direction: column;
                text-align: center;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-panel.active {
            animation: fadeInUp 0.6s ease;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <header class="header">
            <h1>
                <i class="fas fa-shield-alt"></i>
                Face Recognition Admin Panel
            </h1>
            <a href="#dashboard" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
        </header>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="tab-list">
                <button class="tab-button active" data-tab="unknown">
                    <i class="fas fa-user-secret"></i>
                    Unknown Persons
                </button>
                <button class="tab-button" data-tab="registration">
                    <i class="fas fa-user-plus"></i>
                    User Registration
                </button>
                <button class="tab-button" data-tab="management">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </button>
                <button class="tab-button" data-tab="system-management">
                    <i class="fas fa-cogs"></i>
                    Management
                </button>
                <button class="tab-button" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    Settings
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Unknown Persons Panel -->
            <div class="content-panel active" id="unknown">
                <div class="panel-card">
                    <div class="panel-header">
                        <i class="fas fa-user-secret"></i>
                        Unknown Persons
                    </div>
                    <div class="unknown-person">
                        <div class="person-info">
                            <div class="person-image">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f1f5f9'/%3E%3Ctext x='50' y='55' text-anchor='middle' font-family='Arial' font-size='16' fill='%23374151'%3EPhoto%3C/text%3E%3C/svg%3E" alt="Unknown Person">
                            </div>
                            <div class="person-details">
                                <h3>Unknown Person #4</h3>
                                <div class="attendance-info">
                                    <i class="fas fa-clock"></i>
                                    Attendance Records
                                </div>
                                <table class="attendance-table">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>Camera</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2025-06-04 15:56:31</td>
                                            <td>Camera 1</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <button class="delete-btn">
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- User Registration Panel -->
            <div class="content-panel" id="registration">
                <div class="panel-card">
                    <div class="panel-header">
                        <i class="fas fa-user-plus"></i>
                        Register New User
                    </div>
                    
                    <form>
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-user"></i>
                                Username
                            </label>
                            <input type="text" class="form-input" placeholder="Enter username">
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-envelope"></i>
                                Email
                            </label>
                            <input type="email" class="form-input" placeholder="Enter email address">
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-camera"></i>
                                Profile Image
                            </label>
                            <div class="file-upload">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <h3>Select Profile Image</h3>
                                <p>No file chosen</p>
                            </div>
                        </div>

                        <button type="submit" class="register-btn">
                            <i class="fas fa-plus"></i>
                            Register User
                        </button>
                    </form>
                </div>
            </div>

            <!-- User Management Panel -->
            <div class="content-panel" id="management">
                <div class="panel-card">
                    <div class="panel-header">
                        <i class="fas fa-users-cog"></i>
                        User Management
                    </div>
                    
                    <div class="user-actions">
                        <input type="text" class="search-input" placeholder="Search users...">
                        <button class="search-btn">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>

                    <table class="user-table">
                        <thead>
                            <tr>
                                <th>Avatar</th>
                                <th>Username</th>
                                <th>Email</th> 
                                