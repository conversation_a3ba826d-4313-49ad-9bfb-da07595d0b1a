from fastapi import <PERSON><PERSON><PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.templating import Jinja2Templates
from contextlib import asynccontextmanager
import os
import logging
from app.routes import router
from app.config import settings
# from app.database import create_tables

# Configure logging using settings
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format=settings.LOG_FORMAT,
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"Environment: {os.getenv('ENVIRONMENT', 'development')}")
    logger.info(f"Debug mode: {settings.APP_DEBUG}")
    logger.info(f"Host: {settings.APP_HOST}:{settings.APP_PORT}")

    yield

    # Shutdown
    logger.info("Shutting down application")

# Create FastAPI app with configuration
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.APP_DEBUG,
    lifespan=lifespan
)

# Add CORS middleware with configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_CREDENTIALS,
    allow_methods=settings.CORS_METHODS,
    allow_headers=settings.CORS_HEADERS,
)

# Create directories if they don't exist
os.makedirs(settings.TEMPLATES_PATH, exist_ok=True)
os.makedirs(settings.STATIC_PATH, exist_ok=True)
os.makedirs(settings.DATASET_PATH, exist_ok=True)

# Set up templates with configuration
templates = Jinja2Templates(directory=settings.TEMPLATES_PATH)

# Mount static files with configuration
app.mount("/static", StaticFiles(directory=settings.STATIC_PATH), name="static")

# Mount Dataset directory for serving images with configuration
app.mount("/Dataset", StaticFiles(directory=settings.DATASET_PATH), name="dataset")

# Include router without prefix
app.include_router(router, prefix="/face_recognition")

# Add root route for homepage
from fastapi import Request

@app.get("/")
async def homepage(request: Request):
    """Serve the homepage"""
    return templates.TemplateResponse("homepage.html", {"request": request})

# Initialize database tables on startup
# Note: Database initialization can be added to the lifespan function above if needed
# @app.on_event("startup")
# async def startup_event():
#     try:
#         # Create database tables if they don't exist
#         create_tables()
#         logger.info("Database tables created successfully")
#     except Exception as e:
#         logger.error(f"Error creating database tables: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.APP_HOST,
        port=settings.APP_PORT,
        reload=settings.APP_RELOAD,
        log_level=settings.LOG_LEVEL.lower()
    )