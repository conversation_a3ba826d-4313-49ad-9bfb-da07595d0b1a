/* ==================== ADMIN PANEL REDESIGN - PROFESSIONAL BLUE THEME ==================== */

/* CSS Variables for Professional Blue Theme */
:root {
  --primary-blue: #1e3a8a;
  --secondary-blue: #3b82f6;
  --dark-blue: #1e40af;
  --light-blue: #60a5fa;
  --accent-blue: #2563eb;
  --navy-blue: #1e293b;
  --slate-blue: #334155;
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Hide old tab system */
.admin-tabs,
.tab-content {
  display: none !important;
}

/* Admin Container */
.admin-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

/* Admin Navigation Header */
.admin-nav-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--dark-blue) 100%);
  color: white;
  padding: 20px 30px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  box-shadow: var(--shadow-lg);
}

.admin-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-logo i {
  font-size: 2rem;
  color: var(--light-blue);
}

.admin-logo h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Main Navigation */
.admin-main-nav {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  padding: 15px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  flex-wrap: wrap;
}

.admin-nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  position: relative;
  min-height: 48px;
}

.admin-nav-btn:hover {
  background: var(--light-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.admin-nav-btn.active {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-lg);
}

.admin-nav-btn i {
  font-size: 1.1rem;
}

.admin-nav-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.admin-nav-btn.active .admin-nav-arrow {
  transform: rotate(180deg);
}

/* Sub Navigation */
.admin-sub-nav {
  display: flex;
  gap: 6px;
  margin-bottom: 20px;
  padding: 12px 15px;
  background: linear-gradient(135deg, var(--slate-blue) 0%, var(--navy-blue) 100%);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  flex-wrap: wrap;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-sub-nav-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.admin-sub-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.admin-sub-nav-btn.active {
  background: var(--light-blue);
  color: white;
  border-color: var(--light-blue);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-sub-nav-btn i {
  font-size: 1rem;
}

/* Content Area */
.admin-content {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* Sections */
.admin-section {
  display: none;
  padding: 30px;
  min-height: 600px;
}

.admin-section.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Subsections */
.admin-subsection {
  display: none;
}

.admin-subsection.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

.admin-subsection-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-color);
}

.admin-subsection-header h2 {
  margin: 0 0 8px 0;
  color: var(--primary-blue);
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-subsection-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Grid Layout */
.admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

/* Cards */
.admin-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition);
}

.admin-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.admin-card-header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--border-color) 100%);
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-card-header h3 {
  margin: 0;
  color: var(--primary-blue);
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-card-body {
  padding: 25px;
}

/* Forms */
.admin-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.admin-form-input,
.admin-form-textarea,
.admin-method-dropdown {
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.95rem;
  transition: var(--transition);
  background: white;
}

.admin-form-input:focus,
.admin-form-textarea:focus,
.admin-method-dropdown:focus {
  outline: none;
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-form-textarea {
  min-height: 80px;
  resize: vertical;
}

/* URL Input Container */
.admin-url-input-container {
  display: flex;
  gap: 0;
}

.admin-method-dropdown {
  border-radius: 6px 0 0 6px;
  border-right: none;
  min-width: 100px;
  background: var(--bg-secondary);
}

.admin-url-input {
  border-radius: 0 6px 6px 0;
  flex: 1;
}

/* Buttons */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  min-height: 44px;
}

.admin-btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
}

.admin-btn-primary:hover {
  background: linear-gradient(135deg, var(--dark-blue) 0%, var(--primary-blue) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.admin-btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.admin-btn-secondary:hover {
  background: var(--light-blue);
  color: white;
  border-color: var(--light-blue);
}

.admin-btn-sm {
  padding: 8px 16px;
  font-size: 0.85rem;
  min-height: 36px;
}

/* Item Lists */
.admin-item-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Alert Management Specific Styles */
.admin-alert-status-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 6px;
  margin-bottom: 20px;
}

.admin-alert-status-text {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.admin-alert-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ef4444;
  animation: pulse 2s infinite;
}

.admin-alert-status-badge {
  padding: 6px 12px;
  background: #ef4444;
  color: white;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.admin-alert-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.admin-days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.admin-day-btn {
  padding: 8px 4px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background: white;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
}

.admin-day-btn:hover {
  border-color: var(--secondary-blue);
  background: var(--light-blue);
  color: white;
}

.admin-day-btn.active {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
  color: white;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 15px;
  }

  .admin-main-nav,
  .admin-sub-nav {
    flex-direction: column;
  }

  .admin-grid {
    grid-template-columns: 1fr;
  }

  .admin-nav-btn,
  .admin-sub-nav-btn {
    justify-content: center;
  }

  .admin-days-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .admin-alert-controls {
    flex-direction: column;
  }
}
