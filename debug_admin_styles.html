<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Style Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔧 Admin Panel Style Debug Tool</h1>
    
    <div class="debug-section">
        <h2 class="debug-title">1. CSS File Loading Test</h2>
        <p>Testing if the admin panel CSS is loading correctly...</p>
        <button class="test-button" onclick="testCSSLoading()">Test CSS Loading</button>
        <div id="css-test-result"></div>
    </div>

    <div class="debug-section">
        <h2 class="debug-title">2. HTML Structure Test</h2>
        <p>Checking if the new HTML structure exists...</p>
        <button class="test-button" onclick="testHTMLStructure()">Test HTML Structure</button>
        <div id="html-test-result"></div>
    </div>

    <div class="debug-section">
        <h2 class="debug-title">3. Style Application Test</h2>
        <p>Testing if the new styles are being applied...</p>
        <button class="test-button" onclick="testStyleApplication()">Test Style Application</button>
        <div id="style-test-result"></div>
    </div>

    <div class="debug-section">
        <h2 class="debug-title">4. Cache Status</h2>
        <p>Checking browser cache status...</p>
        <button class="test-button" onclick="checkCacheStatus()">Check Cache</button>
        <div id="cache-test-result"></div>
    </div>

    <div class="debug-section">
        <h2 class="debug-title">5. Quick Fixes</h2>
        <p>Try these solutions:</p>
        <button class="test-button" onclick="clearCache()">Clear Cache & Reload</button>
        <button class="test-button" onclick="openAdminPanel()">Open Admin Panel</button>
        <button class="test-button" onclick="forceReload()">Force Hard Reload</button>
    </div>

    <script>
        function testCSSLoading() {
            const result = document.getElementById('css-test-result');
            result.innerHTML = '<p>Testing CSS loading...</p>';
            
            // Test if we can fetch the CSS file
            fetch('/static/admin_panel.css?v=2.0')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    }
                    throw new Error(`HTTP ${response.status}`);
                })
                .then(cssText => {
                    const hasNewStyles = cssText.includes('admin-dashboard') && 
                                       cssText.includes('admin-nav-tab') && 
                                       cssText.includes('Professional Combat-Style');
                    
                    if (hasNewStyles) {
                        result.innerHTML = `
                            <p class="status-good">✅ CSS file loaded successfully!</p>
                            <p class="status-good">✅ New styles detected in CSS file</p>
                            <div class="code-block">CSS file size: ${cssText.length} characters</div>
                        `;
                    } else {
                        result.innerHTML = `
                            <p class="status-warning">⚠️ CSS file loaded but new styles not found</p>
                            <p>The CSS file might be cached or not updated properly.</p>
                        `;
                    }
                })
                .catch(error => {
                    result.innerHTML = `
                        <p class="status-bad">❌ Failed to load CSS file</p>
                        <p>Error: ${error.message}</p>
                    `;
                });
        }

        function testHTMLStructure() {
            const result = document.getElementById('html-test-result');
            result.innerHTML = '<p>Testing HTML structure...</p>';
            
            // Test if we can fetch the admin HTML
            fetch('/admin')
                .then(response => response.text())
                .then(htmlText => {
                    const hasNewStructure = htmlText.includes('admin-dashboard') && 
                                          htmlText.includes('admin-nav-tab') && 
                                          htmlText.includes('Professional Combat-Style');
                    
                    if (hasNewStructure) {
                        result.innerHTML = `
                            <p class="status-good">✅ New HTML structure detected!</p>
                            <p class="status-good">✅ Admin dashboard elements found</p>
                        `;
                    } else {
                        result.innerHTML = `
                            <p class="status-bad">❌ Old HTML structure still present</p>
                            <p>The HTML file might not be updated or server needs restart.</p>
                        `;
                    }
                })
                .catch(error => {
                    result.innerHTML = `
                        <p class="status-bad">❌ Failed to fetch admin page</p>
                        <p>Error: ${error.message}</p>
                    `;
                });
        }

        function testStyleApplication() {
            const result = document.getElementById('style-test-result');
            
            // Create a test element with the new classes
            const testDiv = document.createElement('div');
            testDiv.className = 'admin-dashboard';
            testDiv.style.position = 'absolute';
            testDiv.style.left = '-9999px';
            document.body.appendChild(testDiv);
            
            const computedStyle = window.getComputedStyle(testDiv);
            const hasNewStyles = computedStyle.background.includes('linear-gradient') || 
                                computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            document.body.removeChild(testDiv);
            
            if (hasNewStyles) {
                result.innerHTML = `
                    <p class="status-good">✅ New styles are being applied!</p>
                    <p>Background: ${computedStyle.background}</p>
                `;
            } else {
                result.innerHTML = `
                    <p class="status-warning">⚠️ New styles not being applied</p>
                    <p>This might be a cache issue or CSS specificity problem.</p>
                `;
            }
        }

        function checkCacheStatus() {
            const result = document.getElementById('cache-test-result');
            const timestamp = new Date().getTime();
            
            result.innerHTML = `
                <p>Current timestamp: ${timestamp}</p>
                <p>CSS URL: /static/admin_panel.css?v=2.0</p>
                <p>Recommended: Clear browser cache and hard refresh (Ctrl+F5)</p>
            `;
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            location.reload(true);
        }

        function openAdminPanel() {
            window.open('/admin', '_blank');
        }

        function forceReload() {
            location.reload(true);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testCSSLoading();
                testHTMLStructure();
                testStyleApplication();
                checkCacheStatus();
            }, 1000);
        });
    </script>
</body>
</html>
