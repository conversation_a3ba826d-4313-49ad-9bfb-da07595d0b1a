
:root {
    --primary-color: #8b5cf6;
    --secondary-color: #7c3aed;
    --accent-color: #a855f7;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --bg-light: #f1f5f9;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.header {
    /* background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%); */
    background: linear-gradient(135deg, #180a39 0%, #1b0147 50%, #19033c 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 0;
    border-radius: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-title i {
    font-size: 1.5rem;
    color: #fbbf24;
}

.header-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

.page-title {
    text-align: center;
    color: var(--dark-color);
    margin: 0;
    padding: 0;
    font-size: 2.5rem;
    font-weight: 700;
    display: none; /* Hide since we have header title */
}

.admin-container {
    padding: 0 20px 40px 20px;
}

/* Admin Tabs */
.admin-tabs {
    display: flex;
    justify-content: center;
    margin: 30px auto;
    background: white;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 4px;
    max-width: 1200px;
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    /* color: var(--text-light); */
    color: #321b77;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    min-width: 140px;
    justify-content: center;
    position: relative;
}

.tab-btn:hover {
    background: rgba(139, 92, 246, 0.1);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.tab-btn.active {
    /* background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); */
    background: linear-gradient(135deg, #625d6d 0%, #3d295e 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.tab-btn i {
    font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0 auto 30px auto;
    max-width: 1200px;
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.tab-pane {
    display: none;
    padding: 40px;
    min-height: 400px;
}

.tab-pane.active {
    display: block;
}

/* Cards */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    border-bottom: none;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 20px;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--text-light), var(--dark-color));
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(127, 140, 141, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #229954);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d68910);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

/* Loading and Empty States */
.loading {
    text-align: center;
    padding: 60px 40px;
    color: var(--text-light);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.loading i {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.loading p {
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

/* Header Content */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
}

.back-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Management Mode Styles */
body.management-active .admin-container {
    display: none !important;
}

body.management-active #management-tab {
    display: block !important;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

/* Cards and Content Styling - Match face_recognition.html */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(59, 130, 246, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Styling - Match face_recognition.html */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* Table Styling */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.table th {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.table td {
    color: #6b7280;
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: rgba(59, 130, 246, 0.02);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.empty-state h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0 0 1.5rem 0;
}

/* Loading State */
.loading {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.loading p {
    font-size: 0.875rem;
    margin: 0;
}

/* Management Container Styles */
.management-container {
    display: flex;
    height: 100vh;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    overflow: hidden;
    position: relative;
}

.management-back-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10001;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-back-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

#management-tab {
    display: none;
}

#management-tab.active {
    display: block;
}

/* Management Sidebar */
.management-sidebar {
    width: 240px;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(59, 130, 246, 0.1);
}

/* Management Content Area */
.management-content-area {
    flex: 1;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Management Sidebar Logo */
.management-sidebar-logo {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    background: rgba(30, 41, 59, 0.3);
    backdrop-filter: blur(10px);
}

.management-logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.management-logo-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.management-logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.management-logo-title {
    font-size: 1rem;
    font-weight: 700;
    color: #f8fafc;
    margin: 0;
    letter-spacing: -0.25px;
}

.management-logo-subtitle {
    font-size: 0.6875rem;
    color: #64748b;
    font-weight: 500;
    margin: 0;
}

/* Navigation Menu */
.management-nav-container {
    flex: 1;
    padding: 0.75rem 0;
    overflow-y: auto;
}

.management-nav-header {
    padding: 0 1.25rem 0.5rem 1.25rem;
}

.management-nav-section-title {
    font-size: 0.625rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #94a3b8;
    margin-bottom: 0.5rem;
}

.management-nav-menu {
    padding: 0 0.5rem;
}

.management-nav-item {
    position: relative;
    display: block;
    margin-bottom: 0.25rem;
    border-radius: 12px;
    overflow: hidden;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    text-decoration: none;
}

.management-nav-item-content {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    padding: 0.625rem 0.875rem;
    position: relative;
    z-index: 2;
}

.management-nav-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 8px;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.management-nav-icon {
    font-size: 0.8rem;
    color: #cbd5e1;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.management-nav-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #cbd5e1;
    margin: 0;
    line-height: 1.2;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.management-nav-subtitle {
    font-size: 0.6rem;
    color: #64748b;
    margin: 0;
    line-height: 1.2;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.management-nav-item:hover {
    transform: translateX(4px);
    background: rgba(30, 41, 59, 0.9);
}

.management-nav-item.active {
    background: rgba(59, 130, 246, 0.1);
    transform: translateX(4px);
}

.management-nav-item.active .management-nav-icon-container {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.management-nav-item.active .management-nav-icon {
    color: white;
}

.management-nav-item.active .management-nav-title {
    color: #f8fafc;
    font-weight: 700;
}

.management-nav-item.active .management-nav-subtitle {
    color: #3b82f6;
}

/* Management Content Header */
.management-content-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    padding: 1.5rem 2rem;
    backdrop-filter: blur(10px);
}

.management-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.management-page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f8fafc;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.management-page-subtitle {
    font-size: 0.875rem;
    color: #cbd5e1;
    margin: 0.25rem 0 0 0;
}

.management-header-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.management-stat-card {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    text-align: center;
    backdrop-filter: blur(10px);
}

.management-stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #3b82f6;
    margin: 0;
}

.management-stat-label {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0;
}

/* Management Content Body */
.management-content-body {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: rgba(15, 23, 42, 0.3);
}

.management-module-section {
    display: none;
}

.management-module-section.active {
    display: block;
}

/* Management Cards */
.management-premium-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.05);
}

.management-card-header {
    margin-bottom: 1.5rem;
}

.management-card-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.management-card-title h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f8fafc;
    margin: 0;
}

.management-card-description {
    font-size: 0.875rem;
    color: #cbd5e1;
    margin: 0;
}

/* Management Forms */
.management-form-premium {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.management-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.management-form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #f8fafc;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.management-form-input {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: #f8fafc;
    font-size: 0.875rem;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.management-form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.management-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.management-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.management-btn-full {
    width: 100%;
}

/* Empty State */
.management-empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #64748b;
}

.management-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.management-empty-state h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #cbd5e1;
    margin: 0 0 0.5rem 0;
}

.management-empty-state p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

/* Notification Styles - Exactly like crowd_detection */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.notification-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.notification-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.notification-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-content i {
    font-size: 18px;
    flex-shrink: 0;
}

.notification-content span {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
}
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #1abc9c;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --success-color: #2ecc71;
    --text-light: #ecf0f1;
    --text-dark: #2c3e50;
    --bg-light: #f5f7fa;
    --bg-dark: #34495e;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-light);
    color: var(--text-dark);
    line-height: 1.6;
}

.header {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.header-title {
    font-size: 20px;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
}

.page-title {
    text-align: center;
    margin: 30px 0;
    color: var(--primary-color);
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.page-title:after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px 40px;
}

.card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.card-images {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.card-images img {
    max-width: 140px;
    border-radius: 8px;
    border: 1px solid #eee;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.card-images img:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-container {
    background-color: var(--bg-light);
    padding: 20px;
    border-radius: 8px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: var(--transition);
    background-color: white;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.empty-state i {
    font-size: 48px;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.empty-state p {
    color: #666;
    margin-bottom: 20px;
}

.loading {
    text-align: center;
    padding: 30px;
    color: var(--primary-color);
}

/* Admin Tabs */
.admin-tabs {
    display: flex;
    background-color: var(--bg-light);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
}



.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Management Modal Styles */
.management-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.management-modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.management-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.management-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.management-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.management-modal-close:hover {
    background: #e0e0e0;
    color: #333;
}

.management-modal-body {
    padding: 20px;
}

.management-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.management-form-group {
    margin-bottom: 20px;
}

.management-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.management-form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.management-form-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.management-form-input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.management-form-group small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 12px;
}

/* Management Button Styles */
.management-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s;
}

.management-btn-primary {
    background: #007bff;
    color: white;
}

.management-btn-primary:hover {
    background: #0056b3;
}

.management-btn-secondary {
    background: #6c757d;
    color: white;
}

.management-btn-secondary:hover {
    background: #545b62;
}

.management-btn-warning {
    background: #ffc107;
    color: #212529;
}

.management-btn-warning:hover {
    background: #e0a800;
}

.management-btn-success {
    background: #28a745;
    color: white;
}

.management-btn-success:hover {
    background: #1e7e34;
}

.management-btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Management Badge Styles */
.management-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.management-badge-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.management-badge-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.management-badge-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: var(--transition);
    background-color: var(--bg-light);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.file-input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.file-status {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* Camera List Section */
.camera-list-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    color: var(--primary-color);
    margin: 0;
}

.camera-list, .users-list {
    background-color: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    min-height: 100px;
}

.camera-table, .users-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.camera-table th, .users-table th,
.camera-table td, .users-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.camera-table th, .users-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
}

.camera-table th:first-child, .users-table th:first-child {
    border-top-left-radius: 8px;
}

.camera-table th:last-child, .users-table th:last-child {
    border-top-right-radius: 8px;
}

.camera-table tr:last-child td, .users-table tr:last-child td {
    border-bottom: none;
}

.camera-table tr:hover, .users-table tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.camera-url, .user-email {
    font-size: 12px;
    color: #666;
    word-break: break-all;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

/* User Management Styles */
.user-management-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.search-box {
    display: flex;
    gap: 10px;
    width: 60%;
}

.search-box input {
    flex-grow: 1;
}

.user-actions {
    display: flex;
    gap: 5px;
}

.user-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #eee;
    transition: var(--transition);
}

.user-image:hover {
    transform: scale(1.1);
    border-color: var(--secondary-color);
}

/* Attendance section styling */
.attendance-section {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.attendance-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.attendance-table th,
.attendance-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.attendance-table th {
    background-color: var(--primary-color);
    color: white;
}

.attendance-table tr:last-child td {
    border-bottom: none;
}

.attendance-table tr:hover td {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Clustering Styles */
.unknown-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.control-buttons {
    display: flex;
    gap: 10px;
}

.cluster-settings {
    display: flex;
    gap: 15px;
    align-items: center;
}

.cluster-settings .form-group {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cluster-settings .form-control {
    width: 70px;
    padding: 8px;
}

.cluster-settings label {
    margin-bottom: 0;
    white-space: nowrap;
    cursor: help;
}

.cluster-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: #666;
    background-color: rgba(243, 156, 18, 0.1);
    padding: 6px 10px;
    border-radius: 4px;
    border-left: 3px solid var(--warning-color);
}

.cluster-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-left: 5px solid var(--secondary-color);
}

.cluster-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.cluster-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.cluster-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.cluster-count {
    background-color: var(--secondary-color);
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.cluster-faces {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.cluster-faces img {
    max-width: 120px;
    border-radius: 8px;
    border: 1px solid #eee;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.cluster-faces img:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modal Styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 30px;
    border: none;
    width: 500px;
    max-width: 90%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    animation: slideIn 0.3s ease;
}

.modal-content h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 25px;
    text-align: center;
    position: relative;
}

.modal-content h3:after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.modal-content button {
    margin-bottom: 10px;
}

/* Camera Container */
#camera-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: none;
}

.camera-content {
    background-color: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.camera-content h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 24px;
}

.camera-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
}

#video, #image-preview {
    width: 100%;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    background-color: #f0f0f0;
    margin: 0 auto;
    display: block;
}

#video {
    height: 300px;
    object-fit: cover;
}

#image-preview {
    margin-top: 15px;
    border: 3px solid var(--success-color);
    max-height: 300px;
    object-fit: contain;
}

/* Only apply animation to the spinner icon */
.fa-spinner.fa-spin {
    font-size: 40px;
    animation: spin 1s linear infinite;
}

/* Explicitly disable animation for all other icons */
.fas:not(.fa-spin) {
    animation: none !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Confirmation Dialog */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    display: none;
    justify-content: center;
    align-items: center;
}

.confirm-dialog::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(3px);
}

.confirm-dialog h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-color);
    position: relative;
    font-size: 24px;
}

.confirm-dialog p {
    margin-bottom: 20px;
    color: var(--text-dark);
    position: relative;
    font-size: 16px;
    line-height: 1.5;
}

.confirm-dialog-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 400px;
    max-width: 90%;
    text-align: center;
    position: relative;
    z-index: 1101;
    animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.confirm-dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    position: relative;
    margin-top: 25px;
}

.confirm-dialog-buttons button {
    min-width: 100px;
    padding: 10px 15px;
    font-weight: 500;
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: white;
    padding: 12px 25px;
    border-radius: 8px;
    z-index: 1200;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
    transform: translate(-50%, -10px);
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--danger-color);
}

.toast.info {
    background-color: var(--secondary-color);
}

/* Responsive Design */
/* Card Images and Image Container Styles */
.card-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.image-container {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.image-container:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-images img, .cluster-faces img, .image-container img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #eee;
    transition: var(--transition);
}

.image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-container:hover .image-actions {
    opacity: 1;
}

.delete-image-btn, .move-image-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.delete-image-btn {
    background-color: rgba(220, 53, 69, 0.8);
}

.move-image-btn {
    background-color: rgba(0, 123, 255, 0.8);
}

.delete-image-btn i, .move-image-btn i {
    font-size: 12px;
}

.delete-image-btn:hover {
    background-color: rgba(220, 53, 69, 1);
}

.move-image-btn:hover {
    background-color: rgba(0, 123, 255, 1);
}

/* Cluster options in move modal */
.clusters-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 15px 0;
    max-height: 300px;
    overflow-y: auto;
}

.cluster-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-size: 14px;
}

.cluster-option:hover {
    background-color: var(--primary-color);
    color: white;
}

.cluster-option i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Settings Tiles */
.settings-tiles {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
}

.settings-tile {
    flex: 1;
    min-width: 150px;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.settings-tile i {
    font-size: 24px;
    color: var(--primary-color);
}

.settings-tile span {
    font-weight: 500;
}

.settings-tile:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.settings-tile.active {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.settings-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.settings-section.active {
    display: block;
}

/* Settings Sidebar */
.settings-sidebar {
    display: flex;
    flex-direction: column;
    width: 250px;
    background-color: var(--bg-light);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-right: 25px;
    float: left;
}

.settings-sidebar-item {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.settings-sidebar-item i {
    font-size: 18px;
    width: 24px;
    text-align: center;
    color: var(--primary-color);
}

.settings-sidebar-item:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.settings-sidebar-item.active {
    background-color: white;
    border-left-color: var(--secondary-color);
    font-weight: 500;
}

.settings-subsection {
    display: none;
    animation: fadeIn 0.3s ease;
    margin-left: 275px;
}

.settings-subsection.active {
    display: block;
}

@media (max-width: 768px) {
    .settings-sidebar {
    width: 100%;
    float: none;
    margin-right: 0;
    margin-bottom: 20px;
    flex-direction: row;
    overflow-x: auto;
    }

    .settings-sidebar-item {
    border-left: none;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    }

    .settings-sidebar-item.active {
    border-left-color: transparent;
    border-bottom-color: var(--secondary-color);
    }

    .settings-subsection {
    margin-left: 0;
    }
}

/* Webhook, WhatsApp, SMS, and Email Lists */
.webhook-list,
.whatsapp-list,
.sms-list,
.email-list {
    background-color: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.webhook-table,
.whatsapp-table,
.sms-table,
.email-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.webhook-table th,
.webhook-table td,
.whatsapp-table th,
.whatsapp-table td,
.sms-table th,
.sms-table td,
.email-table th,
.email-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.webhook-table th,
.whatsapp-table th,
.sms-table th,
.email-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
}

.webhook-table th:first-child,
.whatsapp-table th:first-child,
.sms-table th:first-child,
.email-table th:first-child {
    border-top-left-radius: 8px;
}

.webhook-table th:last-child,
.whatsapp-table th:last-child,
.sms-table th:last-child,
.email-table th:last-child {
    border-top-right-radius: 8px;
}

.webhook-table tr:last-child td,
.whatsapp-table tr:last-child td,
.sms-table tr:last-child td,
.email-table tr:last-child td {
    border-bottom: none;
}

.webhook-table tr:hover,
.whatsapp-table tr:hover,
.sms-table tr:hover,
.email-table tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.webhook-url,
.whatsapp-phone,
.sms-phone,
.email-address {
    font-size: 14px;
    word-break: break-all;
    font-family: monospace;
}

.webhook-description,
.whatsapp-name,
.sms-name,
.email-name {
    font-size: 14px;
    color: #666;
}

.webhook-template {
    font-size: 12px;
    font-family: monospace;
    max-width: 200px;
    word-break: break-word;
}

.webhook-template span {
    cursor: help;
}

.webhook-status,
.whatsapp-status,
.sms-status,
.email-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.webhook-status.active,
.whatsapp-status.active,
.sms-status.active,
.email-status.active {
    background-color: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.webhook-status.inactive,
.whatsapp-status.inactive,
.sms-status.inactive,
.email-status.inactive {
    background-color: rgba(231, 76, 60, 0.2);
    color: #c0392b;
}

.webhook-actions,
.whatsapp-actions,
.sms-actions,
.email-actions {
    display: flex;
    gap: 5px;
}

@media (max-width: 768px) {
    .form-actions {
    flex-direction: column;
    }

    .card-images img, .image-container, .cluster-faces img, .unknown-image {
    max-width: 100px;
    max-height: 100px;
    }

    .settings-tiles {
    flex-direction: column;
    }

    .settings-tile {
    min-width: 100%;
    }
}

/* Template Editor Styles - Compact Professional Layout */
.template-editor-compact {
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: white;
    padding: 20px;
}

.template-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    align-items: start;
}

.template-input-column {
    display: flex;
    flex-direction: column;
}

.template-input-column label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.template-textarea {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    resize: vertical;
    min-height: 160px;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
}

.template-reference-column {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    height: fit-content;
}

.variables-reference h6 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.variables-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 20px;
}

.var-tag {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    font-weight: bold;
    user-select: all;
    cursor: text;
    border: 1px solid #bbdefb;
}

.example-section {
    margin-bottom: 20px;
}

.example-template {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.example-template pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #333;
    line-height: 1.4;
}

.preview-section {
    margin-bottom: 0;
}

.template-preview-compact {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    min-height: 60px;
    max-height: 120px;
    overflow-y: auto;
}

.template-preview-compact pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
}



/* Webhook Header Styles */
.webhook-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.section-info h3 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 24px;
}

.section-info p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Webhook Form Section Styles */
.webhook-form-section {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
    opacity: 0;
    transform: translateY(-10px);
    }
    to {
    opacity: 1;
    transform: translateY(0);
    }
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
}

.form-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.webhook-form-section form {
    padding: 25px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Webhook Cards Styles */
.webhook-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.webhook-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.3s ease;
}

.webhook-card:hover {
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.webhook-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.webhook-info {
    flex: 1;
}

.webhook-details {
    display: flex;
    gap: 15px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.webhook-method,
.webhook-headers {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.85em;
    color: #495057;
}

.webhook-method {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #1976d2;
}

/* Method-specific colors for webhook cards */
.webhook-method-get {
    background-color: rgba(97, 175, 254, 0.1);
    border-color: #61affe;
    color: #61affe;
}

.webhook-method-post {
    background-color: rgba(73, 204, 144, 0.1);
    border-color: #49cc90;
    color: #49cc90;
}

.webhook-method-put {
    background-color: rgba(252, 161, 48, 0.1);
    border-color: #fca130;
    color: #fca130;
}

.webhook-method-patch {
    background-color: rgba(80, 227, 194, 0.1);
    border-color: #50e3c2;
    color: #50e3c2;
}

.webhook-method-delete {
    background-color: rgba(249, 62, 62, 0.1);
    border-color: #f93e3e;
    color: #f93e3e;
}

.webhook-headers {
    background-color: #f3e5f5;
    border-color: #ce93d8;
    color: #7b1fa2;
}

/* Postman-style URL container */
.postman-url-container {
    display: flex;
    border: 1px solid #ced4da;
    border-radius: 4px;
    overflow: hidden;
    background-color: white;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.postman-url-container:focus-within {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.method-dropdown {
    border: none;
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    font-size: 14px;
    padding: 8px 12px;
    min-width: 100px;
    border-right: 1px solid #dee2e6;
    outline: none;
    cursor: pointer;
    text-align: center;
}

.method-dropdown:focus {
    background-color: #e9ecef;
    outline: none;
}

.method-dropdown option {
    font-weight: 600;
    padding: 5px;
}

.url-input {
    flex: 1;
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    outline: none;
    background-color: white;
}

.url-input:focus {
    outline: none;
}

.url-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Method-specific colors (like Postman) */
.method-dropdown option[value="GET"] {
    color: #61affe;
}

.method-dropdown option[value="POST"] {
    color: #49cc90;
}

.method-dropdown option[value="PUT"] {
    color: #fca130;
}

.method-dropdown option[value="PATCH"] {
    color: #50e3c2;
}

.method-dropdown option[value="DELETE"] {
    color: #f93e3e;
}

.webhook-url-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.webhook-description {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.webhook-status-badge {
    margin-left: 20px;
}

.webhook-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.webhook-status.active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.webhook-status.inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.webhook-template-section {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.webhook-template-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-preview-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    position: relative;
}

.template-content {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #333;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 150px;
    overflow-y: auto;
}

.view-full-template {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    font-size: 11px;
}

.webhook-actions {
    padding: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background-color: #f8f9fa;
}

.webhook-actions .btn {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .template-layout {
    grid-template-columns: 1fr;
    gap: 15px;
    }

    .template-reference-column {
    order: -1;
    }

    .variables-compact {
    justify-content: center;
    }

    .webhook-card-header {
    flex-direction: column;
    gap: 15px;
    }

    .webhook-status-badge {
    margin-left: 0;
    align-self: flex-start;
    }

    .webhook-actions {
    flex-direction: column;
    gap: 8px;
    }

    .webhook-actions .btn {
    justify-content: center;
    }
}

/* Template Modal Styles */
.template-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.template-modal-content {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 80%;
    max-height: 80%;
    width: 600px;
    display: flex;
    flex-direction: column;
}

.template-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
}

.template-modal-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.template-modal-close:hover {
    background-color: #f8f9fa;
    color: #333;
}

.template-modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.template-modal-code {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #333;
    line-height: 1.5;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.template-modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Camera Permissions Modal Styles */
.camera-permissions-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.camera-permissions-content {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.camera-permissions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-radius: 12px 12px 0 0;
}

.camera-permissions-header h3 {
    margin: 0;
    color: #2e7d32;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.camera-permissions-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.camera-permissions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.camera-permission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.camera-permission-item:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.camera-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.camera-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.camera-url {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    word-break: break-all;
}

.permission-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.permission-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.permission-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.permission-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.permission-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .permission-slider {
    background-color: #4caf50;
}

input:checked + .permission-slider:before {
    transform: translateX(26px);
}

.permission-status {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.permission-status.allowed {
    color: #4caf50;
}

.permission-status.denied {
    color: #f44336;
}

.camera-permissions-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.permissions-summary {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.summary-count {
    font-weight: 600;
    color: #333;
}


/* ==================== MANAGEMENT TAB STYLES ==================== */

/* Enhanced CSS Variables for Management Tab */
:root {
  /* Primary Colors */
  --mgmt-primary: #3b82f6;
  --mgmt-primary-dark: #2563eb;
  --mgmt-primary-light: #60a5fa;
  --mgmt-primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

  /* Status Colors */
  --mgmt-success: #10b981;
  --mgmt-success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --mgmt-danger: #ef4444;
  --mgmt-danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --mgmt-warning: #f59e0b;
  --mgmt-warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --mgmt-info: #06b6d4;
  --mgmt-info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);

  /* Special Colors */
  --mgmt-whatsapp: #25d366;
  --mgmt-sms: #ff6b35;
  --mgmt-email: #ea4335;

  /* Dark Theme Backgrounds */
  --mgmt-dark-bg-primary: #0f172a;
  --mgmt-dark-bg-secondary: #1e293b;
  --mgmt-dark-bg-tertiary: #334155;
  --mgmt-dark-surface: rgba(30, 41, 59, 0.8);
  --mgmt-dark-surface-hover: rgba(30, 41, 59, 0.9);
  --mgmt-dark-surface-active: rgba(59, 130, 246, 0.1);
  --mgmt-dark-border: rgba(59, 130, 246, 0.1);
  --mgmt-dark-border-hover: rgba(59, 130, 246, 0.2);

  /* Dark Theme Text Colors */
  --mgmt-text-primary: #f8fafc;
  --mgmt-text-secondary: #cbd5e1;
  --mgmt-text-muted: #64748b;
  --mgmt-text-accent: #94a3b8;

  /* Dark Theme Background Gradients */
  --mgmt-bg-gradient-main: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  --mgmt-bg-gradient-sidebar: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  --mgmt-bg-gradient-header: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);

  /* Dark Theme Shadows */
  --mgmt-shadow-premium: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.1);
  --mgmt-shadow-card: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.05);
  --mgmt-shadow-button: 0 2px 8px rgba(59, 130, 246, 0.3);
  --mgmt-shadow-button-hover: 0 4px 12px rgba(59, 130, 246, 0.4);
  --mgmt-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

  /* Border Radius */
  --mgmt-radius-sm: 8px;
  --mgmt-radius-md: 12px;
  --mgmt-radius-lg: 16px;
  --mgmt-radius-xl: 20px;
  --mgmt-radius-2xl: 24px;
  --mgmt-radius-full: 9999px;

  /* Transitions */
  --mgmt-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --mgmt-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --mgmt-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --mgmt-transition-bounce: 400ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Management Container */
.management-container {
  display: flex;
  height: 100vh;
  background: var(--mgmt-bg-gradient-main);
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* Hide management container by default */
#management-tab {
  display: none;
}

/* Show management container when active */
#management-tab.active {
  display: block;
}

/* Hide other content when management is active */
body.management-active .admin-container {
  display: none !important;
}

body.management-active #management-tab {
  display: block !important;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

/* Add back button to management interface */
.management-back-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10001;
  background: var(--mgmt-danger-gradient);
  color: white;
  border: none;
  border-radius: var(--mgmt-radius-full);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--mgmt-shadow-button);
}

.management-back-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

/* Management Sidebar */
.management-sidebar {
  width: 240px;
  background: var(--mgmt-bg-gradient-sidebar);
  display: flex;
  flex-direction: column;
  box-shadow: var(--mgmt-shadow-premium);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--mgmt-dark-border);
}

.management-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(59, 130, 246, 0.05) 50%,
    transparent 70%
  );
  pointer-events: none;
}

/* Management Sidebar Logo */
.management-sidebar-logo {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--mgmt-dark-border);
  background: rgba(30, 41, 59, 0.3);
  backdrop-filter: blur(10px);
}

.management-logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.management-logo-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.management-logo-icon {
  width: 32px;
  height: 32px;
  background: var(--mgmt-primary-gradient);
  border-radius: var(--mgmt-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  box-shadow: var(--mgmt-shadow-button);
  position: relative;
  overflow: hidden;
}

.management-logo-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(255, 255, 255, 0.1) 2px,
    rgba(255, 255, 255, 0.1) 4px
  );
  animation: mgmt-shimmer 3s linear infinite;
}

.management-logo-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--mgmt-primary-gradient);
  border-radius: var(--mgmt-radius-lg);
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
}

.management-logo-content {
  flex: 1;
}

.management-logo-title {
  font-size: 1rem;
  font-weight: 700;
  color: var(--mgmt-text-primary);
  margin: 0;
  letter-spacing: -0.25px;
}

.management-logo-subtitle {
  font-size: 0.6875rem;
  color: var(--mgmt-text-muted);
  font-weight: 500;
  margin: 0;
}

@keyframes mgmt-shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Management Navigation Container */
.management-nav-container {
  flex: 1;
  padding: 0.75rem 0;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.management-nav-container::-webkit-scrollbar {
  display: none;
}

.management-nav-header {
  padding: 0 1.25rem 0.5rem 1.25rem;
}

.management-nav-section-title {
  font-size: 0.625rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--mgmt-text-accent);
  margin-bottom: 0.5rem;
}

.management-nav-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--mgmt-dark-border), transparent);
  margin-top: 0.5rem;
}

/* Management Navigation Menu */
.management-nav-menu {
  padding: 0 0.5rem;
}

.management-nav-item {
  position: relative;
  display: block;
  margin-bottom: 0.25rem;
  border-radius: var(--mgmt-radius-md);
  overflow: hidden;
  background: none;
  border: none;
  cursor: pointer;
  transition: all var(--mgmt-transition-bounce);
  width: 100%;
  text-decoration: none;
}

.management-nav-item-content {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.625rem 0.875rem;
  position: relative;
  z-index: 2;
}

.management-nav-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--mgmt-radius-sm);
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  transition: all var(--mgmt-transition-normal);
}

.management-nav-icon {
  font-size: 0.8rem;
  color: var(--mgmt-text-secondary);
  transition: all var(--mgmt-transition-normal);
}

.management-nav-icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--mgmt-primary-gradient);
  border-radius: var(--mgmt-radius-md);
  opacity: 0;
  transition: all var(--mgmt-transition-normal);
}

.management-nav-text-container {
  flex: 1;
}

.management-nav-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--mgmt-text-secondary);
  margin: 0;
  line-height: 1.2;
  transition: all var(--mgmt-transition-normal);
}

.management-nav-subtitle {
  font-size: 0.6rem;
  color: var(--mgmt-text-muted);
  margin: 0;
  line-height: 1.2;
  transition: all var(--mgmt-transition-normal);
}

.management-nav-arrow {
  color: var(--mgmt-text-muted);
  font-size: 0.8rem;
  transition: all var(--mgmt-transition-bounce);
  opacity: 0;
}

.management-nav-active-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 0;
  background: var(--mgmt-primary-gradient);
  border-radius: 0 2px 2px 0;
  transition: all var(--mgmt-transition-bounce);
}

/* Management Navigation Hover and Active States */
.management-nav-item:hover {
  transform: translateX(4px);
  background: var(--mgmt-dark-surface-hover);
}

.management-nav-item:hover .management-nav-icon-container {
  background: var(--mgmt-dark-surface-active);
  border-color: var(--mgmt-dark-border-hover);
  transform: scale(1.1);
}

.management-nav-item:hover .management-nav-icon {
  color: var(--mgmt-primary);
  transform: scale(1.1);
}

.management-nav-item:hover .management-nav-title {
  color: var(--mgmt-text-primary);
}

.management-nav-item:hover .management-nav-subtitle {
  color: var(--mgmt-text-secondary);
}

.management-nav-item:hover .management-nav-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.management-nav-item.active {
  background: var(--mgmt-dark-surface-active);
  transform: translateX(4px);
}

.management-nav-item.active .management-nav-icon-container {
  background: var(--mgmt-primary-gradient);
  border-color: var(--mgmt-primary);
  box-shadow: var(--mgmt-shadow-button);
}

.management-nav-item.active .management-nav-icon-bg {
  opacity: 1;
}

.management-nav-item.active .management-nav-icon {
  color: white;
}

.management-nav-item.active .management-nav-title {
  color: var(--mgmt-text-primary);
  font-weight: 700;
}

.management-nav-item.active .management-nav-subtitle {
  color: var(--mgmt-primary);
}

.management-nav-item.active .management-nav-arrow {
  opacity: 1;
  color: var(--mgmt-primary);
}

.management-nav-item.active .management-nav-active-indicator {
  height: 60%;
}

/* Management Services Grid Styles */
.management-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
  padding: 20px 0;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--secondary-color);
}

.service-card:hover::before {
  opacity: 1;
}

.service-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.service-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.service-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.service-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.service-arrow {
  color: var(--text-muted);
  font-size: 18px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.service-card:hover .service-arrow {
  color: var(--secondary-color);
  transform: translateX(5px);
}

.service-card:hover .service-content h3 {
  color: var(--secondary-color);
}

.service-card:hover .service-content p {
  color: #333;
}

/* Specific service card colors */
.service-card[data-service="camera-management"] .service-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.service-card[data-service="alert-management"] .service-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.service-card[data-service="email-management"] .service-icon {
  background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
}

.service-card[data-service="sms-management"] .service-icon {
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

.service-card[data-service="webhook-management"] .service-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.service-card[data-service="whatsapp-management"] .service-icon {
  background: linear-gradient(135deg, #25d366 0%, #1da851 100%);
}

/* Responsive design for management grid */
@media (max-width: 768px) {
  .management-services-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px 0;
  }
  
  .service-card {
    padding: 20px;
    gap: 15px;
  }
  
  .service-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .service-content h3 {
    font-size: 16px;
  }
  
  .service-content p {
    font-size: 13px;
  }
}

/* Management Modal Styles */
.management-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.management-modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.management-modal-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.management-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.management-modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.management-modal-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.management-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

/* Service-specific modal content styles */
.service-section {
  display: none;
}

.service-section.active {
  display: block;
}

.service-header {
  margin-bottom: 30px;
  text-align: center;
}

.service-header h3 {
  color: var(--primary-color);
  font-size: 28px;
  margin-bottom: 10px;
}

.service-header p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Form styles for management services */
.management-form {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
}

.management-form .form-group {
  margin-bottom: 20px;
}

.management-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--primary-color);
}

.management-form .form-control {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.management-form .form-control:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.management-form .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* List styles for management services */
.management-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.management-list-header {
  background: var(--primary-color);
  color: white;
  padding: 15px 20px;
  font-weight: 600;
}

.management-list-item {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.management-list-item:hover {
  background-color: #f8f9fa;
}

.management-list-item:last-child {
  border-bottom: none;
}

.item-info h4 {
  margin: 0 0 5px 0;
  color: var(--primary-color);
  font-size: 16px;
}

.item-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.item-actions {
  display: flex;
  gap: 10px;
}

.item-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
}

/* Status badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ccc;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #999;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Management Content Area */
.management-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--mgmt-dark-bg-primary);
  overflow: hidden;
}

/* Management Content Header */
.management-content-header {
  background: var(--mgmt-bg-gradient-header);
  border-bottom: 1px solid var(--mgmt-dark-border);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 10;
}

.management-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  gap: 2rem;
}

.management-page-title-section {
  flex: 1;
}

.management-page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.5px;
}

.management-page-title i {
  color: var(--mgmt-primary);
  font-size: 1.5rem;
}

.management-page-subtitle {
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
}

.management-header-stats {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.management-stat-card {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-lg);
  padding: 1rem 1.25rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all var(--mgmt-transition-normal);
  min-width: 80px;
}

.management-stat-card:hover {
  background: var(--mgmt-dark-surface-hover);
  border-color: var(--mgmt-dark-border-hover);
  transform: translateY(-2px);
}

.management-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--mgmt-primary);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.management-stat-label {
  font-size: 0.75rem;
  color: var(--mgmt-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.management-search-container {
  position: relative;
}

.management-search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.management-search-box i {
  position: absolute;
  left: 1rem;
  color: var(--mgmt-text-muted);
  font-size: 0.875rem;
  z-index: 2;
}

.management-search-input {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-full);
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  width: 250px;
  transition: all var(--mgmt-transition-normal);
  backdrop-filter: blur(10px);
}

.management-search-input:focus {
  outline: none;
  border-color: var(--mgmt-primary);
  background: var(--mgmt-dark-surface-hover);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.management-search-input::placeholder {
  color: var(--mgmt-text-muted);
}

/* Management Content Body */
.management-content-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  scrollbar-width: thin;
  scrollbar-color: var(--mgmt-dark-border) transparent;
}

.management-content-body::-webkit-scrollbar {
  width: 6px;
}

.management-content-body::-webkit-scrollbar-track {
  background: transparent;
}

.management-content-body::-webkit-scrollbar-thumb {
  background: var(--mgmt-dark-border);
  border-radius: 3px;
}

.management-content-body::-webkit-scrollbar-thumb:hover {
  background: var(--mgmt-dark-border-hover);
}

/* Management Module Sections */
.management-module-section {
  display: none;
}

.management-module-section.active {
  display: block;
}

/* Management Admin Grid */
.management-admin-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 1200px) {
  .management-admin-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Management Premium Cards */
.management-premium-card {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
  transition: all var(--mgmt-transition-normal);
  position: relative;
}

.management-premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    transparent 50%,
    rgba(16, 185, 129, 0.05) 100%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--mgmt-transition-normal);
}

.management-premium-card:hover::before {
  opacity: 1;
}

.management-premium-card:hover {
  border-color: var(--mgmt-dark-border-hover);
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-card);
}

.management-card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--mgmt-dark-border);
  background: rgba(30, 41, 59, 0.3);
}

.management-card-title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.management-card-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.management-card-title i {
  color: var(--mgmt-primary);
  font-size: 1.25rem;
}

.management-card-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0;
}

.management-card-badge {
  display: flex;
  gap: 0.5rem;
}

.management-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--mgmt-radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.management-badge-info {
  background: var(--mgmt-info-gradient);
  color: white;
}

.management-badge-success {
  background: var(--mgmt-success-gradient);
  color: white;
}

.management-badge-warning {
  background: var(--mgmt-warning-gradient);
  color: white;
}

.management-badge-danger {
  background: var(--mgmt-danger-gradient);
  color: white;
}

.management-card-description {
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.management-card-body {
  padding: 2rem;
}

/* Management Forms */
.management-form-premium {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.management-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.management-form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
}

.management-form-label i {
  color: var(--mgmt-primary);
  font-size: 0.875rem;
}

.management-required {
  color: var(--mgmt-danger);
  font-weight: 700;
}

.management-form-input,
.management-form-textarea {
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  transition: all var(--mgmt-transition-normal);
  font-family: inherit;
}

.management-form-input:focus,
.management-form-textarea:focus {
  outline: none;
  border-color: var(--mgmt-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--mgmt-dark-bg-tertiary);
}

.management-form-input::placeholder,
.management-form-textarea::placeholder {
  color: var(--mgmt-text-muted);
}

.management-form-textarea {
  resize: vertical;
  min-height: 100px;
}

.management-code-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8125rem;
}

.management-form-hint {
  font-size: 0.75rem;
  color: var(--mgmt-text-muted);
  margin-top: 0.25rem;
}

/* Management URL Input Container */
.management-url-input-container {
  display: flex;
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  overflow: hidden;
  transition: all var(--mgmt-transition-normal);
}

.management-url-input-container:focus-within {
  border-color: var(--mgmt-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.management-method-dropdown {
  background: var(--mgmt-dark-bg-tertiary);
  border: none;
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  font-weight: 600;
  border-right: 1px solid var(--mgmt-dark-border);
  cursor: pointer;
}

.management-url-input {
  flex: 1;
  border: none;
  background: var(--mgmt-dark-bg-secondary);
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
}

.management-url-input:focus {
  outline: none;
  background: var(--mgmt-dark-bg-tertiary);
}

/* Management Buttons */
.management-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--mgmt-radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.management-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.management-btn-primary {
  background: var(--mgmt-primary-gradient);
  color: white;
  box-shadow: var(--mgmt-shadow-button);
}

.management-btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-btn-secondary {
  background: var(--mgmt-dark-surface);
  color: var(--mgmt-text-secondary);
  border: 1px solid var(--mgmt-dark-border);
}

.management-btn-secondary:hover:not(:disabled) {
  background: var(--mgmt-dark-surface-hover);
  color: var(--mgmt-text-primary);
  border-color: var(--mgmt-dark-border-hover);
}

.management-btn-whatsapp {
  background: linear-gradient(135deg, var(--mgmt-whatsapp) 0%, #1da851 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

.management-btn-whatsapp:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
}

.management-btn-sms {
  background: linear-gradient(135deg, var(--mgmt-sms) 0%, #e55a2b 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.management-btn-sms:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
}

.management-btn-email {
  background: linear-gradient(135deg, var(--mgmt-email) 0%, #d33b2c 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(234, 67, 53, 0.3);
}

.management-btn-email:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(234, 67, 53, 0.4);
}

.management-btn-danger {
  background: var(--mgmt-danger-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.management-btn-warning {
  background: var(--mgmt-warning-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.management-btn-warning:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.management-btn-success {
  background: var(--mgmt-success-gradient);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.management-btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.management-btn-full {
  width: 100%;
}

.management-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
}

.management-btn-loader {
  display: none;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: mgmt-spin 1s linear infinite;
}

.management-btn.loading .management-btn-loader {
  display: block;
}

.management-btn.loading span {
  display: none;
}

@keyframes mgmt-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Management Item Lists */
.management-item-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.management-list-item {
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 1.5rem;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.management-list-item:hover {
  background: var(--mgmt-dark-bg-tertiary);
  border-color: var(--mgmt-dark-border-hover);
  transform: translateY(-1px);
}

.management-item-info {
  flex: 1;
}

.management-item-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
}

.management-item-info p {
  font-size: 0.875rem;
  color: var(--mgmt-text-secondary);
  margin: 0 0 0.5rem 0;
}

.management-item-actions {
  display: flex;
  gap: 0.5rem;
}

/* Management Camera Cards */
.management-camera-card {
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  margin-bottom: 1rem;
  transition: all var(--mgmt-transition-normal);
  overflow: hidden;
}

.management-camera-card:hover {
  background: var(--mgmt-dark-bg-tertiary);
  border-color: var(--mgmt-dark-border-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.management-camera-card-header {
  padding: 1.25rem 1.5rem 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--mgmt-dark-border);
}

.management-camera-info {
  flex: 1;
}

.management-camera-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.management-camera-name::before {
  content: "📹";
  font-size: 1rem;
}

.management-camera-url {
  font-size: 0.875rem;
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-family: 'Courier New', monospace;
  background: var(--mgmt-dark-bg-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--mgmt-dark-border);
  word-break: break-all;
}

.management-camera-status {
  margin-left: 1rem;
}

.management-status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.management-status-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.management-status-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-camera-card-body {
  padding: 1rem 1.5rem 1.25rem 1.5rem;
}

.management-camera-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.management-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border: none;
  border-radius: var(--mgmt-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  text-decoration: none;
  min-width: 90px;
  justify-content: center;
}

.management-action-btn:hover {
  transform: translateY(-1px);
}

.management-action-btn i {
  font-size: 0.875rem;
}

.management-action-edit {
  background: linear-gradient(135deg, var(--mgmt-primary) 0%, #1e40af 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.management-action-edit:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.management-action-activate {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.management-action-activate:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.management-action-deactivate {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.management-action-deactivate:hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.management-action-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-action-delete:hover {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Responsive design for camera actions */
@media (max-width: 768px) {
  .management-camera-actions {
    flex-direction: column;
  }

  .management-action-btn {
    min-width: 100%;
  }

  .management-camera-card-header {
    flex-direction: column;
    gap: 1rem;
  }

  .management-camera-status {
    margin-left: 0;
    align-self: flex-start;
  }
}

/* Management Empty State */
.management-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--mgmt-text-muted);
}

.management-empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--mgmt-text-accent);
  opacity: 0.5;
}

.management-empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-secondary);
  margin-bottom: 0.5rem;
}

.management-empty-state p {
  font-size: 0.875rem;
  margin: 0;
}

/* Management Camera Dashboard */
.management-camera-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 1200px) {
  .management-camera-dashboard {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.management-camera-add-panel,
.management-camera-inventory-panel {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.management-camera-add-header,
.management-camera-inventory-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--mgmt-dark-border);
  background: rgba(30, 41, 59, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.management-camera-add-title,
.management-camera-inventory-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.management-camera-add-title i,
.management-camera-inventory-title i {
  color: var(--mgmt-primary);
}

.management-camera-add-subtitle,
.management-camera-inventory-subtitle {
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.management-camera-new-btn,
.management-camera-refresh-btn {
  background: var(--mgmt-primary-gradient);
  color: white;
  border: none;
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.management-camera-new-btn:hover,
.management-camera-refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-camera-add-body,
.management-camera-inventory-body {
  padding: 2rem;
}

.management-camera-form-group {
  margin-bottom: 1.5rem;
}

.management-camera-form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.management-camera-form-label i {
  color: var(--mgmt-primary);
}

.management-camera-form-input {
  width: 100%;
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  transition: all var(--mgmt-transition-normal);
}

.management-camera-form-input:focus {
  outline: none;
  border-color: var(--mgmt-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--mgmt-dark-bg-tertiary);
}

.management-camera-form-help {
  font-size: 0.75rem;
  color: var(--mgmt-text-muted);
  margin-top: 0.25rem;
}

.management-camera-add-btn {
  background: var(--mgmt-primary-gradient);
  color: white;
  border: none;
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
}

.management-camera-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-camera-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Management Alert Dashboard */
.management-alert-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 1200px) {
  .management-alert-dashboard {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.management-alert-control-panel,
.management-schedule-panel {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.management-alert-control-header,
.management-schedule-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--mgmt-dark-border);
  background: rgba(30, 41, 59, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.management-alert-control-title,
.management-schedule-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.management-alert-control-title i,
.management-schedule-title i {
  color: var(--mgmt-primary);
}

.management-alert-control-subtitle {
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.management-alert-control-body,
.management-schedule-body {
  padding: 2rem;
}

.management-alert-status-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--mgmt-dark-bg-secondary);
  border-radius: var(--mgmt-radius-md);
  border: 1px solid var(--mgmt-dark-border);
}

.management-alert-status-text {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.management-alert-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--mgmt-danger);
  animation: mgmt-pulse 2s infinite;
}

.management-alert-status-badge {
  padding: 0.5rem 1rem;
  border-radius: var(--mgmt-radius-full);
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--mgmt-danger-gradient);
  color: white;
}

@keyframes mgmt-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.management-alert-controls {
  display: flex;
  gap: 1rem;
}

.management-alert-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--mgmt-radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.management-alert-btn-start {
  background: var(--mgmt-success-gradient);
  color: white;
}

.management-alert-btn-stop {
  background: var(--mgmt-danger-gradient);
  color: white;
}

.management-alert-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-schedule-days {
  margin-bottom: 1.5rem;
}

.management-schedule-days-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.management-schedule-days-label i {
  color: var(--mgmt-primary);
}

.management-days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.management-day-btn {
  padding: 0.75rem 0.5rem;
  border: 1px solid var(--mgmt-dark-border);
  background: var(--mgmt-dark-bg-secondary);
  color: var(--mgmt-text-secondary);
  border-radius: var(--mgmt-radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
}

.management-day-btn:hover {
  background: var(--mgmt-dark-bg-tertiary);
  color: var(--mgmt-text-primary);
}

.management-day-btn.active {
  background: var(--mgmt-primary-gradient);
  color: white;
  border-color: var(--mgmt-primary);
}

.management-schedule-time {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.management-time-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.management-time-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
}

.management-time-label i {
  color: var(--mgmt-primary);
}

.management-time-input {
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  transition: all var(--mgmt-transition-normal);
}

.management-time-input:focus {
  outline: none;
  border-color: var(--mgmt-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--mgmt-dark-bg-tertiary);
}

.management-schedule-frequency {
  margin-bottom: 2rem;
}

.management-form-actions {
  display: flex;
  gap: 1rem;
}

.management-schedule-settings-btn {
  background: var(--mgmt-dark-surface);
  color: var(--mgmt-text-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.management-schedule-settings-btn:hover {
  background: var(--mgmt-dark-surface-hover);
  color: var(--mgmt-text-primary);
  border-color: var(--mgmt-dark-border-hover);
}
