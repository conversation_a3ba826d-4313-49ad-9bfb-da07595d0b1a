/* Management Camera Cards */
.management-camera-card {
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  margin-bottom: 1rem;
  transition: all var(--mgmt-transition-normal);
  overflow: hidden;
}

.management-camera-card:hover {
  background: var(--mgmt-dark-bg-tertiary);
  border-color: var(--mgmt-dark-border-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.management-camera-card-header {
  padding: 1.25rem 1.5rem 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--mgmt-dark-border);
}

.management-camera-info {
  flex: 1;
}

.management-camera-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.management-camera-name::before {
  content: "📹";
  font-size: 1rem;
}

.management-camera-url {
  font-size: 0.875rem;
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-family: 'Courier New', monospace;
  background: var(--mgmt-dark-bg-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--mgmt-dark-border);
  word-break: break-all;
}

.management-camera-status {
  margin-left: 1rem;
}

.management-status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.management-status-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.management-status-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-camera-card-body {
  padding: 1rem 1.5rem 1.25rem 1.5rem;
}

.management-camera-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.management-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border: none;
  border-radius: var(--mgmt-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  text-decoration: none;
  min-width: 90px;
  justify-content: center;
}

.management-action-btn:hover {
  transform: translateY(-1px);
}

.management-action-btn i {
  font-size: 0.875rem;
}

.management-action-edit {
  background: linear-gradient(135deg, var(--mgmt-primary) 0%, #1e40af 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.management-action-edit:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.management-action-activate {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.management-action-activate:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.management-action-deactivate {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.management-action-deactivate:hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.management-action-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.management-action-delete:hover {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Responsive design for camera actions */
@media (max-width: 768px) {
  .management-camera-actions {
    flex-direction: column;
  }

  .management-action-btn {
    min-width: 100%;
  }

  .management-camera-card-header {
    flex-direction: column;
    gap: 1rem;
  }

  .management-camera-status {
    margin-left: 0;
    align-self: flex-start;
  }
}

/* Management Empty State */
.management-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--mgmt-text-muted);
}

.management-empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--mgmt-text-accent);
  opacity: 0.5;
}

.management-empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-secondary);
  margin-bottom: 0.5rem;
}

.management-empty-state p {
  font-size: 0.875rem;
  margin: 0;
}

/* Management Camera Dashboard */
.management-camera-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

@media (max-width: 1200px) {
  .management-camera-dashboard {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.management-camera-add-panel,
.management-camera-inventory-panel {
  background: var(--mgmt-dark-surface);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.management-camera-add-header,
.management-camera-inventory-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--mgmt-dark-border);
  background: rgba(30, 41, 59, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.management-camera-add-title,
.management-camera-inventory-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.management-camera-add-title i,
.management-camera-inventory-title i {
  color: var(--mgmt-primary);
}

.management-camera-add-subtitle,
.management-camera-inventory-subtitle {
  color: var(--mgmt-text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.management-camera-new-btn,
.management-camera-refresh-btn {
  background: var(--mgmt-primary-gradient);
  color: white;
  border: none;
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.management-camera-new-btn:hover,
.management-camera-refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-camera-add-body,
.management-camera-inventory-body {
  padding: 2rem;
}

.management-camera-form-group {
  margin-bottom: 1.5rem;
}

.management-camera-form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.management-camera-form-label i {
  color: var(--mgmt-primary);
}

.management-camera-form-input {
  width: 100%;
  background: var(--mgmt-dark-bg-secondary);
  border: 1px solid var(--mgmt-dark-border);
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1rem;
  color: var(--mgmt-text-primary);
  font-size: 0.875rem;
  transition: all var(--mgmt-transition-normal);
}

.management-camera-form-input:focus {
  outline: none;
  border-color: var(--mgmt-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--mgmt-dark-bg-tertiary);
}

.management-camera-form-help {
  font-size: 0.75rem;
  color: var(--mgmt-text-muted);
  margin-top: 0.25rem;
}

.management-camera-add-btn {
  background: var(--mgmt-primary-gradient);
  color: white;
  border: none;
  border-radius: var(--mgmt-radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--mgmt-transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
}

.management-camera-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--mgmt-shadow-button-hover);
}

.management-camera-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
