# Environment Variables Template for Face Recognition System
# Copy this file to .env and fill in your actual values

# Database Configuration
DB_PASSWORD=1234
DB_USER=root
DB_HOST=localhost
DB_NAME=face_recognition_db
DB_PORT=3306

# Security
SECRET_KEY=generate-a-secure-secret-key

# Application Environment
ENVIRONMENT=development

# Optional: Override YAML settings via environment variables
# APP_HOST=0.0.0.0
# APP_PORT=8000
# APP_DEBUG=false

# Qdrant Configuration (if different from config.yaml)
# QDRANT_URL=http://localhost:6333

# HuggingFace Token (if needed for private models)
# HUGGINGFACE_TOKEN=your-token-here

# Production Database (for production deployment)
# DB_HOST=mysql
# DB_PASSWORD=your-production-password

# Production Qdrant (for production deployment)
# QDRANT_URL=http://qdrant:6333
