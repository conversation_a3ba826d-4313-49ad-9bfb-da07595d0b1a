#!/usr/bin/env python3
"""
Migration script to add is_active column to cameras table
"""

import sqlite3
import os
import sys

def add_camera_is_active_column():
    """Add is_active column to cameras table if it doesn't exist"""
    
    # Get the database path - try multiple locations
    possible_paths = [
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'face_recognition.db'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'face_recognition.db'),
        'face_recognition.db',
        './face_recognition.db'
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("Database file not found in any of the expected locations:")
        for path in possible_paths:
            print(f"  - {path}")
        print("The database will be created when the application runs.")
        return True  # Not an error if DB doesn't exist yet

    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if is_active column already exists
        cursor.execute("PRAGMA table_info(cameras)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'is_active' in columns:
            print("Column 'is_active' already exists in cameras table")
            return True
        
        # Add the is_active column with default value True
        cursor.execute("ALTER TABLE cameras ADD COLUMN is_active BOOLEAN DEFAULT 1 NOT NULL")
        
        # Update all existing cameras to be active
        cursor.execute("UPDATE cameras SET is_active = 1 WHERE is_active IS NULL")
        
        conn.commit()
        print("Successfully added 'is_active' column to cameras table")
        print("All existing cameras have been set to active")
        
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Running camera is_active column migration...")
    success = add_camera_is_active_column()
    
    if success:
        print("Migration completed successfully!")
        sys.exit(0)
    else:
        print("Migration failed!")
        sys.exit(1)
