<!-- Webhook Management Section -->
<div class="management-module-section" id="mgmtWebhookManagementSection">
  <div class="management-admin-grid">
    <!-- Add Webhook Form -->
    <div class="management-premium-card">
      <div class="management-card-header">
        <div class="management-card-title-section">
          <div class="management-card-title">
            <i class="fas fa-link"></i>
            <h3>Add New Webhook</h3>
          </div>
          <div class="management-card-badge">
            <span class="management-badge management-badge-info">
              <i class="fas fa-code"></i>
              API
            </span>
          </div>
        </div>
        <p class="management-card-description">Configure webhook endpoints for alert notifications</p>
      </div>
      <div class="management-card-body">
        <form id="mgmtWebhookForm" class="management-form-premium">
          <div class="management-form-group">
            <label for="mgmtWebhookUrl" class="management-form-label">
              <i class="fas fa-link"></i>
              Webhook URL
              <span class="management-required">*</span>
            </label>
            <div class="management-url-input-container">
              <select id="mgmtWebhookMethod" class="management-method-dropdown" required>
                <option value="POST" selected>POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
                <option value="GET">GET</option>
                <option value="DELETE">DELETE</option>
              </select>
              <input type="url" id="mgmtWebhookUrl" class="management-form-input management-url-input" placeholder="https://example.com/webhook" required>
            </div>
            <div class="management-form-hint">Complete webhook endpoint URL</div>
          </div>

          <div class="management-form-group">
            <label for="mgmtWebhookDescription" class="management-form-label">
              <i class="fas fa-info-circle"></i>
              Description
            </label>
            <textarea id="mgmtWebhookDescription" class="management-form-textarea" placeholder="Enter a description for this webhook"></textarea>
          </div>

          <div class="management-form-group">
            <label for="mgmtWebhookHeaders" class="management-form-label">
              <i class="fas fa-code"></i>
              Headers (JSON)
            </label>
            <textarea id="mgmtWebhookHeaders" class="management-form-textarea management-code-input" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'></textarea>
          </div>

          <div class="management-form-group">
            <label for="mgmtWebhookTemplate" class="management-form-label">
              <i class="fas fa-file-code"></i>
              Body Template (JSON)
            </label>
            <textarea id="mgmtWebhookTemplate" class="management-form-textarea management-code-input" placeholder='{"alert": "{{alert_type}}", "camera": "{{camera_name}}", "count": {{count}}}'></textarea>
          </div>

          <button type="submit" class="management-btn management-btn-primary management-btn-full">
            <i class="fas fa-link"></i>
            <span>Add Webhook</span>
            <div class="management-btn-loader"></div>
          </button>
        </form>
      </div>
    </div>

    <!-- Webhook List -->
    <div class="management-premium-card">
      <div class="management-card-header">
        <div class="management-card-title-section">
          <div class="management-card-title">
            <i class="fas fa-list"></i>
            <h3>Configured Webhooks</h3>
          </div>
          <button class="management-btn management-btn-secondary management-btn-sm" id="mgmtRefreshWebhooksBtn">
            <i class="fas fa-sync-alt"></i>
            <span>Refresh</span>
          </button>
        </div>
        <p class="management-card-description">Manage existing webhook endpoints</p>
      </div>
      <div class="management-card-body">
        <div id="mgmtWebhookList" class="management-item-list">
          <!-- Webhook list will be populated here -->
          <div class="management-empty-state">
            <i class="fas fa-link"></i>
            <h3>No Webhooks Configured</h3>
            <p>Add your first webhook endpoint to receive alerts</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
