<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel - Face Recognition System</title>
  <link rel="stylesheet" href="/static/admin_panel.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body data-role="{{ user_role }}">
  <script>
    // Check if user has admin role (this is a client-side check for UI only)
    // The server-side check is more important and already implemented
    window.addEventListener('DOMContentLoaded', function() {
      // If we were redirected here without admin privileges, go back to face recognition page
      if (document.body.getAttribute('data-role') !== 'admin') {
        window.location.href = '/face_recognition';
      }
    });
  </script>
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <h1 class="header-title">
        <i class="fas fa-shield-alt"></i>
        Face Recognition Admin Panel
      </h1>
      
    </div>
    <a href="/face_recognition" class="back-btn">
      <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
  </div>

  <div class="container admin-container">
    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <button class="tab-btn active" data-tab="unknown-persons">
        <i class="fas fa-user-question" style="animation: none;"></i> Unknown Persons
      </button>
      <button class="tab-btn" data-tab="user-registration">
        <i class="fas fa-user-plus" style="animation: none;"></i> User Registration
      </button>
      <button class="tab-btn" data-tab="user-management">
        <i class="fas fa-users-cog" style="animation: none;"></i> User Management
      </button>
      <!-- <button class="tab-btn" data-tab="camera-management">
        <i class="fas fa-video" style="animation: none;"></i> Camera Management
      </button> -->
      <button class="tab-btn" data-tab="management">
        <i class="fas fa-tools" style="animation: none;"></i> Management
      </button>
      <button class="tab-btn" data-tab="settings">
        <i class="fas fa-cog" style="animation: none;"></i> Settings
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Unknown Persons Tab -->
      <div class="tab-pane active" id="unknown-persons-tab">
        <div id="unknownContainer" class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      </div>

      <!-- User Registration Tab -->
      <div class="tab-pane" id="user-registration-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Register New User</h2>
            <p>Add a new user to the face recognition system</p>
          </div>

          <form id="registerForm" enctype="multipart/form-data">
            <div class="form-group">
              <label for="username"><i class="fas fa-user" style="animation: none;"></i> Username</label>
              <input type="text" id="username" name="username" class="form-control" placeholder="Enter username" required>
            </div>

            <div class="form-group">
              <label for="email"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
              <input type="email" id="email" name="email" class="form-control" placeholder="Enter email address" required>
            </div>

            <div class="form-group">
              <label for="profile_image"><i class="fas fa-camera" style="animation: none;"></i> Profile Image</label>
              <div class="file-input-group">
                <button type="button" id="choose-file-btn" class="btn btn-primary">
                  <i class="fas fa-image" style="animation: none;"></i> Select Profile Image
                </button>
                <input type="file" id="upload-input" name="image_file" accept="image/*" style="display: none;" required>
                <input type="hidden" id="captured_image" name="captured_image">
                <div id="file-status" class="file-status">No file chosen</div>
              </div>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-user-plus" style="animation: none;"></i> Register User
            </button>
          </form>
        </div>
      </div>

      <!-- User Management Tab -->
      <div class="tab-pane" id="user-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">User Management</h2>
            <p>View and manage registered users</p>
          </div>

          <div class="user-management-controls">
            <div class="search-box">
              <input type="text" id="userSearchInput" class="form-control" placeholder="Search users...">
              <button class="btn btn-primary" id="searchUsersBtn">
                <i class="fas fa-search" style="animation: none;"></i> Search
              </button>
            </div>

            <button class="btn btn-primary" id="refreshUsersBtn">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
            </button>
          </div>

          <div id="usersList" class="users-list">
            <div class="loading">
              <i class="fas fa-spinner fa-spin"></i>
              <p>Loading users...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Camera Management Tab -->
      <div class="tab-pane" id="camera-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Camera Management</h2>
            <p>Add and manage cameras for face recognition</p>
          </div>

          <form id="cameraForm">
            <div class="form-group">
              <label for="cameraName"><i class="fas fa-tag" style="animation: none;"></i> Camera Name</label>
              <input type="text" id="cameraName" name="cameraName" class="form-control" placeholder="Enter a unique camera name" required>
            </div>

            <div class="form-group">
              <label for="rtspUrl"><i class="fas fa-link" style="animation: none;"></i> RTSP URL</label>
              <input type="text" id="rtspUrl" name="rtspUrl" class="form-control" placeholder="rtsp://username:password@ip:port/path" required>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-plus-circle" style="animation: none;"></i> Add Camera
            </button>
          </form>

          <div class="camera-list-section">
            <div class="section-header">
              <h3>Configured Cameras</h3>
              <button class="btn btn-primary" id="refreshCamerasBtn">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
              </button>
            </div>

            <div id="cameraList" class="camera-list">
              <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading cameras...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Management Tab -->
      <div class="tab-pane" id="management-tab">
        <!-- Back Button -->
        <!-- <button class="management-back-button" onclick="exitManagementMode()">
          <i class="fas fa-arrow-left"></i>
          <span>Back to Admin Panel</span>
        </button> -->

        <div class="management-container">
          <!-- Management Sidebar -->
          <div class="management-sidebar">
            <div class="management-sidebar-logo">
              <div class="management-logo-container">
                <div class="management-logo-icon-wrapper">
                  <div class="management-logo-glow"></div>
                  <div class="management-logo-icon">
                    <i class="fas fa-tools"></i>
                  </div>
                </div>
                <div class="management-logo-content">
                  <h1 class="management-logo-title">Management</h1>
                  <p class="management-logo-subtitle">Control Center</p>
                </div>
              </div>
            </div>

            <div class="management-nav-container">
              <div class="management-nav-header">
                <h3 class="management-nav-section-title">Services</h3>
                <div class="management-nav-divider"></div>
              </div>

              <div class="management-nav-menu">
                <button class="management-nav-item active" id="mgmtCameraManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fas fa-video"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">Camera Management</h4>
                      <p class="management-nav-subtitle">Surveillance</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>

                <button class="management-nav-item" id="mgmtWebhookManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fas fa-link"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">Webhook Management</h4>
                      <p class="management-nav-subtitle">API</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>

                <button class="management-nav-item" id="mgmtWhatsappManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fab fa-whatsapp"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">WhatsApp Management</h4>
                      <p class="management-nav-subtitle">Messaging</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>

                <button class="management-nav-item" id="mgmtSmsManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fas fa-sms"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">SMS Management</h4>
                      <p class="management-nav-subtitle">Text</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>

                <button class="management-nav-item" id="mgmtEmailManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fas fa-envelope"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">Email Management</h4>
                      <p class="management-nav-subtitle">Email</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>

                <button class="management-nav-item" id="mgmtAlertManagementNav">
                  <div class="management-nav-item-content">
                    <div class="management-nav-icon-container">
                      <div class="management-nav-icon-bg"></div>
                      <i class="management-nav-icon fas fa-bell"></i>
                    </div>
                    <div class="management-nav-text-container">
                      <h4 class="management-nav-title">Alert Management</h4>
                      <p class="management-nav-subtitle">System</p>
                    </div>
                    <i class="management-nav-arrow fas fa-chevron-right"></i>
                  </div>
                  <div class="management-nav-active-indicator"></div>
                </button>
              </div>
            </div>
          </div>
          

          <button class="management-back-button" onclick="exitManagementMode()">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Admin Panel</span>
          </button>
          
          <!-- Management Content Area -->
          <div class="management-content-area">
            <!-- Management Header -->
            <div class="management-content-header">
              <div class="management-header-content">
                <div class="management-page-title-section">
                  <h1 class="management-page-title" id="mgmtPageTitle">
                    <i class="fas fa-video"></i>
                    <span>Camera Management</span>
                  </h1>
                  <p class="management-page-subtitle" id="mgmtPageSubtitle">Configure surveillance cameras</p>
                </div>

                <div class="management-header-stats">
                  <div class="management-stat-card">
                    <div class="management-stat-value" id="mgmtTotalCameras">0</div>
                    <div class="management-stat-label">Total</div>
                  </div>
                  <div class="management-stat-card">
                    <div class="management-stat-value" id="mgmtActiveCameras">0</div>
                    <div class="management-stat-label">Active</div>
                  </div>
                  <div class="management-search-container">
                    <div class="management-search-box">
                      <i class="fas fa-search"></i>
                      <input type="text" class="management-search-input" placeholder="Search..." id="mgmtGlobalSearch">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Management Content Body -->
            <div class="management-content-body">

              <!-- Camera Management Section -->
              <div class="management-module-section active" id="mgmtCameraManagementSection">
                <div class="management-camera-dashboard">
                  <!-- Add Camera Panel -->
                  <div class="management-camera-add-panel">
                    <div class="management-camera-add-header">
                      <div>
                        <h3 class="management-camera-add-title">
                          <i class="fas fa-video"></i>
                          Add New Camera
                        </h3>
                        <p class="management-camera-add-subtitle">Configure surveillance camera for face recognition</p>
                      </div>
                      <button class="management-camera-new-btn">
                        <i class="fas fa-plus"></i>
                        New
                      </button>
                    </div>
                    <div class="management-camera-add-body">
                      <form id="mgmtCameraForm" class="management-form-premium">
                        <div class="management-camera-form-group">
                          <label for="mgmtCameraName" class="management-camera-form-label">
                            <i class="fas fa-tag"></i>
                            Camera Name
                            <span class="management-required">*</span>
                          </label>
                          <input type="text" id="mgmtCameraName" class="management-camera-form-input" placeholder="Enter camera name..." required>
                          <div class="management-camera-form-help">Choose a descriptive name for easy identification</div>
                        </div>

                        <div class="management-camera-form-group">
                          <label for="mgmtRtspUrl" class="management-camera-form-label">
                            <i class="fas fa-link"></i>
                            RTSP URL
                            <span class="management-required">*</span>
                          </label>
                          <input type="text" id="mgmtRtspUrl" class="management-camera-form-input" placeholder="rtsp://username:password@ip:port/path" required>
                          <div class="management-camera-form-help">Complete RTSP stream URL with credentials</div>
                        </div>

                        <button type="submit" class="management-camera-add-btn">
                          <i class="fas fa-plus"></i>
                          Add Camera
                        </button>
                      </form>
                    </div>
                  </div>

                  <!-- Camera Inventory Panel -->
                  <div class="management-camera-inventory-panel">
                    <div class="management-camera-inventory-header">
                      <div>
                        <h3 class="management-camera-inventory-title">
                          <i class="fas fa-list"></i>
                          Camera Inventory
                        </h3>
                        <p class="management-camera-inventory-subtitle">Manage existing cameras</p>
                      </div>
                      <button class="management-camera-refresh-btn" id="mgmtRefreshCamerasBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                      </button>
                    </div>
                    <div class="management-camera-inventory-body">
                      <div id="mgmtCameraList" class="management-camera-list">
                        <!-- Camera list will be populated here -->
                        <div class="management-empty-state">
                          <i class="fas fa-video"></i>
                          <h3>No Cameras Found</h3>
                          <p>Add your first camera to start monitoring</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Webhook Management Section -->
              <div class="management-module-section" id="mgmtWebhookManagementSection">
                <div class="management-admin-grid">
                  <!-- Add Webhook Form -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-link"></i>
                          <h3>Add New Webhook</h3>
                        </div>
                        <div class="management-card-badge">
                          <span class="management-badge management-badge-info">
                            <i class="fas fa-code"></i>
                            API
                          </span>
                        </div>
                      </div>
                      <p class="management-card-description">Configure webhook endpoints for alert notifications</p>
                    </div>
                    <div class="management-card-body">
                      <form id="mgmtWebhookForm" class="management-form-premium">
                        <div class="management-form-group">
                          <label for="mgmtWebhookUrl" class="management-form-label">
                            <i class="fas fa-link"></i>
                            Webhook URL
                            <span class="management-required">*</span>
                          </label>
                          <div class="management-url-input-container">
                            <select id="mgmtWebhookMethod" class="management-method-dropdown" required>
                              <option value="POST" selected>POST</option>
                              <option value="PUT">PUT</option>
                              <option value="PATCH">PATCH</option>
                              <option value="GET">GET</option>
                              <option value="DELETE">DELETE</option>
                            </select>
                            <input type="url" id="mgmtWebhookUrl" class="management-form-input management-url-input" placeholder="https://example.com/webhook" required>
                          </div>
                          <div class="management-form-hint">Complete webhook endpoint URL</div>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtWebhookDescription" class="management-form-label">
                            <i class="fas fa-info-circle"></i>
                            Description
                          </label>
                          <textarea id="mgmtWebhookDescription" class="management-form-textarea" placeholder="Enter a description for this webhook"></textarea>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtWebhookHeaders" class="management-form-label">
                            <i class="fas fa-code"></i>
                            Headers (JSON)
                          </label>
                          <textarea id="mgmtWebhookHeaders" class="management-form-textarea management-code-input" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'></textarea>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtWebhookTemplate" class="management-form-label">
                            <i class="fas fa-file-code"></i>
                            Body Template (JSON)
                          </label>
                          <textarea id="mgmtWebhookTemplate" class="management-form-textarea management-code-input" placeholder='{"alert": "{{alert_type}}", "camera": "{{camera_name}}", "count": {{count}}}'></textarea>
                        </div>

                        <button type="submit" class="management-btn management-btn-primary management-btn-full">
                          <i class="fas fa-link"></i>
                          <span>Add Webhook</span>
                          <div class="management-btn-loader"></div>
                        </button>
                      </form>
                    </div>
                  </div>

                  <!-- Webhook List -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-list"></i>
                          <h3>Configured Webhooks</h3>
                        </div>
                        <button class="management-btn management-btn-secondary management-btn-sm" id="mgmtRefreshWebhooksBtn">
                          <i class="fas fa-sync-alt"></i>
                          <span>Refresh</span>
                        </button>
                      </div>
                      <p class="management-card-description">Manage existing webhook endpoints</p>
                    </div>
                    <div class="management-card-body">
                      <div id="mgmtWebhookList" class="management-item-list">
                        <!-- Webhook list will be populated here -->
                        <div class="management-empty-state">
                          <i class="fas fa-link"></i>
                          <h3>No Webhooks Configured</h3>
                          <p>Add your first webhook endpoint to receive alerts</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- WhatsApp Management Section -->
              <div class="management-module-section" id="mgmtWhatsappManagementSection">
                <div class="management-admin-grid">
                  <!-- Add WhatsApp Contact Form -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fab fa-whatsapp"></i>
                          <h3>Add WhatsApp Contact</h3>
                        </div>
                        <div class="management-card-badge">
                          <span class="management-badge management-badge-success">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp
                          </span>
                        </div>
                      </div>
                      <p class="management-card-description">Add contacts for WhatsApp alert notifications</p>
                    </div>
                    <div class="management-card-body">
                      <form id="mgmtWhatsappForm" class="management-form-premium">
                        <div class="management-form-group">
                          <label for="mgmtWhatsappPhone" class="management-form-label">
                            <i class="fas fa-phone"></i>
                            Phone Number
                            <span class="management-required">*</span>
                          </label>
                          <input type="tel" id="mgmtWhatsappPhone" class="management-form-input" placeholder="+1234567890" required>
                          <div class="management-form-hint">Include country code (e.g., +1 for US)</div>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtWhatsappName" class="management-form-label">
                            <i class="fas fa-user"></i>
                            Full Name
                            <span class="management-required">*</span>
                          </label>
                          <input type="text" id="mgmtWhatsappName" class="management-form-input" placeholder="Enter full name" required>
                          <div class="management-form-hint">Contact's full name for identification</div>
                        </div>

                        <button type="submit" class="management-btn management-btn-whatsapp management-btn-full">
                          <i class="fab fa-whatsapp"></i>
                          <span>Add WhatsApp Contact</span>
                          <div class="management-btn-loader"></div>
                        </button>
                      </form>
                    </div>
                  </div>

                  <!-- WhatsApp Contact List -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-address-book"></i>
                          <h3>WhatsApp Contacts</h3>
                        </div>
                        <button class="management-btn management-btn-secondary management-btn-sm" id="mgmtRefreshWhatsAppBtn">
                          <i class="fas fa-sync-alt"></i>
                          <span>Refresh</span>
                        </button>
                      </div>
                      <p class="management-card-description">Manage WhatsApp notification contacts</p>
                    </div>
                    <div class="management-card-body">
                      <div id="mgmtWhatsappList" class="management-item-list">
                        <!-- WhatsApp contact list will be populated here -->
                        <div class="management-empty-state">
                          <i class="fab fa-whatsapp"></i>
                          <h3>No WhatsApp Contacts</h3>
                          <p>Add contacts to receive WhatsApp notifications</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- SMS Management Section -->
              <div class="management-module-section" id="mgmtSmsManagementSection">
                <div class="management-admin-grid">
                  <!-- Add SMS Contact Form -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-sms"></i>
                          <h3>Add SMS Contact</h3>
                        </div>
                        <div class="management-card-badge">
                          <span class="management-badge management-badge-warning">
                            <i class="fas fa-sms"></i>
                            SMS
                          </span>
                        </div>
                      </div>
                      <p class="management-card-description">Add contacts for SMS text notifications</p>
                    </div>
                    <div class="management-card-body">
                      <form id="mgmtSmsForm" class="management-form-premium">
                        <div class="management-form-group">
                          <label for="mgmtSmsPhone" class="management-form-label">
                            <i class="fas fa-phone"></i>
                            Phone Number
                            <span class="management-required">*</span>
                          </label>
                          <input type="tel" id="mgmtSmsPhone" class="management-form-input" placeholder="+1234567890" required>
                          <div class="management-form-hint">Include country code (e.g., +1 for US)</div>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtSmsName" class="management-form-label">
                            <i class="fas fa-user"></i>
                            Full Name
                            <span class="management-required">*</span>
                          </label>
                          <input type="text" id="mgmtSmsName" class="management-form-input" placeholder="Enter full name" required>
                          <div class="management-form-hint">Contact's full name for identification</div>
                        </div>

                        <button type="submit" class="management-btn management-btn-sms management-btn-full">
                          <i class="fas fa-sms"></i>
                          <span>Add SMS Contact</span>
                          <div class="management-btn-loader"></div>
                        </button>
                      </form>
                    </div>
                  </div>

                  <!-- SMS Contact List -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-address-book"></i>
                          <h3>SMS Contacts</h3>
                        </div>
                        <button class="management-btn management-btn-secondary management-btn-sm" id="mgmtRefreshSMSBtn">
                          <i class="fas fa-sync-alt"></i>
                          <span>Refresh</span>
                        </button>
                      </div>
                      <p class="management-card-description">Manage SMS notification contacts</p>
                    </div>
                    <div class="management-card-body">
                      <div id="mgmtSmsList" class="management-item-list">
                        <!-- SMS contact list will be populated here -->
                        <div class="management-empty-state">
                          <i class="fas fa-sms"></i>
                          <h3>No SMS Contacts</h3>
                          <p>Add contacts to receive SMS notifications</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Email Management Section -->
              <div class="management-module-section" id="mgmtEmailManagementSection">
                <div class="management-admin-grid">
                  <!-- Add Email Contact Form -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-envelope"></i>
                          <h3>Add Email Contact</h3>
                        </div>
                        <div class="management-card-badge">
                          <span class="management-badge management-badge-danger">
                            <i class="fas fa-envelope"></i>
                            Email
                          </span>
                        </div>
                      </div>
                      <p class="management-card-description">Add contacts for email alert notifications</p>
                    </div>
                    <div class="management-card-body">
                      <form id="mgmtEmailForm" class="management-form-premium">
                        <div class="management-form-group">
                          <label for="mgmtEmailAddress" class="management-form-label">
                            <i class="fas fa-at"></i>
                            Email Address
                            <span class="management-required">*</span>
                          </label>
                          <input type="email" id="mgmtEmailAddress" class="management-form-input" placeholder="<EMAIL>" required>
                          <div class="management-form-hint">Valid email address for notifications</div>
                        </div>

                        <div class="management-form-group">
                          <label for="mgmtEmailName" class="management-form-label">
                            <i class="fas fa-user"></i>
                            Full Name
                            <span class="management-required">*</span>
                          </label>
                          <input type="text" id="mgmtEmailName" class="management-form-input" placeholder="Enter full name" required>
                          <div class="management-form-hint">Contact's full name for identification</div>
                        </div>

                        <button type="submit" class="management-btn management-btn-email management-btn-full">
                          <i class="fas fa-envelope"></i>
                          <span>Add Email Contact</span>
                          <div class="management-btn-loader"></div>
                        </button>
                      </form>
                    </div>
                  </div>

                  <!-- Email Contact List -->
                  <div class="management-premium-card">
                    <div class="management-card-header">
                      <div class="management-card-title-section">
                        <div class="management-card-title">
                          <i class="fas fa-address-book"></i>
                          <h3>Email Contacts</h3>
                        </div>
                        <button class="management-btn management-btn-secondary management-btn-sm" id="mgmtRefreshEmailBtn">
                          <i class="fas fa-sync-alt"></i>
                          <span>Refresh</span>
                        </button>
                      </div>
                      <p class="management-card-description">Manage email notification contacts</p>
                    </div>
                    <div class="management-card-body">
                      <div id="mgmtEmailList" class="management-item-list">
                        <!-- Email contact list will be populated here -->
                        <div class="management-empty-state">
                          <i class="fas fa-envelope"></i>
                          <h3>No Email Contacts</h3>
                          <p>Add contacts to receive email notifications</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Alert Management Section -->
              <div class="management-module-section" id="mgmtAlertManagementSection">
                <div class="management-alert-dashboard">
                  <!-- System Status Control Panel -->
                  <div class="management-alert-control-panel">
                    <div class="management-alert-control-header">
                      <div>
                        <h3 class="management-alert-control-title">
                          <i class="fas fa-power-off"></i>
                          System Status Control
                        </h3>
                        <p class="management-alert-control-subtitle">Monitor and control the alert system status</p>
                      </div>
                    </div>
                    <div class="management-alert-control-body">
                      <div class="management-alert-status-display">
                        <div class="management-alert-status-text">
                          <div class="management-alert-status-indicator"></div>
                          <span id="mgmtAlertStatusText">System Inactive</span>
                        </div>
                        <span class="management-alert-status-badge" id="mgmtSystemStatusBadge">INACTIVE</span>
                      </div>
                      <div class="management-alert-controls">
                        <button id="mgmtStartAlertBtn" class="management-alert-btn management-alert-btn-start">
                          <i class="fas fa-play"></i>
                          Start Alert System
                        </button>
                        <button id="mgmtStopAlertBtn" class="management-alert-btn management-alert-btn-stop">
                          <i class="fas fa-stop"></i>
                          Stop Alert System
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Schedule Configuration Panel -->
                  <div class="management-schedule-panel">
                    <div class="management-schedule-header">
                      <div>
                        <h3 class="management-schedule-title">
                          <i class="fas fa-calendar-alt"></i>
                          Schedule Configuration
                        </h3>
                        <p class="management-alert-control-subtitle">Configure alert system schedule and notification settings</p>
                      </div>
                      <button class="management-schedule-settings-btn">
                        <i class="fas fa-cog"></i>
                        Settings
                      </button>
                    </div>
                    <div class="management-schedule-body">
                      <form id="mgmtAlertForm" class="management-form-premium">
                        <div class="management-schedule-days">
                          <label class="management-schedule-days-label">
                            <i class="fas fa-calendar-week"></i>
                            Active Days
                          </label>
                          <div class="management-days-grid">
                            <button type="button" class="management-day-btn" data-day="Monday" onclick="mgmtToggleDayButton(this)">Mon</button>
                            <button type="button" class="management-day-btn" data-day="Tuesday" onclick="mgmtToggleDayButton(this)">Tue</button>
                            <button type="button" class="management-day-btn" data-day="Wednesday" onclick="mgmtToggleDayButton(this)">Wed</button>
                            <button type="button" class="management-day-btn" data-day="Thursday" onclick="mgmtToggleDayButton(this)">Thu</button>
                            <button type="button" class="management-day-btn" data-day="Friday" onclick="mgmtToggleDayButton(this)">Fri</button>
                            <button type="button" class="management-day-btn" data-day="Saturday" onclick="mgmtToggleDayButton(this)">Sat</button>
                            <button type="button" class="management-day-btn" data-day="Sunday" onclick="mgmtToggleDayButton(this)">Sun</button>
                          </div>
                        </div>

                        <div class="management-schedule-time">
                          <div class="management-time-group">
                            <label for="mgmtStartTime" class="management-time-label">
                              <i class="fas fa-sun"></i>
                              Start Time
                            </label>
                            <input type="time" id="mgmtStartTime" class="management-time-input" value="09:00" required>
                          </div>

                          <div class="management-time-group">
                            <label for="mgmtEndTime" class="management-time-label">
                              <i class="fas fa-moon"></i>
                              End Time
                            </label>
                            <input type="time" id="mgmtEndTime" class="management-time-input" value="17:00" required>
                          </div>
                        </div>

                        <div class="management-schedule-frequency">
                          <label for="mgmtAlertFrequency" class="management-time-label">
                            <i class="fas fa-clock"></i>
                            Alert Frequency (minutes)
                          </label>
                          <input type="number" id="mgmtAlertFrequency" class="management-time-input" min="1" max="1440" value="5" required>
                          <div class="management-form-hint">Minimum time between alerts for the same camera/region</div>
                        </div>

                        <div class="management-form-actions">
                          <button type="submit" class="management-btn management-btn-primary management-btn-full">
                            <i class="fas fa-save"></i>
                            <span>Save Alert Settings</span>
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-pane" id="settings-tab">
        <div class="card">
          <!-- Main Settings Tiles -->
          <div class="settings-tiles">
            <div class="settings-tile active" data-settings-section="application">
              <i class="fas fa-cogs" style="animation: none;"></i>
              <span>Application Settings</span>
            </div>
            <!-- <div class="settings-tile" data-settings-section="alert-management">
              <i class="fas fa-bell" style="animation: none;"></i>
              <span>Alert Management</span>
            </div> -->
          </div>

          <!-- Application Settings Section -->
          <div id="application-settings" class="settings-section active">
            <!-- Application Settings Sidebar -->
            <div class="settings-sidebar" style="display: flex; margin-bottom: 20px;">
              <div class="settings-sidebar-item active" data-subsection="accuracy-threshold">
                <i class="fas fa-sliders-h" style="animation: none;"></i>
                <span>Accuracy Threshold</span>
              </div>
              <!-- Add more application settings options here in the future -->
            </div>

            <!-- Accuracy Threshold Settings -->
            <div id="accuracy-threshold-subsection" class="settings-subsection active">
              <div class="section-header">
                <h3>Face Recognition Accuracy</h3>
                <p>Configure the threshold for face recognition matching</p>
              </div>

              <form id="settingsForm">
                <div class="form-group">
                  <label for="faceThreshold">
                    <i class="fas fa-sliders-h" style="animation: none;"></i> Face Recognition Threshold
                    <small class="text-muted">(Higher values require more similarity for a match)</small>
                  </label>
                  <div class="threshold-container" style="display: flex; align-items: center; gap: 15px;">
                    <input type="range" id="faceThreshold" name="faceThreshold" class="form-control" min="0.1" max="0.9" step="0.05" value="0.7">
                    <span id="thresholdValue" style="min-width: 40px; font-weight: bold;">0.7</span>
                  </div>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-save" style="animation: none;"></i> Save Settings
                </button>
              </form>

              <div id="settingsStatus" class="mt-3" style="display: none;">
                <div class="alert alert-success">
                  <i class="fas fa-check-circle" style="animation: none;"></i> Settings saved successfully!
                </div>
              </div>
            </div>
          </div>

          <!-- Alert Management Settings Section -->
          <div id="alert-management-settings" class="settings-section">
            <!-- Alert Management Sidebar -->
            <div class="settings-sidebar" style="display: flex; margin-bottom: 20px;">
              <div class="settings-sidebar-item active" data-subsection="webhook">
                <i class="fas fa-link" style="animation: none;"></i>
                <span>Webhook Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="whatsapp">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <span>WhatsApp Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="sms">
                <i class="fas fa-sms" style="animation: none;"></i>
                <span>SMS Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="email">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <span>Email Management</span>
              </div>
            </div>

            <!-- Webhook Management Subsection -->
            <div id="webhook-subsection" class="settings-subsection active">
              <!-- Header with Add Button -->
              <div class="webhook-header">
                <div class="section-info">
                  <h3><i class="fas fa-bell" style="animation: none;"></i> Webhook Management</h3>
                  <p>Configure webhooks for system alerts and notifications</p>
                </div>
                <button id="toggleWebhookFormBtn" class="btn btn-success">
                  <i class="fas fa-plus" style="animation: none;"></i> Add New Webhook
                </button>
              </div>

              <!-- Collapsible Form Section -->
              <div id="webhookFormSection" class="webhook-form-section" style="display: none;">
                <div class="form-header">
                  <h4 id="formTitle"><i class="fas fa-plus" style="animation: none;"></i> Add New Webhook</h4>
                  <button id="closeFormBtn" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times" style="animation: none;"></i> Close
                  </button>
                </div>

                <form id="webhookForm">
                  <!-- Postman-style URL input with method dropdown -->
                  <div class="form-group">
                    <label><i class="fas fa-link" style="animation: none;"></i> Webhook URL</label>
                    <div class="postman-url-container">
                      <select id="webhookMethod" name="webhookMethod" class="method-dropdown" required>
                        <option value="POST">POST</option>
                        <option value="GET">GET</option>
                        <option value="PUT">PUT</option>
                        <option value="PATCH">PATCH</option>
                        <option value="DELETE">DELETE</option>
                      </select>
                      <input type="url" id="webhookUrl" name="webhookUrl" class="url-input" placeholder="https://example.com/webhook" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="webhookDescription"><i class="fas fa-info-circle" style="animation: none;"></i> Description</label>
                    <input type="text" id="webhookDescription" name="webhookDescription" class="form-control" placeholder="Enter a description for this webhook">
                  </div>

                  <div class="form-group">
                    <label for="webhookHeaders"><i class="fas fa-list" style="animation: none;"></i> Headers (JSON)</label>
                    <textarea id="webhookHeaders" name="webhookHeaders" class="form-control" rows="3" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your-token"}'></textarea>
                    <small class="form-text text-muted">
                      Optional. Enter headers as JSON key-value pairs. Leave empty if no custom headers are needed.
                    </small>
                  </div>

                  <!-- Body Template Section -->
                  <div class="form-group">
                    <label><i class="fas fa-code" style="animation: none;"></i> Webhook Body Template</label>

                    <div class="template-editor-compact">
                      <!-- Variables Reference and Template Input in Two Columns -->
                      <div class="template-layout">
                        <!-- Left Column: Template Input -->
                        <div class="template-input-column">
                          <label for="webhookTemplate">JSON Template</label>
                          <textarea id="webhookTemplate" name="webhookTemplate" class="form-control template-textarea" rows="8" placeholder='{"event": "{% raw %}{{ event_type }}{% endraw %}", "user": {"id": "{% raw %}{{ user_id }}{% endraw %}", "name": "{% raw %}{{ username }}{% endraw %}"}, "camera": {"id": "{% raw %}{{ camera_id }}{% endraw %}", "name": "{% raw %}{{ camera_name }}{% endraw %}"}, "timestamp": "{% raw %}{{ timestamp }}{% endraw %}"}'  required></textarea>
                          <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Define your custom JSON structure using variables from the reference panel.
                          </small>
                        </div>

                        <!-- Right Column: Variables Reference -->
                        <div class="template-reference-column">
                          <div class="variables-reference">
                            <h6><i class="fas fa-list"></i> Available Variables</h6>
                            <div class="variables-compact">
                              <span class="var-tag">{% raw %}{{ user_id }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ username }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ camera_id }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ camera_name }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ timestamp }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ event_type }}{% endraw %}</span>
                            </div>

                            <div class="example-section">
                              <h6><i class="fas fa-lightbulb"></i> Example Template</h6>
                              <div class="example-template">
                                <pre>{
  "alert": "Face detected",
  "person": "{% raw %}{{ username }}{% endraw %}",
  "camera": "{% raw %}{{ camera_name }}{% endraw %}",
  "time": "{% raw %}{{ timestamp }}{% endraw %}"
}</pre>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-plus-circle" style="animation: none;"></i> Add Webhook
                    </button>
                    <button type="button" id="cancelFormBtn" class="btn btn-secondary">
                      <i class="fas fa-times" style="animation: none;"></i> Cancel
                    </button>
                  </div>
                </form>
              </div>

              <!-- Webhooks List Section -->
              <div class="webhook-management">
                <div class="section-header">
                  <h4><i class="fas fa-list" style="animation: none;"></i> Configured Webhooks</h4>
                  <button id="refreshWebhooksBtn" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
                  </button>
                </div>
                <div id="webhookList">
                  <!-- Webhooks will be loaded here -->
                </div>
              </div>
            </div>

            <!-- WhatsApp Management Subsection -->
            <div id="whatsapp-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>WhatsApp Management</h3>
                <p>Configure WhatsApp notifications for alerts</p>
              </div>

              <form id="whatsappForm">
                <div class="form-group">
                  <label for="whatsappPhone"><i class="fas fa-phone" style="animation: none;"></i> Phone Number</label>
                  <input type="text" id="whatsappPhone" name="whatsappPhone" class="form-control" placeholder="+1234567890" required>
                </div>

                <div class="form-group">
                  <label for="whatsappName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="whatsappName" name="whatsappName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add WhatsApp Contact
                </button>
              </form>

              <div class="whatsapp-list-section">
                <div class="section-header">
                  <h3>Configured WhatsApp Contacts</h3>
                  <button class="btn btn-primary" id="refreshWhatsappBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="whatsappList" class="whatsapp-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading WhatsApp contacts...</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- SMS Management Subsection -->
            <div id="sms-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>SMS Management</h3>
                <p>Configure SMS notifications for alerts</p>
              </div>

              <form id="smsForm">
                <div class="form-group">
                  <label for="smsPhone"><i class="fas fa-phone" style="animation: none;"></i> Phone Number</label>
                  <input type="text" id="smsPhone" name="smsPhone" class="form-control" placeholder="+1234567890" required>
                </div>

                <div class="form-group">
                  <label for="smsName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="smsName" name="smsName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add SMS Contact
                </button>
              </form>

              <div class="sms-list-section">
                <div class="section-header">
                  <h3>Configured SMS Contacts</h3>
                  <button class="btn btn-primary" id="refreshSmsBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="smsList" class="sms-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading SMS contacts...</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Email Management Subsection -->
            <div id="email-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>Email Management</h3>
                <p>Configure email notifications for alerts</p>
              </div>

              <form id="emailForm">
                <div class="form-group">
                  <label for="emailAddress"><i class="fas fa-envelope" style="animation: none;"></i> Email Address</label>
                  <input type="email" id="emailAddress" name="emailAddress" class="form-control" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                  <label for="emailName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="emailName" name="emailName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add Email Contact
                </button>
              </form>

              <div class="email-list-section">
                <div class="section-header">
                  <h3>Configured Email Contacts</h3>
                  <button class="btn btn-primary" id="refreshEmailBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="emailList" class="email-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading email contacts...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-dialog-content">
      <h3>Confirm Action</h3>
      <p id="confirmMessage">Are you sure you want to perform this action?</p>
      <div class="confirm-dialog-buttons">
        <button class="btn btn-danger" id="confirmYes">Delete</button>
        <button class="btn btn-primary" id="confirmNo">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Modal for image selection -->
  <div id="modal" class="modal" style="display: none;">
    <div class="modal-content">
      <h3>Select Profile Image Source</h3>
      <button type="button" id="upload-btn" class="btn btn-primary">
        <i class="fas fa-upload" style="animation: none;"></i> Upload from Device
      </button>
      <button type="button" id="capture-btn" class="btn btn-success">
        <i class="fas fa-camera" style="animation: none;"></i> Take Picture with Camera
      </button>
      <button type="button" id="close-modal-btn" class="btn btn-secondary">
        <i class="fas fa-times" style="animation: none;"></i> Cancel
      </button>
    </div>
  </div>

  <!-- Camera Section -->
  <div id="camera-container">
    <div class="camera-content">
      <h3>Take Profile Picture</h3>
      <video id="video" autoplay playsinline></video>
      <div class="camera-buttons">
        <button type="button" id="capture-image-btn" class="btn btn-success">
          <i class="fas fa-camera" style="animation: none;"></i> Capture Image
        </button>
        <button type="button" id="close-camera-btn" class="btn btn-danger">
          <i class="fas fa-times" style="animation: none;"></i> Close Camera
        </button>
      </div>
      <canvas id="canvas" style="display: none;"></canvas>
      <img id="image-preview" alt="Captured Image Preview" style="display: none;">
    </div>
  </div>

  <script>
    // DOM Elements
    const container = document.getElementById('unknownContainer');
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmYes = document.getElementById('confirmYes');
    const confirmNo = document.getElementById('confirmNo');
    const confirmMessage = document.getElementById('confirmMessage');

    // State variables
    let pendingAction = null;
    let pendingId = null;

    // Event Listeners for elements that exist
    const backBtn = document.getElementById('backBtn');
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        // Navigate back to the face recognition page without stopping the monitoring
        window.location.href = '/face_recognition';
      });
    }

    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        showToast('Refreshing data...', 'info');
        loadUnknowns();
      });
    }

    // Function to show confirmation dialog
    function showConfirmDialog(message, id, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.style.display = 'flex';

      // Store the callback and ID for later use
      pendingAction = onConfirm;
      pendingId = id;

      // Set up event listeners for the buttons
      confirmYes.onclick = () => {
        confirmDialog.style.display = 'none';
        if (pendingAction) {
          if (pendingId !== null) {
            pendingAction(pendingId);
          } else {
            pendingAction();
          }
        }
        pendingAction = null;
        pendingId = null;
      };

      confirmNo.onclick = () => {
        confirmDialog.style.display = 'none';
        pendingAction = null;
        pendingId = null;
      };

      // Also close when clicking outside the dialog content
      confirmDialog.addEventListener('click', function(event) {
        if (event.target === confirmDialog) {
          confirmDialog.style.display = 'none';
          pendingAction = null;
          pendingId = null;
        }
      }, { once: true });
    }

    // Function to show toast notification
    function showToast(message, type = 'info') {
      // Create toast element if it doesn't exist
      let toast = document.getElementById('toast');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        document.body.appendChild(toast);
      }

      // Set toast content and style based on type
      toast.textContent = message;
      toast.className = `toast ${type}`;

      // Show the toast
      toast.classList.add('show');

      // Hide after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }

    // Function to load unknown persons
    async function loadUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      `;

      try {
        const res = await fetch('/face_recognition/get-unknowns');
        const data = await res.json();

        if (!data.unknowns || data.unknowns.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Clear container
        container.innerHTML = '';

        // Create cards for each unknown person
        data.unknowns.forEach(person => {
          const card = document.createElement('div');
          card.className = 'card';
          card.setAttribute('data-id', person.id);

          const imageSection = person.image_paths.map(path => {
            // Skip invalid paths
            if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
              console.error('Invalid image path:', path);
              return '';
            }

            // Sanitize path - replace backslashes with forward slashes
            const sanitizedPath = path.replace(/\\/g, '/');
            // Encode the path to prevent issues with special characters
            const encodedPath = sanitizedPath.replace(/'/g, "\\'");

            return `<div class="image-container">
              <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
              <div class="image-actions">
                <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                  <i class="fas fa-exchange-alt" style="animation: none;"></i>
                </button>
                <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </div>`;
          }).join('');

          // Create attendance section if there are attendance records
          let attendanceSection = '';
          if (person.attendance_records && person.attendance_records.length > 0) {
            attendanceSection = `
              <div class="attendance-section">
                <h4><i class="fas fa-clock" style="animation: none;"></i> Attendance Records</h4>
                <div class="attendance-list">
                  <table class="attendance-table">
                    <thead>
                      <tr>
                        <th>Date & Time</th>
                        <th>Camera</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${person.attendance_records.map(record => `
                        <tr>
                          <td>${record.timestamp}</td>
                          <td>${record.camera_name}</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              </div>
            `;
          }

          card.innerHTML = `
            <div class="card-header">
              <div class="card-title">
                <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
              </div>
              <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </div>
            <div class="card-images">${imageSection}</div>
            ${attendanceSection}
            <div class="form-container">
              <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                <input type="hidden" name="unknown_id" value="${person.id}">

                <!-- Mode selection buttons -->
                <div class="mode-selection" style="margin-bottom: 15px;">
                  <div class="btn-group" role="group" style="width: 100%;">
                    <button type="button" class="btn btn-outline-primary mode-btn" data-mode="existing" onclick="switchMode(${person.id}, 'existing')" style="width: 50%;">
                      <i class="fas fa-user-check" style="animation: none;"></i> Existing User
                    </button>
                    <button type="button" class="btn btn-outline-primary mode-btn" data-mode="new" onclick="switchMode(${person.id}, 'new')" style="width: 50%;">
                      <i class="fas fa-user-plus" style="animation: none;"></i> New User
                    </button>
                  </div>
                </div>

                <!-- Loading indicator for potential matches -->
                <div id="loading-matches-${person.id}" style="display: none; text-align: center; margin-bottom: 15px;">
                  <i class="fas fa-spinner fa-spin"></i> Checking for potential matches...
                </div>

                <!-- Potential matches section (initially hidden) -->
                <div id="potential-matches-${person.id}" style="display: none; margin-bottom: 15px;">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Potential matches found. Select a user from the dropdown or switch to "New User" mode.
                  </div>
                </div>

                <!-- Existing user selection (initially hidden) -->
                <div id="existing-user-${person.id}" class="mode-container" style="display: none;">
                  <div class="form-group">
                    <label for="user-select-${person.id}"><i class="fas fa-users" style="animation: none;"></i> Select User</label>
                    <select id="user-select-${person.id}" class="form-control" onchange="userSelected(${person.id})">
                      <option value="">-- Select a user --</option>
                    </select>
                  </div>
                  <div class="selected-user-info" id="selected-user-info-${person.id}" style="display: none; margin-top: 10px;">
                    <div class="alert alert-success">
                      <strong>Selected User:</strong> <span id="selected-username-${person.id}"></span><br>
                      <strong>Email:</strong> <span id="selected-email-${person.id}"></span>
                      <input type="hidden" id="selected-username-input-${person.id}" name="username">
                      <input type="hidden" id="selected-email-input-${person.id}" name="email">
                    </div>
                  </div>
                </div>

                <!-- New user input (initially shown) -->
                <div id="new-user-${person.id}" class="mode-container">
                  <div class="form-group">
                    <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username">
                  </div>
                  <div class="form-group">
                    <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address">
                  </div>
                </div>

                <div class="form-actions">
                  <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                  </button>
                  <small class="form-text text-muted" style="margin-top: 8px;">
                    <i class="fas fa-info-circle"></i> You can assign multiple images to the same user to improve recognition.
                  </small>
                </div>
              </form>
            </div>
          `;

          container.appendChild(card);

          // Add form submission handler
          const form = document.getElementById(`form-${person.id}`);
          form.addEventListener('submit', function(e) {
            e.preventDefault();
            assignUnknown(this, person.id);
          });

          // Initialize the UI - check for potential matches
          setTimeout(() => {
            // Set the "New User" mode as default initially
            switchMode(person.id, 'new');

            // Check if this unknown person might match existing users
            checkUnknownMatches(person.id).then(result => {
              if (result.is_likely_registered && result.matches.length > 0) {
                // If there are potential matches, switch to existing user mode
                switchMode(person.id, 'existing');
              }
            });
          }, 100);
        });
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load unknown persons. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Global variable to store registered users
    let registeredUsers = [];

    // Function to load registered users
    async function loadRegisteredUsers() {
      try {
        const response = await fetch('/face_recognition/get-registered-users');
        const data = await response.json();
        registeredUsers = data.users;
        return registeredUsers;
      } catch (error) {
        console.error('Error loading registered users:', error);
        showToast('Failed to load registered users', 'error');
        return [];
      }
    }

    // Function to check if an unknown person matches any registered users
    async function checkUnknownMatches(unknownId) {
      try {
        const response = await fetch(`/face_recognition/check-unknown-matches/${unknownId}`);
        return await response.json();
      } catch (error) {
        console.error('Error checking unknown matches:', error);
        return { matches: [], is_likely_registered: false };
      }
    }

    // Function to switch between existing and new user modes
    async function switchMode(personId, mode) {
      // Update button states
      const modeButtons = document.querySelectorAll(`[data-id="${personId}"] .mode-btn`);
      modeButtons.forEach(btn => {
        if (btn.getAttribute('data-mode') === mode) {
          btn.classList.remove('btn-outline-primary');
          btn.classList.add('btn-primary');
        } else {
          btn.classList.remove('btn-primary');
          btn.classList.add('btn-outline-primary');
        }
      });

      // Show/hide appropriate containers
      const existingUserContainer = document.getElementById(`existing-user-${personId}`);
      const newUserContainer = document.getElementById(`new-user-${personId}`);

      // Get the form inputs
      const usernameInput = document.getElementById(`username-${personId}`);
      const emailInput = document.getElementById(`email-${personId}`);
      const userSelect = document.getElementById(`user-select-${personId}`);

      if (mode === 'existing') {
        // Switch to existing user mode
        existingUserContainer.style.display = 'block';
        newUserContainer.style.display = 'none';

        // Make dropdown required and manual inputs not required
        userSelect.setAttribute('required', 'required');
        usernameInput.removeAttribute('required');
        emailInput.removeAttribute('required');

        // Show loading indicator
        document.getElementById(`loading-matches-${personId}`).style.display = 'block';

        // Load registered users if not already loaded
        if (registeredUsers.length === 0) {
          await loadRegisteredUsers();
        }

        // Check for potential matches
        const matchesResult = await checkUnknownMatches(personId);

        // Hide loading indicator
        document.getElementById(`loading-matches-${personId}`).style.display = 'none';

        // Populate the dropdown with all users
        const selectElement = document.getElementById(`user-select-${personId}`);
        selectElement.innerHTML = '<option value="">-- Select a user --</option>';

        // Add matched users first with a special group if there are matches
        if (matchesResult.matches.length > 0) {
          const matchesGroup = document.createElement('optgroup');
          matchesGroup.label = 'Potential Matches';

          matchesResult.matches.forEach(match => {
            const option = document.createElement('option');
            option.value = match.id;
            option.textContent = `${match.username} (${match.email}) - ${Math.round(match.similarity * 100)}% match`;
            option.dataset.username = match.username;
            option.dataset.email = match.email;
            matchesGroup.appendChild(option);
          });

          selectElement.appendChild(matchesGroup);

          // Show potential matches alert
          document.getElementById(`potential-matches-${personId}`).style.display = 'block';
        }

        // Add all other users
        const otherUsersGroup = document.createElement('optgroup');
        otherUsersGroup.label = 'All Users';

        registeredUsers.forEach(user => {
          // Skip users that are already in the matches group
          if (!matchesResult.matches.some(match => match.id === user.id)) {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.username} (${user.email})`;
            option.dataset.username = user.username;
            option.dataset.email = user.email;
            otherUsersGroup.appendChild(option);
          }
        });

        selectElement.appendChild(otherUsersGroup);
      } else {
        // Switch to new user mode
        existingUserContainer.style.display = 'none';
        newUserContainer.style.display = 'block';
        document.getElementById(`potential-matches-${personId}`).style.display = 'none';

        // Make manual inputs required and dropdown not required
        userSelect.removeAttribute('required');
        usernameInput.setAttribute('required', 'required');
        emailInput.setAttribute('required', 'required');

        // Clear the hidden fields
        document.getElementById(`selected-username-input-${personId}`).value = '';
        document.getElementById(`selected-email-input-${personId}`).value = '';
      }
    }

    // Function to handle user selection from dropdown
    function userSelected(personId) {
      const selectElement = document.getElementById(`user-select-${personId}`);
      const selectedOption = selectElement.options[selectElement.selectedIndex];
      const selectedUserInfo = document.getElementById(`selected-user-info-${personId}`);

      if (selectElement.value) {
        // Update the selected user info display
        document.getElementById(`selected-username-${personId}`).textContent = selectedOption.dataset.username;
        document.getElementById(`selected-email-${personId}`).textContent = selectedOption.dataset.email;

        // Update hidden form fields
        document.getElementById(`selected-username-input-${personId}`).value = selectedOption.dataset.username;
        document.getElementById(`selected-email-input-${personId}`).value = selectedOption.dataset.email;

        // Also update the regular input fields (as a fallback)
        const usernameInput = document.getElementById(`username-${personId}`);
        const emailInput = document.getElementById(`email-${personId}`);
        if (usernameInput) usernameInput.value = selectedOption.dataset.username;
        if (emailInput) emailInput.value = selectedOption.dataset.email;

        // Show the selected user info
        selectedUserInfo.style.display = 'block';

        console.log(`Selected user: ${selectedOption.dataset.username}, email: ${selectedOption.dataset.email}`);
      } else {
        // Hide the selected user info if no user is selected
        selectedUserInfo.style.display = 'none';
      }
    }

    // Function to assign an unknown person to a user
    async function assignUnknown(form, id) {
      try {
        // Check which mode is active
        const existingUserMode = document.querySelector(`[data-id="${id}"] .mode-btn[data-mode="existing"]`).classList.contains('btn-primary');

        if (existingUserMode) {
          // In existing user mode, validate that a user is selected
          const selectElement = document.getElementById(`user-select-${id}`);
          if (!selectElement.value) {
            showToast('Please select a user from the dropdown', 'error');
            return;
          }

          // Make sure the hidden fields are populated
          const selectedUsername = document.getElementById(`selected-username-input-${id}`).value;
          const selectedEmail = document.getElementById(`selected-email-input-${id}`).value;

          if (!selectedUsername || !selectedEmail) {
            showToast('Please select a user from the dropdown', 'error');
            return;
          }
        } else {
          // In new user mode, validate the manual inputs
          const usernameInput = document.getElementById(`username-${id}`);
          const emailInput = document.getElementById(`email-${id}`);

          if (!usernameInput.value) {
            showToast('Please enter a username', 'error');
            usernameInput.focus();
            return;
          }

          if (!emailInput.value) {
            showToast('Please enter an email address', 'error');
            emailInput.focus();
            return;
          }
        }

        // Create a new FormData object
        const formData = new FormData();

        // Add the unknown_id
        formData.append('unknown_id', form.querySelector('input[name="unknown_id"]').value);

        if (existingUserMode) {
          // In existing user mode, use the values from the hidden fields
          const username = document.getElementById(`selected-username-input-${id}`).value;
          const email = document.getElementById(`selected-email-input-${id}`).value;

          console.log(`Submitting form with username: ${username}, email: ${email}`);

          formData.append('username', username);
          formData.append('email', email);
        } else {
          // In new user mode, use the values from the input fields
          const username = document.getElementById(`username-${id}`).value;
          const email = document.getElementById(`email-${id}`).value;

          console.log(`Submitting form with username: ${username}, email: ${email}`);

          formData.append('username', username);
          formData.append('email', email);
        }

        showToast('Assigning user...', 'info');

        const response = await fetch('/face_recognition/assign-unknown', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User assigned successfully!', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign user', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign user. Please try again.', 'error');
      }
    }

    // Function to confirm deletion of an unknown person
    function confirmDeleteUnknown(id) {
      showConfirmDialog('Are you sure you want to delete this unknown person?', id, deleteUnknown);
    }

    // Function to delete an unknown person
    async function deleteUnknown(id) {
      try {
        showToast('Deleting unknown person...', 'info');

        const response = await fetch(`/face_recognition/delete-unknown/${id}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Unknown person deleted successfully', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast(result.detail || 'Failed to delete unknown person', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete unknown person. Please try again.', 'error');
      }
    }

    // Function to enlarge an image when clicked
    function enlargeImage(src) {
      // Check if the source is valid
      if (!src || src === '' || src === '.jpg' || src.endsWith('/.jpg')) {
        console.error('Invalid image source:', src);
        return;
      }

      const modal = document.createElement('div');
      modal.style.position = 'fixed';
      modal.style.top = '0';
      modal.style.left = '0';
      modal.style.width = '100%';
      modal.style.height = '100%';
      modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
      modal.style.display = 'flex';
      modal.style.justifyContent = 'center';
      modal.style.alignItems = 'center';
      modal.style.zIndex = '2000';
      modal.style.cursor = 'pointer';

      const img = document.createElement('img');
      img.src = src;
      img.style.maxWidth = '90%';
      img.style.maxHeight = '90%';
      img.style.borderRadius = '8px';
      img.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';

      // Handle image loading error with simple fallback
      img.onerror = function() {
        console.log(`Image failed to load: ${src}`);
        // Just show placeholder without trying cropped_faces fallback
        this.src = 'https://via.placeholder.com/200?text=No+Image';
        this.onerror = null; // Prevent infinite error loop
      };

      modal.appendChild(img);
      document.body.appendChild(modal);

      modal.addEventListener('click', () => {
        document.body.removeChild(modal);
      });
    }

    // Tab functionality
    function setupTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      console.log('Found tab buttons:', tabBtns.length);

      if (tabBtns.length === 0) {
        console.error('No tab buttons found! Check if .tab-btn elements exist.');
        return;
      }

      // Function to switch to a specific tab
      function switchToTab(tabId) {
        console.log('Switching to tab:', tabId);

        // Remove active class from all buttons and panes
        tabBtns.forEach(b => b.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

        // Add active class to the button with the matching data-tab attribute
        const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);
        if (targetBtn) {
          targetBtn.classList.add('active');
          console.log('Activated button for tab:', tabId);

          // Show corresponding tab pane
          const tabPane = document.getElementById(`${tabId}-tab`);

          if (tabPane) {
            tabPane.classList.add('active');
            console.log('Activated pane for tab:', tabId);

            // Load data for the tab if needed
            if (tabId === 'unknown-persons') {
              loadUnknowns();
            } else if (tabId === 'camera-management') {
              loadCameras();
            } else if (tabId === 'user-management') {
              loadUsers();
            } else if (tabId === 'settings') {
              // For settings tab, check if there's a saved section
              const savedSection = localStorage.getItem('activeSettingsSection');
              if (savedSection) {
                console.log('Found saved settings section:', savedSection);

                // Use a small timeout to ensure the settings tab is fully loaded
                setTimeout(() => {
                  // Get all settings tiles and sections
                  const settingsTiles = document.querySelectorAll('.settings-tile');
                  const settingsSections = document.querySelectorAll('.settings-section');

                  // Remove active class from all tiles and sections
                  settingsTiles.forEach(t => t.classList.remove('active'));
                  settingsSections.forEach(s => s.classList.remove('active'));

                  // Add active class to the tile with matching data-settings-section
                  const targetTile = document.querySelector(`.settings-tile[data-settings-section="${savedSection}"]`);
                  if (targetTile) {
                    targetTile.classList.add('active');
                  }

                  // Show corresponding section
                  const section = document.getElementById(`${savedSection}-settings`);
                  if (section) {
                    section.classList.add('active');
                    console.log('Activated section:', savedSection);

                    // If alert management section is activated, load webhooks
                    if (savedSection === 'alert-management') {
                      if (typeof window.loadWebhooks === 'function') {
                        window.loadWebhooks();
                      }
                    }
                  }
                }, 50);
              } else {
                // Default to face recognition settings
                if (typeof window.loadSettings === 'function') {
                  window.loadSettings();
                }
              }
            }

            // Update URL hash
            window.location.hash = tabId;
          } else {
            console.error(`Tab pane with ID "${tabId}-tab" not found`);
          }
        } else {
          console.error(`Tab button with data-tab="${tabId}" not found`);
        }
      }

      // Add click event listeners to tab buttons
      tabBtns.forEach((btn, index) => {
        console.log(`Adding click listener to button ${index}:`, btn.getAttribute('data-tab'));
        btn.addEventListener('click', (e) => {
          console.log('Tab button clicked:', btn.getAttribute('data-tab'));
          e.preventDefault();

          const tabId = btn.getAttribute('data-tab');
          switchToTab(tabId);

          // Load data for specific tabs
          if (tabId === 'user-management') {
            loadUsers();
          } else if (tabId === 'camera-management') {
            loadCameras();
          } else if (tabId === 'management') {
            enterManagementMode();
          }
        });
      });

      // Check for hash in URL on page load
      if (window.location.hash) {
        const tabId = window.location.hash.substring(1); // Remove the # character
        switchToTab(tabId);
      }
    }

    // Management Mode Functions
    function enterManagementMode() {
      console.log('Entering management mode...');
      document.body.classList.add('management-active');

      // Initialize management functionality if not already done
      if (!window.managementInitialized) {
        setTimeout(() => {
          console.log('Initializing management functionality...');
          // The management initialization is handled by the external admin_panel.js
          window.managementInitialized = true;
        }, 100);
      }
    }

    function exitManagementMode() {
      console.log('Exiting management mode...');
      document.body.classList.remove('management-active');

      // Switch back to the management tab in normal mode
      switchToTab('management');
    }

    // Make functions globally available
    window.enterManagementMode = enterManagementMode;
    window.exitManagementMode = exitManagementMode;

    // Camera Management Functions
    function setupCameraManagement() {
      const cameraForm = document.getElementById('cameraForm');
      const refreshCamerasBtn = document.getElementById('refreshCamerasBtn');

      // Load cameras initially
      loadCameras();

      // Refresh cameras button
      refreshCamerasBtn.addEventListener('click', () => {
        loadCameras();
      });

      // Handle form submission to add the camera
      cameraForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const cameraName = document.getElementById('cameraName').value;
        const rtspUrl = document.getElementById('rtspUrl').value;

        try {
          showToast('Adding camera...', 'info');

          // Send data to backend to save the camera information
          const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cameraName, rtspUrl }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            showToast('Camera added successfully!', 'success');
            cameraForm.reset();
            loadCameras();
          } else if (result.status === 'error') {
            showToast(`Error: RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
          } else if (result.status === 'samename') {
            showToast('Camera with this name already exists', 'error');
          } else {
            showToast('Error adding camera.', 'error');
          }
        } catch (error) {
          console.error('Error adding camera:', error);
          showToast('Failed to add camera. Please check your connection.', 'error');
        }
      });
    }

    // Function to load and display the list of cameras
    async function loadCameras() {
      const cameraList = document.getElementById('cameraList');
      console.log('camera list:', cameraList);
      cameraList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading cameras...</p>
        </div>
      `;

      try {
        const response = await fetch('/face_recognition/get-cameras', {
          method: 'GET',
        });

        console.log('getting cameras..');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        console.log('Cameras:', cameras);
        cameraList.innerHTML = ''; // Clear previous list

        if (Object.keys(cameras).length === 0) {
          cameraList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-video-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No cameras configured.</p>
            </div>
          `;
          return;
        }

        // Create a table for cameras
        const table = document.createElement('table');
        table.className = 'camera-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Camera Name</th>
              <th>RTSP URL</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${cameraName}</td>
            <td><span class="camera-url">${Array.isArray(rtspUrl) ? rtspUrl[0] : rtspUrl}</span></td>
            <td>
              <button class="btn btn-danger btn-sm" onclick="confirmDeleteCamera('${cameraName}')">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </td>
          `;
          tbody.appendChild(row);
        }

        cameraList.appendChild(table);
      } catch (error) {
        console.error('Error loading cameras:', error);
        cameraList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load cameras. Please check your connection.</p>
            <button class="btn btn-primary" onclick="loadCameras()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
      console.log('in except catch');
    }

    // Function to confirm camera deletion
    function confirmDeleteCamera(cameraName) {
      showConfirmDialog(`Are you sure you want to delete the camera "${cameraName}"?`, null, () => {
        deleteCamera(cameraName);
      });
    }

    // Function to delete a camera
    async function deleteCamera(cameraName) {
      try {
        showToast('Deleting camera...', 'info');

        const response = await fetch(`/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (result.status === 'success') {
          showToast(result.message, 'success');
          loadCameras(); // Refresh the list
        } else {
          showToast('Error deleting camera: ' + result.message, 'error');
        }
      } catch (error) {
        console.error('Error deleting camera:', error);
        showToast('Failed to delete camera. Please check your connection.', 'error');
      }
    }

    // User Registration Functions
    function setupUserRegistration() {
      const registerForm = document.getElementById('registerForm');
      const chooseFileBtn = document.getElementById('choose-file-btn');
      const uploadInput = document.getElementById('upload-input');
      const fileStatus = document.getElementById('file-status');
      const modal = document.getElementById('modal');
      const uploadBtn = document.getElementById('upload-btn');
      const captureBtn = document.getElementById('capture-btn');
      const closeModalBtn = document.getElementById('close-modal-btn');
      const cameraContainer = document.getElementById('camera-container');
      const video = document.getElementById('video');
      const captureImageBtn = document.getElementById('capture-image-btn');
      const closeCameraBtn = document.getElementById('close-camera-btn');
      const canvas = document.getElementById('canvas');
      const imagePreview = document.getElementById('image-preview');
      const capturedImageInput = document.getElementById('captured_image');

      // Handle file selection button
      chooseFileBtn.addEventListener('click', () => {
        modal.style.display = 'block';
      });

      // Close modal when the close button is clicked
      closeModalBtn.addEventListener('click', () => {
        modal.style.display = 'none';
      });

      // Close modal when clicking outside the modal
      window.addEventListener('click', (event) => {
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      });

      // Upload from device
      uploadBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        uploadInput.click();
      });

      // Update file status when a file is selected
      uploadInput.addEventListener('change', () => {
        if (uploadInput.files.length > 0) {
          fileStatus.textContent = `Selected: ${uploadInput.files[0].name}`;
        } else {
          fileStatus.textContent = 'No file chosen';
        }
      });

      // Take Picture with Camera
      captureBtn.addEventListener('click', async () => {
        modal.style.display = 'none';

        console.log("Opening camera...");

        // Show camera container immediately without animation first
        cameraContainer.style.display = 'block';
        cameraContainer.style.opacity = '1';
        cameraContainer.style.visibility = 'visible';

        try {
          console.log("Requesting camera access...");
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: "user"
            }
          });
          console.log("Camera access granted");
          video.srcObject = stream;

          // Make sure video is visible
          video.style.display = 'block';

          // Log when video is ready
          video.onloadedmetadata = () => {
            console.log("Video metadata loaded, dimensions:", video.videoWidth, "x", video.videoHeight);
          };
        } catch (err) {
          // Create toast notification for error
          showToast('Could not access the camera. Please check permissions.', 'error');
          console.error("Camera error:", err);
          hideCamera();
        }
      });

      // Function to hide camera with animation
      function hideCamera() {
        console.log("Hiding camera...");
        // Directly set styles instead of using classes
        cameraContainer.style.opacity = '0';
        cameraContainer.style.visibility = 'hidden';

        // Wait for transition to complete before hiding
        setTimeout(() => {
          cameraContainer.style.display = 'none';
          console.log("Camera hidden");
        }, 300); // Match this with the transition duration
      }

      // Capture Image
      captureImageBtn.addEventListener('click', () => {
        try {
          const context = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          const dataURL = canvas.toDataURL('image/png');
          imagePreview.src = dataURL;
          imagePreview.style.display = 'block';

          // Convert the base64 string to a Blob
          const byteString = atob(dataURL.split(',')[1]);
          const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          const blob = new Blob([ab], { type: mimeString });
          const file = new File([blob], 'captured-image.png', { type: mimeString });

          // Now append the file to the form input
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          uploadInput.files = dataTransfer.files;

          // Update file status text
          fileStatus.textContent = "Image successfully captured";

          // Automatically set the base64 value in the hidden input for reference (if needed)
          capturedImageInput.value = dataURL;

          // Stop the video stream
          video.srcObject.getTracks().forEach(track => track.stop());

          // Hide camera with animation
          hideCamera();

          // Show success message
          showToast('Image captured successfully!', 'success');
        } catch (err) {
          console.error('Error capturing image:', err);
          showToast('Failed to capture image. Please try again.', 'error');
        }
      });

      // Close Camera
      closeCameraBtn.addEventListener('click', () => {
        // Stop all video tracks
        if (video.srcObject) {
          video.srcObject.getTracks().forEach(track => track.stop());
        }

        // Hide camera with animation
        hideCamera();
      });

      // Form submission with AJAX
      registerForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Always prevent default form submission

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const hasFile = uploadInput.files.length > 0 || document.getElementById('captured_image').value;

        if (!username || !email || !hasFile) {
          let message = 'Please fill in all fields';
          if (!hasFile) {
            message += ' and provide a profile image';
          }
          showToast(message + '.', 'error');
          return;
        }

        // Show loading message
        showToast('Registering user...', 'info');

        // Create FormData object from the form
        const formData = new FormData(registerForm);

        // Submit the form via AJAX
        fetch('/face_recognition/register', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.message) {
            // Show success message
            showToast(data.message, 'success');

            // Clear the form
            registerForm.reset();
            fileStatus.textContent = 'No file chosen';

            // Refresh the user list if we're on that tab
            if (document.querySelector('.tab-btn[data-tab="user-management"]').classList.contains('active')) {
              loadUsers();
            }
          } else if (data.detail) {
            // Show error message
            showToast(data.detail, 'error');
          }
        })
        .catch(error => {
          console.error('Error registering user:', error);
          showToast('Failed to register user. Please try again.', 'error');
        });
      });
    }

    // User Management Functions
    function setupUserManagement() {
      const refreshUsersBtn = document.getElementById('refreshUsersBtn');
      const searchUsersBtn = document.getElementById('searchUsersBtn');
      const userSearchInput = document.getElementById('userSearchInput');

      if (!refreshUsersBtn || !searchUsersBtn || !userSearchInput) {
        console.error('Some user management elements not found!');
        return;
      }

      // Load users initially
      loadUsers();

      // Refresh users button
      refreshUsersBtn.addEventListener('click', () => {
        loadUsers();
      });

      // Search users button
      searchUsersBtn.addEventListener('click', () => {
        const searchTerm = userSearchInput.value.trim();
        loadUsers(searchTerm);
      });

      // Search on Enter key
      userSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const searchTerm = userSearchInput.value.trim();
          loadUsers(searchTerm);
        }
      });
    }

    // Function to load and display users
    async function loadUsers(searchTerm = '') {
      const usersList = document.getElementById('usersList');
      usersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading users...</p>
        </div>
      `;

      try {
        // Construct the URL with search parameter if provided
        let url = '/face_recognition/users';
        if (searchTerm) {
          url += `?search=${encodeURIComponent(searchTerm)}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const users = await response.json();

        if (users.length === 0) {
          usersList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-users-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No users found${searchTerm ? ' matching your search' : ''}.</p>
            </div>
          `;
          return;
        }

        // Create a table for users
        const table = document.createElement('table');
        table.className = 'users-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Image</th>
              <th>User Info</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const user of users) {
          const row = document.createElement('tr');

          // Use the original uploaded image from static/images folder
          // Encode the username to handle spaces and special characters
          const encodedUsername = encodeURIComponent(user.username);
          let userImage = `/static/images/${encodedUsername}.jpg`;

          // Store fallback images in a data attribute (only database images, no cropped_faces)
          let fallbackImages = [];

          // First fallback: try non-encoded username version (since we're starting with encoded)
          fallbackImages.push(`/static/images/${user.username}.jpg`);

          // Second fallback: images from database
          if (user.images && user.images.length > 0) {
            user.images.forEach(img => {
              fallbackImages.push(img.image_url.replace('./', '/'));
            });
          }

          // Store fallbacks as JSON in a data attribute
          row.setAttribute('data-fallback-images', JSON.stringify(fallbackImages));

          // Create the row HTML
          row.innerHTML = `
            <td>
              <img src="${userImage}" alt="${user.username}" class="user-image" onclick="enlargeImage('${userImage}')">
            </td>
            <td>
              <strong>${user.username}</strong><br>
              <span class="user-email">${user.email}</span>
            </td>
            <td>
              <div class="user-actions">
                <button class="btn btn-primary btn-sm" onclick="viewUserDetails(${user.id})" title="View Details">
                  <i class="fas fa-eye" style="animation: none;"></i>
                </button>
                <button class="btn btn-success btn-sm" onclick="manageCameraPermissions(${user.id}, '${user.username}')" title="Camera Permissions">
                  <i class="fas fa-video" style="animation: none;"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="confirmDeleteUser(${user.id}, '${user.username}')" title="Delete User">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </td>
          `;

          // Add error handling for the image with fallbacks
          const imgElement = row.querySelector('img');
          imgElement.onerror = function() {
            try {
              // Get the fallback images array
              const fallbackImagesStr = row.getAttribute('data-fallback-images');
              if (fallbackImagesStr) {
                const fallbacks = JSON.parse(fallbackImagesStr);

                if (fallbacks.length > 0) {
                  // Try the next fallback image
                  const nextFallback = fallbacks.shift();
                  console.log(`Trying fallback image for ${user.username}: ${nextFallback}`);
                  this.src = nextFallback;

                  // Update the data attribute with remaining fallbacks
                  row.setAttribute('data-fallback-images', JSON.stringify(fallbacks));

                  // If this is the last fallback, set up final error handler
                  if (fallbacks.length === 0) {
                    this.onerror = function() {
                      console.error(`All image fallbacks failed for ${user.username}`);
                      this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Vc2VyPC90ZXh0Pjwvc3ZnPg==';
                      this.onerror = null; // Prevent infinite error loop
                    };
                  }
                  return;
                }
              }

              // If we get here, no fallbacks worked
              this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Vc2VyPC90ZXh0Pjwvc3ZnPg==';
              this.onerror = null; // Prevent infinite error loop
            } catch (e) {
              console.error('Error in image fallback handler:', e);
              this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Vc2VyPC90ZXh0Pjwvc3ZnPg==';
              this.onerror = null; // Prevent infinite error loop
            }
          };

          tbody.appendChild(row);
        }

        usersList.innerHTML = '';
        usersList.appendChild(table);
      } catch (error) {
        console.error('Error loading users:', error);
        usersList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load users. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUsers()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
    }

    // Function to view user details
    function viewUserDetails(userId) {
      // Redirect to the user details page
      window.location.href = `/face_recognition/user/${userId}/page`;
      console.log(`Navigating to user details page: /face_recognition/user/${userId}/page`);
    }

    // Manage camera permissions
    async function manageCameraPermissions(userId, username) {
      try {
        showToast('Loading camera permissions...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}/camera-permissions`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        showCameraPermissionsModal(data);
      } catch (error) {
        console.error('Error loading camera permissions:', error);
        showToast('Failed to load camera permissions. Please try again.', 'error');
      }
    }

    // Show camera permissions modal
    function showCameraPermissionsModal(data) {
      // Create modal
      const modal = document.createElement('div');
      modal.className = 'camera-permissions-modal';
      modal.innerHTML = `
        <div class="camera-permissions-content">
          <div class="camera-permissions-header">
            <h3><i class="fas fa-video"></i> Camera Permissions - ${data.username}</h3>
            <button class="template-modal-close" onclick="closeCameraPermissionsModal()">&times;</button>
          </div>
          <div class="camera-permissions-body">
            <div class="camera-permissions-list" id="cameraPermissionsList">
              ${data.cameras.map(camera => `
                <div class="camera-permission-item">
                  <div class="camera-info">
                    <div class="camera-name">${camera.camera_name}</div>
                    <div class="camera-url">${camera.rtsp_url}</div>
                  </div>
                  <div class="permission-toggle">
                    <span class="permission-status ${camera.has_permission ? 'allowed' : 'denied'}">
                      ${camera.has_permission ? 'Allowed' : 'Denied'}
                    </span>
                    <label class="permission-switch">
                      <input type="checkbox"
                             data-camera-id="${camera.camera_id}"
                             ${camera.has_permission ? 'checked' : ''}
                             onchange="updatePermissionStatus(this)">
                      <span class="permission-slider"></span>
                    </label>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="camera-permissions-footer">
            <div class="permissions-summary">
              <div class="summary-item">
                <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                <span>Allowed: <span class="summary-count" id="allowedCount">0</span></span>
              </div>
              <div class="summary-item">
                <i class="fas fa-times-circle" style="color: #f44336;"></i>
                <span>Denied: <span class="summary-count" id="deniedCount">0</span></span>
              </div>
            </div>
            <div>
              <button class="btn btn-secondary" onclick="closeCameraPermissionsModal()">Cancel</button>
              <button class="btn btn-success" onclick="saveCameraPermissions(${data.user_id})">
                <i class="fas fa-save"></i> Save Changes
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      // Update summary counts
      updatePermissionsSummary();

      // Close on background click
      modal.onclick = (e) => {
        if (e.target === modal) {
          closeCameraPermissionsModal();
        }
      };
    }

    // Update permission status when toggle is changed
    function updatePermissionStatus(checkbox) {
      const statusSpan = checkbox.closest('.camera-permission-item').querySelector('.permission-status');
      if (checkbox.checked) {
        statusSpan.textContent = 'Allowed';
        statusSpan.className = 'permission-status allowed';
      } else {
        statusSpan.textContent = 'Denied';
        statusSpan.className = 'permission-status denied';
      }
      updatePermissionsSummary();
    }

    // Update permissions summary counts
    function updatePermissionsSummary() {
      const checkboxes = document.querySelectorAll('#cameraPermissionsList input[type="checkbox"]');
      const allowedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
      const deniedCount = checkboxes.length - allowedCount;

      const allowedCountEl = document.getElementById('allowedCount');
      const deniedCountEl = document.getElementById('deniedCount');

      if (allowedCountEl) allowedCountEl.textContent = allowedCount;
      if (deniedCountEl) deniedCountEl.textContent = deniedCount;
    }

    // Save camera permissions
    async function saveCameraPermissions(userId) {
      try {
        const checkboxes = document.querySelectorAll('#cameraPermissionsList input[type="checkbox"]:checked');
        const cameraIds = Array.from(checkboxes).map(cb => parseInt(cb.dataset.cameraId));

        showToast('Saving camera permissions...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}/camera-permissions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ camera_ids: cameraIds }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        showToast('Camera permissions updated successfully!', 'success');
        closeCameraPermissionsModal();
      } catch (error) {
        console.error('Error saving camera permissions:', error);
        showToast('Failed to save camera permissions. Please try again.', 'error');
      }
    }

    // Close camera permissions modal
    function closeCameraPermissionsModal() {
      const modal = document.querySelector('.camera-permissions-modal');
      if (modal) {
        document.body.removeChild(modal);
      }
    }

    // Function to confirm user deletion
    function confirmDeleteUser(userId, username) {
      showConfirmDialog(`Are you sure you want to delete the user "${username}"?`, null, () => {
        deleteUser(userId);
      });
    }

    // Function to delete a user
    async function deleteUser(userId) {
      try {
        showToast('Deleting user...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User deleted successfully', 'success');
          loadUsers(); // Refresh the list
        } else {
          showToast(result.detail || 'Failed to delete user', 'error');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        showToast('Failed to delete user. Please try again.', 'error');
      }
    }

    // Settings Management Functions
    function setupSettingsManagement() {
      const settingsForm = document.getElementById('settingsForm');
      const faceThreshold = document.getElementById('faceThreshold');
      const thresholdValue = document.getElementById('thresholdValue');
      const settingsStatus = document.getElementById('settingsStatus');
      const settingsTiles = document.querySelectorAll('.settings-tile');
      const settingsSections = document.querySelectorAll('.settings-section');
      const settingsSidebarItems = document.querySelectorAll('.settings-sidebar-item');
      const settingsSubsections = document.querySelectorAll('.settings-subsection');
      const webhookForm = document.getElementById('webhookForm');
      const refreshWebhooksBtn = document.getElementById('refreshWebhooksBtn');

      // Main Settings Tiles Navigation
      settingsTiles.forEach(tile => {
        tile.addEventListener('click', () => {
          console.log('Settings tile clicked:', tile.getAttribute('data-settings-section'));

          // Remove active class from all tiles and sections
          settingsTiles.forEach(t => t.classList.remove('active'));
          settingsSections.forEach(s => s.classList.remove('active'));

          // Add active class to clicked tile
          tile.classList.add('active');

          // Show corresponding section
          const sectionId = tile.getAttribute('data-settings-section');
          const section = document.getElementById(`${sectionId}-settings`);

          if (section) {
            section.classList.add('active');
            console.log('Activated section:', sectionId);

            // Save the active section to localStorage
            localStorage.setItem('activeSettingsSection', sectionId);
            console.log('Saved active section to localStorage:', sectionId);

            // Activate the first sidebar item in the section by default
            const firstSidebarItem = section.querySelector('.settings-sidebar-item');
            if (firstSidebarItem) {
              // Simulate a click on the first sidebar item
              const subsectionId = firstSidebarItem.getAttribute('data-subsection');
              activateSubsection(section, subsectionId);
            }

            // If alert management section is activated, load webhooks
            if (sectionId === 'alert-management') {
              // Load the appropriate subsection data
              const activeSubsection = document.querySelector('#alert-management-settings .settings-sidebar-item.active');
              if (activeSubsection) {
                const subsectionId = activeSubsection.getAttribute('data-subsection');
                if (subsectionId === 'webhook') {
                  loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    initializeTemplateEditor();
                  }, 100);
                } else if (subsectionId === 'whatsapp') {
                  loadWhatsAppContacts();
                } else if (subsectionId === 'sms') {
                  loadSMSContacts();
                } else if (subsectionId === 'email') {
                  loadEmailContacts();
                }
              } else {
                // Default to loading webhooks if no subsection is active
                loadWebhooks();
                // Initialize template editor when webhook subsection is activated
                setTimeout(() => {
                  initializeTemplateEditor();
                }, 100);
              }
            }
          } else {
            console.error(`Settings section with ID "${sectionId}-settings" not found`);
          }
        });
      });

      // Settings Sidebar Items Navigation
      settingsSidebarItems.forEach(item => {
        item.addEventListener('click', () => {
          const subsectionId = item.getAttribute('data-subsection');
          const parentSection = item.closest('.settings-section');

          if (parentSection) {
            activateSubsection(parentSection, subsectionId);

            // Load the appropriate data based on the subsection
            if (subsectionId === 'webhook') {
              loadWebhooks();
              // Initialize template editor when webhook subsection is activated
              setTimeout(() => {
                initializeTemplateEditor();
              }, 100);
            } else if (subsectionId === 'whatsapp') {
              loadWhatsAppContacts();
            } else if (subsectionId === 'sms') {
              loadSMSContacts();
            } else if (subsectionId === 'email') {
              loadEmailContacts();
            }
          }
        });
      });

      // Function to activate a subsection within a section
      function activateSubsection(parentSection, subsectionId) {
        console.log('Activating subsection:', subsectionId);

        // Remove active class from all sidebar items in this section
        const sidebarItems = parentSection.querySelectorAll('.settings-sidebar-item');
        sidebarItems.forEach(si => si.classList.remove('active'));

        // Add active class to the clicked sidebar item
        const clickedItem = parentSection.querySelector(`.settings-sidebar-item[data-subsection="${subsectionId}"]`);
        if (clickedItem) {
          clickedItem.classList.add('active');
        }

        // Remove active class from all subsections in this section
        const subsections = parentSection.querySelectorAll('.settings-subsection');
        subsections.forEach(ss => ss.classList.remove('active'));

        // Add active class to the corresponding subsection
        const subsection = parentSection.querySelector(`#${subsectionId}-subsection`);
        if (subsection) {
          subsection.classList.add('active');

          // Save the active subsection to localStorage
          localStorage.setItem('activeSettingsSubsection', subsectionId);
          console.log('Saved active subsection to localStorage:', subsectionId);
        } else {
          console.error(`Subsection with ID "${subsectionId}-subsection" not found`);
        }
      }

      // Update the displayed threshold value when the slider changes
      faceThreshold.addEventListener('input', () => {
        thresholdValue.textContent = faceThreshold.value;
      });

      // Load current settings when the tab is shown
      async function loadSettings() {
        try {
          console.log('Loading face recognition settings...');
          const response = await fetch('/face_recognition/get-settings');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const settings = await response.json();
          // Update the slider with the current threshold value
          if (settings.face_threshold) {
            faceThreshold.value = settings.face_threshold;
            thresholdValue.textContent = settings.face_threshold;
          }

          // Restore active section from localStorage
          const activeSection = localStorage.getItem('activeSettingsSection');
          if (activeSection) {
            // Find the tile for this section
            const tile = document.querySelector(`.settings-tile[data-settings-section="${activeSection}"]`);
            if (tile) {
              // Simulate a click on the tile
              tile.click();

              // Restore active subsection from localStorage
              const activeSubsection = localStorage.getItem('activeSettingsSubsection');
              if (activeSubsection) {
                // Find the section element
                const sectionElement = document.getElementById(`${activeSection}-settings`);
                if (sectionElement) {
                  // Find the sidebar item for this subsection
                  const sidebarItem = sectionElement.querySelector(`.settings-sidebar-item[data-subsection="${activeSubsection}"]`);
                  if (sidebarItem) {
                    // Simulate a click on the sidebar item
                    sidebarItem.click();
                  }
                }
              }
            }
          }

          // Check which subsection is active and load appropriate data
          const activeSubsection = localStorage.getItem('activeSettingsSubsection');
          if (activeSubsection === 'webhook') {
            if (typeof window.loadWebhooks === 'function') {
              window.loadWebhooks();
              // Initialize template editor when webhook subsection is activated
              setTimeout(() => {
                initializeTemplateEditor();
              }, 100);
            }
          } else if (activeSubsection === 'whatsapp') {
            if (typeof window.loadWhatsAppContacts === 'function') {
              window.loadWhatsAppContacts();
            }
          } else if (activeSubsection === 'sms') {
            if (typeof window.loadSMSContacts === 'function') {
              window.loadSMSContacts();
            }
          } else if (activeSubsection === 'email') {
            if (typeof window.loadEmailContacts === 'function') {
              window.loadEmailContacts();
            }
          }
        } catch (error) {
          console.error('Error loading settings:', error);
          showToast('Failed to load settings. Please try again.', 'error');
        }
      }

      // Handle form submission to save settings
      settingsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const threshold = faceThreshold.value;

        try {
          showToast('Saving settings...', 'info');

          const response = await fetch('/face_recognition/update-settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ face_threshold: parseFloat(threshold) }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Show success message
          showToast(result.message || 'Settings saved successfully!', 'success');

          // Show the settings status message
          settingsStatus.style.display = 'block';

          // Hide the status message after 3 seconds
          setTimeout(() => {
            settingsStatus.style.display = 'none';
          }, 3000);
        } catch (error) {
          console.error('Error saving settings:', error);
          showToast('Failed to save settings. Please try again.', 'error');
        }
      });

      // Webhook Management
      // Load webhooks - define as a function that can be called globally
      window.loadWebhooks = async function() {
        console.log('Loading webhooks...');
        const webhookList = document.getElementById('webhookList');

        if (!webhookList) {
          console.error('Webhook list element not found');
          return;
        }

        webhookList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading webhooks...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/webhooks');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const webhooks = await response.json();

          if (webhooks.length === 0) {
            webhookList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-bell-slash" style="animation: none;"></i>
                <h3>No Webhooks Configured</h3>
                <p>Add a webhook URL to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create cards to display webhooks for better readability
          let cardsHTML = '<div class="webhook-cards">';

          webhooks.forEach(webhook => {
            // Format body template for display
            let templateDisplay = 'No template';
            let templatePreview = '';
            if (webhook.body_template) {
              try {
                const templateStr = JSON.stringify(webhook.body_template, null, 2);
                templateDisplay = templateStr.length > 100
                  ? `${templateStr.substring(0, 100)}...`
                  : templateStr;
                templatePreview = templateStr;
              } catch (e) {
                templateDisplay = 'Invalid template';
                templatePreview = 'Invalid JSON template';
              }
            }

            cardsHTML += `
              <div class="webhook-card" data-id="${webhook.id}">
                <div class="webhook-card-header">
                  <div class="webhook-info">
                    <h4 class="webhook-url-title">${webhook.url}</h4>
                    <p class="webhook-description">${webhook.description || 'No description provided'}</p>
                    <div class="webhook-details">
                      <span class="webhook-method webhook-method-${(webhook.http_method || 'POST').toLowerCase()}"><i class="fas fa-code"></i> ${webhook.http_method || 'POST'}</span>
                      ${webhook.headers ? `<span class="webhook-headers"><i class="fas fa-list"></i> ${Object.keys(webhook.headers).length} header(s)</span>` : ''}
                    </div>
                  </div>
                  <div class="webhook-status-badge">
                    <span class="webhook-status ${webhook.is_active ? 'active' : 'inactive'}">
                      <i class="fas ${webhook.is_active ? 'fa-check-circle' : 'fa-pause-circle'}"></i>
                      ${webhook.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div class="webhook-template-section">
                  <h5><i class="fas fa-code"></i> Body Template</h5>
                  <div class="template-preview-card">
                    <pre class="template-content">${templateDisplay}</pre>
                    ${templatePreview.length > 100 ? `<button class="btn btn-sm btn-link view-full-template" data-template='${templatePreview.replace(/'/g, "&#39;")}'>View Full Template</button>` : ''}
                  </div>
                </div>

                <div class="webhook-actions">
                  <button class="btn btn-sm btn-primary edit-webhook-btn" data-id="${webhook.id}">
                    <i class="fas fa-edit"></i> Edit
                  </button>
                  <button class="btn btn-sm ${webhook.is_active ? 'btn-warning' : 'btn-success'} toggle-webhook-btn"
                          data-id="${webhook.id}"
                          data-active="${webhook.is_active}">
                    <i class="fas ${webhook.is_active ? 'fa-pause' : 'fa-play'}"></i>
                    ${webhook.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                  <button class="btn btn-sm btn-danger delete-webhook-btn" data-id="${webhook.id}">
                    <i class="fas fa-trash-alt"></i> Delete
                  </button>
                </div>
              </div>
            `;
          });

          cardsHTML += '</div>';
          webhookList.innerHTML = cardsHTML;

          // Add event listeners for all webhook buttons
          document.querySelectorAll('.edit-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => editWebhook(btn.getAttribute('data-id')));
          });

          document.querySelectorAll('.toggle-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleWebhook(btn));
          });

          document.querySelectorAll('.delete-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteWebhook(btn.getAttribute('data-id')));
          });

          document.querySelectorAll('.view-full-template').forEach(btn => {
            btn.addEventListener('click', () => viewFullTemplate(btn.getAttribute('data-template')));
          });

        } catch (error) {
          console.error('Error loading webhooks:', error);
          webhookList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading Webhooks</h3>
              <p>Failed to load webhooks. Please try again.</p>
              <button class="btn btn-primary" onclick="loadWebhooks()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // WhatsApp Management
      // Load WhatsApp contacts - define as a function that can be called globally
      window.loadWhatsAppContacts = async function() {
        console.log('Loading WhatsApp contacts...');
        const whatsappList = document.getElementById('whatsappList');

        if (!whatsappList) {
          console.error('WhatsApp list element not found');
          return;
        }

        whatsappList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading WhatsApp contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/whatsapp');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            whatsappList.innerHTML = `
              <div class="empty-state">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <h3>No WhatsApp Contacts Configured</h3>
                <p>Add a WhatsApp contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display WhatsApp contacts
          let tableHTML = `
            <table class="whatsapp-table">
              <thead>
                <tr>
                  <th>Phone Number</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="whatsapp-phone">${contact.phone_number}</td>
                <td class="whatsapp-name">${contact.full_name}</td>
                <td>
                  <span class="whatsapp-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="whatsapp-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-whatsapp-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-whatsapp-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          whatsappList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-whatsapp-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleWhatsApp(btn));
          });

          document.querySelectorAll('.delete-whatsapp-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteWhatsApp(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading WhatsApp contacts:', error);
          whatsappList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading WhatsApp Contacts</h3>
              <p>Failed to load WhatsApp contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadWhatsAppContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // SMS Management
      // Load SMS contacts - define as a function that can be called globally
      window.loadSMSContacts = async function() {
        console.log('Loading SMS contacts...');
        const smsList = document.getElementById('smsList');

        if (!smsList) {
          console.error('SMS list element not found');
          return;
        }

        smsList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading SMS contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/sms');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            smsList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-sms" style="animation: none;"></i>
                <h3>No SMS Contacts Configured</h3>
                <p>Add an SMS contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display SMS contacts
          let tableHTML = `
            <table class="sms-table">
              <thead>
                <tr>
                  <th>Phone Number</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="sms-phone">${contact.phone_number}</td>
                <td class="sms-name">${contact.full_name}</td>
                <td>
                  <span class="sms-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="sms-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-sms-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-sms-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          smsList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-sms-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleSMS(btn));
          });

          document.querySelectorAll('.delete-sms-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteSMS(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading SMS contacts:', error);
          smsList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading SMS Contacts</h3>
              <p>Failed to load SMS contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadSMSContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // Email Management
      // Load Email contacts - define as a function that can be called globally
      window.loadEmailContacts = async function() {
        console.log('Loading Email contacts...');
        const emailList = document.getElementById('emailList');

        if (!emailList) {
          console.error('Email list element not found');
          return;
        }

        emailList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading Email contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/email');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            emailList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <h3>No Email Contacts Configured</h3>
                <p>Add an Email contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display Email contacts
          let tableHTML = `
            <table class="email-table">
              <thead>
                <tr>
                  <th>Email Address</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="email-address">${contact.email_address}</td>
                <td class="email-name">${contact.full_name}</td>
                <td>
                  <span class="email-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="email-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-email-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-email-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          emailList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-email-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleEmail(btn));
          });

          document.querySelectorAll('.delete-email-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteEmail(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading Email contacts:', error);
          emailList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading Email Contacts</h3>
              <p>Failed to load Email contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadEmailContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // Toggle webhook active status
      async function toggleWebhook(button) {
        const webhookId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} webhook...`, 'info');

          const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}"></i> ${!isActive ? 'Deactivate' : 'Activate'}`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-webhook-btn`;

          // Update status badge in the card
          const card = button.closest('.webhook-card');
          const statusBadge = card.querySelector('.webhook-status');
          statusBadge.className = `webhook-status ${!isActive ? 'active' : 'inactive'}`;
          statusBadge.innerHTML = `<i class="fas ${!isActive ? 'fa-check-circle' : 'fa-pause-circle'}"></i> ${!isActive ? 'Active' : 'Inactive'}`;

          showToast(`Webhook ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling webhook:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} webhook. Please try again.`, 'error');
        }
      }

      // Confirm webhook deletion
      function confirmDeleteWebhook(webhookId) {
        showConfirmDialog('Are you sure you want to delete this webhook?', webhookId, deleteWebhook);
      }

      // Edit webhook
      async function editWebhook(webhookId) {
        try {
          // Fetch webhook details
          const response = await fetch(`/face_recognition/webhooks/${webhookId}`);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const webhook = await response.json();

          // Populate the form with existing data
          document.getElementById('webhookUrl').value = webhook.url;
          document.getElementById('webhookDescription').value = webhook.description || '';
          document.getElementById('webhookMethod').value = webhook.http_method || 'POST';

          if (webhook.headers) {
            document.getElementById('webhookHeaders').value = JSON.stringify(webhook.headers, null, 2);
          } else {
            document.getElementById('webhookHeaders').value = '';
          }

          if (webhook.body_template) {
            document.getElementById('webhookTemplate').value = JSON.stringify(webhook.body_template, null, 2);
          } else {
            document.getElementById('webhookTemplate').value = '';
          }

          // Update the form to edit mode
          const form = document.getElementById('webhookForm');
          form.setAttribute('data-edit-id', webhookId);

          // Change submit button text
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Webhook';

          // Add cancel button
          if (!form.querySelector('.cancel-edit-btn')) {
            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'btn btn-secondary cancel-edit-btn';
            cancelBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';
            cancelBtn.onclick = cancelEdit;
            submitBtn.parentNode.insertBefore(cancelBtn, submitBtn);
          }

          // Update form title
          const formTitle = document.getElementById('formTitle');
          if (formTitle) {
            formTitle.innerHTML = '<i class="fas fa-edit"></i> Edit Webhook';
          }

          // Show the form
          showWebhookForm();

          // Initialize template editor
          initializeTemplateEditor();

          // Update method color
          updateMethodColor();

          showToast('Webhook loaded for editing', 'info');
        } catch (error) {
          console.error('Error loading webhook for edit:', error);
          showToast('Failed to load webhook details. Please try again.', 'error');
        }
      }

      // Cancel edit mode
      function cancelEdit() {
        const form = document.getElementById('webhookForm');
        form.removeAttribute('data-edit-id');
        form.reset();

        // Reset submit button
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Webhook';

        // Remove cancel button
        const cancelBtn = form.querySelector('.cancel-edit-btn');
        if (cancelBtn) {
          cancelBtn.remove();
        }

        // Reset form title
        const formTitle = document.getElementById('formTitle');
        if (formTitle) {
          formTitle.innerHTML = '<i class="fas fa-plus"></i> Add New Webhook';
        }

        // Hide the form
        hideWebhookForm();

        showToast('Edit cancelled', 'info');
      }

      // View full template
      function viewFullTemplate(template) {
        try {
          const parsedTemplate = JSON.parse(template);
          const formattedTemplate = JSON.stringify(parsedTemplate, null, 2);

          // Create modal to show full template
          const modal = document.createElement('div');
          modal.className = 'template-modal';
          modal.innerHTML = `
            <div class="template-modal-content">
              <div class="template-modal-header">
                <h3><i class="fas fa-code"></i> Full Webhook Template</h3>
                <button class="template-modal-close">&times;</button>
              </div>
              <div class="template-modal-body">
                <pre class="template-modal-code">${formattedTemplate}</pre>
              </div>
              <div class="template-modal-footer">
                <button class="btn btn-secondary template-modal-close">Close</button>
              </div>
            </div>
          `;

          document.body.appendChild(modal);

          // Add event listeners for closing
          modal.querySelectorAll('.template-modal-close').forEach(btn => {
            btn.onclick = () => {
              document.body.removeChild(modal);
            };
          });

          // Close on background click
          modal.onclick = (e) => {
            if (e.target === modal) {
              document.body.removeChild(modal);
            }
          };

        } catch (error) {
          showToast('Error displaying template', 'error');
        }
      }

      // Delete webhook
      async function deleteWebhook(webhookId) {
        try {
          showToast('Deleting webhook...', 'info');

          const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the card from the list
          const card = document.querySelector(`.webhook-card[data-id="${webhookId}"]`);
          if (card) {
            card.remove();
          }

          // Check if there are any cards left
          const cards = document.querySelectorAll('.webhook-card');
          if (cards.length === 0) {
            document.getElementById('webhookList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-bell-slash" style="animation: none;"></i>
                <h3>No Webhooks Configured</h3>
                <p>Add a webhook URL to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('Webhook deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting webhook:', error);
          showToast('Failed to delete webhook. Please try again.', 'error');
        }
      }

      // Template Editor Functions
      function initializeTemplateEditor() {
        // Template editor is now simplified - no preview functionality needed
        console.log('Template editor initialized');
      }

      // Webhook Form Toggle Functions
      function showWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        const toggleBtn = document.getElementById('toggleWebhookFormBtn');

        formSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-minus" style="animation: none;"></i> Hide Form';
        toggleBtn.classList.remove('btn-success');
        toggleBtn.classList.add('btn-outline-secondary');

        // Scroll to form
        formSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      function hideWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        const toggleBtn = document.getElementById('toggleWebhookFormBtn');
        const form = document.getElementById('webhookForm');

        formSection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-plus" style="animation: none;"></i> Add New Webhook';
        toggleBtn.classList.remove('btn-outline-secondary');
        toggleBtn.classList.add('btn-success');

        // Reset form if not in edit mode
        if (!form.getAttribute('data-edit-id')) {
          form.reset();
          document.getElementById('webhookMethod').value = 'POST';
          document.getElementById('webhookHeaders').value = '';
          document.getElementById('webhookTemplate').value = '';
        }
      }

      function toggleWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        if (formSection.style.display === 'none' || formSection.style.display === '') {
          showWebhookForm();
        } else {
          hideWebhookForm();
        }
      }

      // Add event listeners for form toggle buttons
      document.getElementById('toggleWebhookFormBtn').addEventListener('click', toggleWebhookForm);
      document.getElementById('closeFormBtn').addEventListener('click', hideWebhookForm);
      document.getElementById('cancelFormBtn').addEventListener('click', hideWebhookForm);

      // Add event listener for HTTP method dropdown to change colors like Postman
      const methodDropdown = document.getElementById('webhookMethod');

      function updateMethodColor() {
        const selectedMethod = methodDropdown.value;
        const colors = {
          'GET': '#61affe',
          'POST': '#49cc90',
          'PUT': '#fca130',
          'PATCH': '#50e3c2',
          'DELETE': '#f93e3e'
        };

        methodDropdown.style.color = colors[selectedMethod] || '#495057';
        methodDropdown.style.fontWeight = '600';
      }

      methodDropdown.addEventListener('change', updateMethodColor);

      // Set initial color
      updateMethodColor();

      // Add webhook form submission
      webhookForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const url = document.getElementById('webhookUrl').value;
        const description = document.getElementById('webhookDescription').value;
        const httpMethod = document.getElementById('webhookMethod').value;
        const headersText = document.getElementById('webhookHeaders').value;
        const templateText = document.getElementById('webhookTemplate').value;
        const editId = webhookForm.getAttribute('data-edit-id');

        // Validate that template is provided
        if (!templateText.trim()) {
          showToast('Webhook body template is required. Please provide a JSON template.', 'error');
          return;
        }

        // Parse and validate the headers if provided
        let headers = null;
        if (headersText.trim()) {
          try {
            headers = JSON.parse(headersText);
          } catch (error) {
            showToast('Invalid JSON headers format. Please check your headers syntax.', 'error');
            return;
          }
        }

        // Parse and validate the template
        let bodyTemplate = null;
        try {
          bodyTemplate = JSON.parse(templateText);
        } catch (error) {
          showToast('Invalid JSON template format. Please check your template syntax.', 'error');
          return;
        }

        try {
          const isEdit = !!editId;
          showToast(isEdit ? 'Updating webhook...' : 'Adding webhook...', 'info');

          const response = await fetch(
            isEdit ? `/face_recognition/webhooks/${editId}` : '/face_recognition/webhooks',
            {
              method: isEdit ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                url,
                description,
                http_method: httpMethod,
                headers: headers,
                body_template: bodyTemplate
              }),
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Reset form and exit edit mode
          if (isEdit) {
            cancelEdit();
          } else {
            webhookForm.reset();
            document.getElementById('webhookMethod').value = 'POST';
            document.getElementById('webhookHeaders').value = '';
            document.getElementById('webhookTemplate').value = '';
            hideWebhookForm();
          }

          // Reload webhooks list
          loadWebhooks();

          showToast(isEdit ? 'Webhook updated successfully!' : 'Webhook added successfully!', 'success');
        } catch (error) {
          console.error('Error saving webhook:', error);
          showToast(`Failed to ${editId ? 'update' : 'add'} webhook: ${error.message}`, 'error');
        }
      });

      // Toggle WhatsApp active status
      async function toggleWhatsApp(button) {
        const whatsappId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} WhatsApp contact...`, 'info');

          const response = await fetch(`/face_recognition/whatsapp/${whatsappId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-whatsapp-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.whatsapp-status');
          statusCell.className = `whatsapp-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`WhatsApp contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling WhatsApp contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} WhatsApp contact. Please try again.`, 'error');
        }
      }

      // Confirm WhatsApp deletion
      function confirmDeleteWhatsApp(whatsappId) {
        showConfirmDialog('Are you sure you want to delete this WhatsApp contact?', whatsappId, deleteWhatsApp);
      }

      // Delete WhatsApp contact
      async function deleteWhatsApp(whatsappId) {
        try {
          showToast('Deleting WhatsApp contact...', 'info');

          const response = await fetch(`/face_recognition/whatsapp/${whatsappId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${whatsappId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.whatsapp-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('whatsappList').innerHTML = `
              <div class="empty-state">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <h3>No WhatsApp Contacts Configured</h3>
                <p>Add a WhatsApp contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('WhatsApp contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting WhatsApp contact:', error);
          showToast('Failed to delete WhatsApp contact. Please try again.', 'error');
        }
      }

      // Add WhatsApp form submission
      const whatsappForm = document.getElementById('whatsappForm');
      whatsappForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone_number = document.getElementById('whatsappPhone').value;
        const full_name = document.getElementById('whatsappName').value;

        try {
          showToast('Adding WhatsApp contact...', 'info');

          const response = await fetch('/face_recognition/whatsapp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone_number, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          whatsappForm.reset();

          // Reload WhatsApp contacts list
          loadWhatsAppContacts();

          showToast('WhatsApp contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding WhatsApp contact:', error);
          showToast(`Failed to add WhatsApp contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Toggle SMS active status
      async function toggleSMS(button) {
        const smsId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} SMS contact...`, 'info');

          const response = await fetch(`/face_recognition/sms/${smsId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-sms-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.sms-status');
          statusCell.className = `sms-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`SMS contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling SMS contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} SMS contact. Please try again.`, 'error');
        }
      }

      // Confirm SMS deletion
      function confirmDeleteSMS(smsId) {
        showConfirmDialog('Are you sure you want to delete this SMS contact?', smsId, deleteSMS);
      }

      // Delete SMS contact
      async function deleteSMS(smsId) {
        try {
          showToast('Deleting SMS contact...', 'info');

          const response = await fetch(`/face_recognition/sms/${smsId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${smsId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.sms-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('smsList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-sms" style="animation: none;"></i>
                <h3>No SMS Contacts Configured</h3>
                <p>Add an SMS contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('SMS contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting SMS contact:', error);
          showToast('Failed to delete SMS contact. Please try again.', 'error');
        }
      }

      // Add SMS form submission
      const smsForm = document.getElementById('smsForm');
      smsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone_number = document.getElementById('smsPhone').value;
        const full_name = document.getElementById('smsName').value;

        try {
          showToast('Adding SMS contact...', 'info');

          const response = await fetch('/face_recognition/sms', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone_number, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          smsForm.reset();

          // Reload SMS contacts list
          loadSMSContacts();

          showToast('SMS contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding SMS contact:', error);
          showToast(`Failed to add SMS contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Refresh WhatsApp contacts button
      const refreshWhatsappBtn = document.getElementById('refreshWhatsappBtn');
      refreshWhatsappBtn.addEventListener('click', () => {
        loadWhatsAppContacts();
        showToast('Refreshing WhatsApp contacts...', 'info');
      });

      // Toggle Email active status
      async function toggleEmail(button) {
        const emailId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} Email contact...`, 'info');

          const response = await fetch(`/face_recognition/email/${emailId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-email-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.email-status');
          statusCell.className = `email-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`Email contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling Email contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} Email contact. Please try again.`, 'error');
        }
      }

      // Confirm Email deletion
      function confirmDeleteEmail(emailId) {
        showConfirmDialog('Are you sure you want to delete this Email contact?', emailId, deleteEmail);
      }

      // Delete Email contact
      async function deleteEmail(emailId) {
        try {
          showToast('Deleting Email contact...', 'info');

          const response = await fetch(`/face_recognition/email/${emailId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${emailId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.email-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('emailList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <h3>No Email Contacts Configured</h3>
                <p>Add an Email contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('Email contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting Email contact:', error);
          showToast('Failed to delete Email contact. Please try again.', 'error');
        }
      }

      // Add Email form submission
      const emailForm = document.getElementById('emailForm');
      emailForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email_address = document.getElementById('emailAddress').value;
        const full_name = document.getElementById('emailName').value;

        try {
          showToast('Adding Email contact...', 'info');

          const response = await fetch('/face_recognition/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email_address, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          emailForm.reset();

          // Reload Email contacts list
          loadEmailContacts();

          showToast('Email contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding Email contact:', error);
          showToast(`Failed to add Email contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Refresh WhatsApp contacts button
      document.getElementById('refreshWhatsappBtn').addEventListener('click', () => {
        loadWhatsAppContacts();
        showToast('Refreshing WhatsApp contacts...', 'info');
      });

      // Refresh SMS contacts button
      document.getElementById('refreshSmsBtn').addEventListener('click', () => {
        loadSMSContacts();
        showToast('Refreshing SMS contacts...', 'info');
      });

      // Refresh Email contacts button
      document.getElementById('refreshEmailBtn').addEventListener('click', () => {
        loadEmailContacts();
        showToast('Refreshing Email contacts...', 'info');
      });

      // Make loadSettings available globally
      window.loadSettings = loadSettings;

      // Make loadSettings available to call when the tab is activated
      // loadWebhooks, loadWhatsAppContacts, loadSMSContacts, and loadEmailContacts are already available globally
      return { loadSettings };
    }

    // Clustering Functions
    let isClusteredView = false;

    function setupClusteringFunctions() {
      const clusterBtn = document.getElementById('clusterBtn');
      const viewModeBtn = document.getElementById('viewModeBtn');
      const viewModeText = document.getElementById('viewModeText');
      const minClusterSizeInput = document.getElementById('minClusterSize');

      // Only add event listeners if elements exist
      if (clusterBtn) {
        // Cluster button click handler
        clusterBtn.addEventListener('click', async () => {
        try {
          showToast('Auto-clustering faces...', 'info');

          const minClusterSize = parseInt(minClusterSizeInput.value) || 2;

          const response = await fetch('/face_recognition/cluster-unknowns', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              min_cluster_size: minClusterSize
            })
          });

          const result = await response.json();

          if (result.success) {
            // Show success message
            showToast(result.message, 'success');

            // If images were removed, show additional information
            if (result.removed_images && result.removed_images > 0) {
              console.log(`${result.removed_images} images without faces were automatically removed`);
            }

            // Switch to clustered view
            isClusteredView = true;
            viewModeText.textContent = 'Switch to Individual View';
            loadClusteredUnknowns();
          } else {
            // Show error message
            if (result.removed_images && result.removed_images > 0) {
              showToast(`Clustering failed but ${result.removed_images} images without faces were removed. ${result.message}`, 'warning');
            } else {
              showToast(result.message || 'Clustering failed', 'error');
            }
          }
        } catch (error) {
          console.error('Error clustering faces:', error);
          showToast('Failed to cluster faces. Please try again.', 'error');
        }
        });
      }

      // View mode button click handler
      if (viewModeBtn && viewModeText) {
        viewModeBtn.addEventListener('click', () => {
          isClusteredView = !isClusteredView;

          if (isClusteredView) {
            viewModeText.textContent = 'Switch to Individual View';
            loadClusteredUnknowns();
          } else {
            viewModeText.textContent = 'Switch to Clustered View';
            loadUnknowns();
          }
        });
      }
    }

    // Function to load clustered unknown faces
    async function loadClusteredUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading clustered faces...</p>
        </div>
      `;

      try {
        // First, get the cluster name mapping
        const clusterRes = await fetch('/face_recognition/get-available-clusters');
        const clusterData = await clusterRes.json();

        // Create a mapping from cluster IDs to display names
        const clusterNameMap = {};
        for (const cluster of clusterData.clusters) {
          clusterNameMap[cluster.id] = cluster.display_name || `Cluster ${cluster.id.split('_').pop()}`;
        }

        // Now get the clustered unknowns
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        // Clear container
        container.innerHTML = '';

        const clusters = data.clusters;
        const unclustered = data.unclustered;

        // Check if there are any clusters or unclustered faces
        if (Object.keys(clusters).length === 0 && unclustered.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Display clusters
        if (Object.keys(clusters).length > 0) {
          const clusterSection = document.createElement('div');
          clusterSection.className = 'cluster-section';

          const clusterHeader = document.createElement('h3');
          clusterHeader.className = 'section-title';
          clusterHeader.innerHTML = `<i class="fas fa-object-group" style="animation: none;"></i> Clustered Faces`;
          clusterSection.appendChild(clusterHeader);

          // Sort clusters by size (number of faces)
          const sortedClusters = Object.entries(clusters).sort((a, b) => b[1].length - a[1].length);

          for (const [clusterId, faces] of sortedClusters) {
            const clusterCard = document.createElement('div');
            clusterCard.className = 'cluster-card';
            clusterCard.setAttribute('data-cluster-id', clusterId);

            // Create image section
            const imageSection = faces.map(face =>
              face.image_paths.map(path => {
                // Skip invalid paths
                if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
                  console.error('Invalid image path:', path);
                  return '';
                }

                // Sanitize path - replace backslashes with forward slashes
                const sanitizedPath = path.replace(/\\/g, '/');
                // Encode the path to prevent issues with special characters
                const encodedPath = sanitizedPath.replace(/'/g, "\\'");

                return `<div class="image-container">
                  <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                  <div class="image-actions">
                    <button class="move-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleMoveImageClick(this, event)">
                      <i class="fas fa-exchange-alt" style="animation: none;"></i>
                    </button>
                    <button class="delete-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleDeleteImageClick(this, event)">
                      <i class="fas fa-trash-alt" style="animation: none;"></i>
                    </button>
                  </div>
                </div>`;
              }).join('')
            ).join('');

            clusterCard.innerHTML = `
              <div class="cluster-header">
                <div class="cluster-title">
                  <i class="fas fa-users" style="animation: none;"></i>
                  ${clusterNameMap[clusterId] || `Cluster Group`}
                  <span class="cluster-count">${faces.length} faces</span>
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteCluster('${clusterId}')">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete Cluster
                </button>
              </div>
              <div class="cluster-faces">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-cluster" class="assign-form" id="cluster-form-${clusterId}">
                  <input type="hidden" name="cluster_id" value="${clusterId}">
                  <div class="form-group">
                    <label><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" name="username" class="form-control" placeholder="Enter username">
                  </div>
                  <div class="form-group">
                    <label><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" name="email" class="form-control" placeholder="Enter email address">
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign All to User
                    </button>
                    <small class="form-text text-muted" style="margin-top: 8px;">
                      <i class="fas fa-info-circle"></i> This will assign all ${faces.length} faces in this cluster to the same user.
                    </small>
                  </div>
                </form>
              </div>
            `;

            clusterSection.appendChild(clusterCard);

            // Add form submission handler
            setTimeout(() => {
              const form = document.getElementById(`cluster-form-${clusterId}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignCluster(this, clusterId);
                });
              }
            }, 0);
          }

          container.appendChild(clusterSection);
        }

        // Display unclustered faces
        if (unclustered.length > 0) {
          const unclusteredSection = document.createElement('div');
          unclusteredSection.className = 'unclustered-section';

          const unclusteredHeader = document.createElement('h3');
          unclusteredHeader.className = 'section-title';
          unclusteredHeader.innerHTML = `<i class="fas fa-user-question" style="animation: none;"></i> Unclustered Faces <span class="cluster-count">${unclustered.length} faces</span>`;
          unclusteredSection.appendChild(unclusteredHeader);

          // Create cards for each unclustered face
          unclustered.forEach(person => {
            const card = document.createElement('div');
            card.className = 'card';
            card.setAttribute('data-id', person.id);

            const imageSection = person.image_paths.map(path => {
              // Skip invalid paths
              if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
                console.error('Invalid image path:', path);
                return '';
              }

              // Sanitize path - replace backslashes with forward slashes
              const sanitizedPath = path.replace(/\\/g, '/');
              // Encode the path to prevent issues with special characters
              const encodedPath = sanitizedPath.replace(/'/g, "\\'");

              return `<div class="image-container">
                <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                <div class="image-actions">
                  <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                    <i class="fas fa-exchange-alt" style="animation: none;"></i>
                  </button>
                  <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </div>
              </div>`;
            }).join('');

            card.innerHTML = `
              <div class="card-header">
                <div class="card-title">
                  <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
                </button>
              </div>
              <div class="card-images">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                  <input type="hidden" name="unknown_id" value="${person.id}">

                  <!-- Mode selection buttons -->
                  <div class="mode-selection" style="margin-bottom: 15px;">
                    <div class="btn-group" role="group" style="width: 100%;">
                      <button type="button" class="btn btn-outline-primary mode-btn" data-mode="existing" onclick="switchMode(${person.id}, 'existing')" style="width: 50%;">
                        <i class="fas fa-user-check" style="animation: none;"></i> Existing User
                      </button>
                      <button type="button" class="btn btn-outline-primary mode-btn" data-mode="new" onclick="switchMode(${person.id}, 'new')" style="width: 50%;">
                        <i class="fas fa-user-plus" style="animation: none;"></i> New User
                      </button>
                    </div>
                  </div>

                  <!-- Loading indicator for potential matches -->
                  <div id="loading-matches-${person.id}" style="display: none; text-align: center; margin-bottom: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Checking for potential matches...
                  </div>

                  <!-- Potential matches section (initially hidden) -->
                  <div id="potential-matches-${person.id}" style="display: none; margin-bottom: 15px;">
                    <div class="alert alert-info">
                      <i class="fas fa-info-circle"></i> Potential matches found. Select a user from the dropdown or switch to "New User" mode.
                    </div>
                  </div>

                  <!-- Existing user selection (initially hidden) -->
                  <div id="existing-user-${person.id}" class="mode-container" style="display: none;">
                    <div class="form-group">
                      <label for="user-select-${person.id}"><i class="fas fa-users" style="animation: none;"></i> Select User</label>
                      <select id="user-select-${person.id}" class="form-control" onchange="userSelected(${person.id})">
                        <option value="">-- Select a user --</option>
                      </select>
                    </div>
                    <div class="selected-user-info" id="selected-user-info-${person.id}" style="display: none; margin-top: 10px;">
                      <div class="alert alert-success">
                        <strong>Selected User:</strong> <span id="selected-username-${person.id}"></span><br>
                        <strong>Email:</strong> <span id="selected-email-${person.id}"></span>
                        <input type="hidden" id="selected-username-input-${person.id}" name="username">
                        <input type="hidden" id="selected-email-input-${person.id}" name="email">
                      </div>
                    </div>
                  </div>

                  <!-- New user input (initially shown) -->
                  <div id="new-user-${person.id}" class="mode-container">
                    <div class="form-group">
                      <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                      <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username">
                    </div>
                    <div class="form-group">
                      <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                      <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address">
                    </div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                    </button>
                  </div>
                </form>
              </div>
            `;

            unclusteredSection.appendChild(card);

            // Add form submission handler and initialize UI
            setTimeout(() => {
              const form = document.getElementById(`form-${person.id}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignUnknown(this, person.id);
                });

                // Initialize the UI - check for potential matches
                // Set the "New User" mode as default initially
                switchMode(person.id, 'new');

                // Check if this unknown person might match existing users
                checkUnknownMatches(person.id).then(result => {
                  if (result.is_likely_registered && result.matches.length > 0) {
                    // If there are potential matches, switch to existing user mode
                    switchMode(person.id, 'existing');
                  }
                });
              }
            }, 100);
          });

          container.appendChild(unclusteredSection);
        }
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load clustered faces. Please try again.</p>
            <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Function to confirm deletion of a cluster
    function confirmDeleteCluster(clusterId) {
      showConfirmDialog('Are you sure you want to delete all faces in this cluster?', clusterId, deleteCluster);
    }

    // Function to delete a cluster
    async function deleteCluster(clusterId) {
      try {
        showToast('Deleting cluster...', 'info');

        // Get all unknown IDs in this cluster
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        if (!data.clusters[clusterId]) {
          showToast('Cluster not found', 'error');
          return;
        }

        const unknownIds = data.clusters[clusterId].map(face => face.id);

        // Delete each unknown in the cluster
        let successCount = 0;

        for (const id of unknownIds) {
          try {
            const deleteResponse = await fetch(`/face_recognition/delete-unknown/${id}`, {
              method: 'DELETE'
            });

            if (deleteResponse.ok) {
              successCount++;
            }
          } catch (error) {
            console.error(`Error deleting unknown ID ${id}:`, error);
          }
        }

        if (successCount > 0) {
          showToast(`Successfully deleted ${successCount} of ${unknownIds.length} faces`, 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast('Failed to delete cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete cluster. Please try again.', 'error');
      }
    }

    // Function to assign a cluster to a user
    async function assignCluster(form, clusterId) {
      try {
        // Validate the form inputs
        const usernameInput = form.querySelector('input[name="username"]');
        const emailInput = form.querySelector('input[name="email"]');

        if (!usernameInput.value) {
          showToast('Please enter a username', 'error');
          usernameInput.focus();
          return;
        }

        if (!emailInput.value) {
          showToast('Please enter an email address', 'error');
          emailInput.focus();
          return;
        }

        // Create a new FormData object
        const formData = new FormData();

        // Add the cluster_id
        formData.append('cluster_id', clusterId);
        formData.append('username', usernameInput.value);
        formData.append('email', emailInput.value);

        console.log(`Assigning cluster ${clusterId} to user: ${usernameInput.value}, email: ${emailInput.value}`);

        showToast('Assigning cluster to user...', 'info');

        const response = await fetch('/face_recognition/assign-cluster', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Cluster assigned successfully!', 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign cluster. Please try again.', 'error');
      }
    }

    // Initialize the page when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, initializing admin panel...');

      // Create settings manager first to avoid reference error
      const settingsManager = setupSettingsManagement();

      // Setup other components
      loadUnknowns();
      setupTabs();
      setupCameraManagement();
      setupUserRegistration();
      setupUserManagement();
      setupClusteringFunctions();

      console.log('Admin panel initialization complete');
    });

    // Load settings data - now loadSettings is available globally
    if (typeof window.loadSettings === 'function') {
      window.loadSettings();
    }

    // Function to directly check and restore the active settings section
    function checkAndRestoreSettingsSection() {
      // Only run this if we're on the settings tab
      const settingsTab = document.getElementById('settings-tab');
      if (settingsTab && settingsTab.classList.contains('active')) {
        console.log('On settings tab, checking for saved section');
        const savedSection = localStorage.getItem('activeSettingsSection');
        if (savedSection) {
          console.log('Found saved section:', savedSection);
          // Get all settings tiles and sections
          const settingsTiles = document.querySelectorAll('.settings-tile');
          const settingsSections = document.querySelectorAll('.settings-section');

          // Remove active class from all tiles and sections
          settingsTiles.forEach(t => t.classList.remove('active'));
          settingsSections.forEach(s => s.classList.remove('active'));

          // Add active class to the tile with matching data-settings-section
          const targetTile = document.querySelector(`.settings-tile[data-settings-section="${savedSection}"]`);
          if (targetTile) {
            targetTile.classList.add('active');
          }

          // Show corresponding section
          const section = document.getElementById(`${savedSection}-settings`);
          if (section) {
            section.classList.add('active');
            console.log('Activated section:', savedSection);

            // If alert management section is activated, load appropriate data
            if (savedSection === 'alert-management') {
              // Check which subsection is active
              const savedSubsection = localStorage.getItem('activeSettingsSubsection');
              if (savedSubsection === 'webhook') {
                if (typeof window.loadWebhooks === 'function') {
                  window.loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    safeInitializeTemplateEditor();
                  }, 500);
                }
              } else if (savedSubsection === 'whatsapp') {
                if (typeof window.loadWhatsAppContacts === 'function') {
                  window.loadWhatsAppContacts();
                }
              } else if (savedSubsection === 'sms') {
                if (typeof window.loadSMSContacts === 'function') {
                  window.loadSMSContacts();
                }
              } else if (savedSubsection === 'email') {
                if (typeof window.loadEmailContacts === 'function') {
                  window.loadEmailContacts();
                }
              } else {
                // Default to webhooks if no subsection is saved
                if (typeof window.loadWebhooks === 'function') {
                  window.loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    safeInitializeTemplateEditor();
                  }, 500);
                }
              }
            }
          }
        }
      }
    }

    // Run the check after a short delay to ensure everything is loaded
    setTimeout(checkAndRestoreSettingsSection, 100);

    // Function to confirm deletion of a single image
    function confirmDeleteImage(imagePath, unknownId, e) {
      console.log("confirmDeleteImage called with:", imagePath, unknownId);

      // Stop event propagation to prevent enlargeImage from being called
      if (e) {
        e.stopPropagation();
        e.preventDefault();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }

      // Fix backslashes in the path (replace \ with /)
      const fixedPath = imagePath.replace(/\\/g, '/');
      console.log("Fixed path:", fixedPath);

      showConfirmDialog('Are you sure you want to delete this image?', { path: fixedPath, id: unknownId }, deleteImage);
    }

    // Function to delete a single image
    async function deleteImage(data) {
      try {
        console.log("deleteImage called with data:", data);
        showToast('Deleting image...', 'info');

        const url = `/face_recognition/delete-unknown-image?image_path=${encodeURIComponent(data.path)}`;
        console.log("Sending DELETE request to:", url);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log("Response status:", response.status);
        const result = await response.json();
        console.log("Response data:", result);

        if (response.ok) {
          showToast(result.message, 'success');

          // Find and remove the image container from the UI
          const imageContainers = document.querySelectorAll('.image-container');
          for (const container of imageContainers) {
            if (container.querySelector('img').src.includes(data.path)) {
              container.remove();
              break;
            }
          }

          // If the record was deleted (no images left), remove the entire card
          if (result.record_deleted) {
            // In individual view
            const card = document.querySelector(`[data-id="${data.id}"]`);
            if (card) {
              card.remove();

              // Check if there are any cards left
              if (container.children.length === 0) {
                container.innerHTML = `
                  <div class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <h3>No Unknown Persons</h3>
                    <p>There are currently no unknown persons to manage.</p>
                    <button class="btn btn-primary" onclick="refreshView()">
                      <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                  </div>
                `;
              }
            }

            // In clustered view
            if (isClusteredView) {
              // Refresh the clustered view to reflect the changes
              loadClusteredUnknowns();
            }
          }
        } else {
          showToast(result.detail || 'Failed to delete image', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete image. Please try again.', 'error');
      }
    }

    // Function to handle delete image button click
    function handleDeleteImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleDeleteImageClick called with path:", path, "id:", id);

      // Call confirmDeleteImage with the path and id
      confirmDeleteImage(path, id);
    }

    // Function to refresh the current view based on the view mode
    function refreshView() {
      if (isClusteredView) {
        loadClusteredUnknowns();
      } else {
        loadUnknowns();
      }
    }

    // Make functions available globally
    window.loadUnknowns = loadUnknowns;
    window.loadClusteredUnknowns = loadClusteredUnknowns;
    window.confirmDeleteUnknown = confirmDeleteUnknown;
    window.deleteUnknown = deleteUnknown;
    window.confirmDeleteCluster = confirmDeleteCluster;
    window.deleteCluster = deleteCluster;
    window.assignCluster = assignCluster;
    window.enlargeImage = enlargeImage;
    window.confirmDeleteImage = confirmDeleteImage;
    window.deleteImage = deleteImage;
    window.handleDeleteImageClick = handleDeleteImageClick;
    window.refreshView = refreshView;
    window.loadCameras = loadCameras;
    window.confirmDeleteCamera = confirmDeleteCamera;
    window.deleteCamera = deleteCamera;
    window.loadUsers = loadUsers;
    window.confirmDeleteUser = confirmDeleteUser;
    window.deleteUser = deleteUser;
    window.viewUserDetails = viewUserDetails;

    // Functions for moving images between clusters

    // Function to handle move image button click
    function handleMoveImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleMoveImageClick called with path:", path, "id:", id);

      // Show the move image modal
      showMoveImageModal(id);
    }

    // Function to show the move image modal
    async function showMoveImageModal(unknownId) {
      // Get the modal and clusters list
      const modal = document.getElementById('moveImageModal');
      const clustersList = document.getElementById('clustersList');

      // Show loading state
      clustersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      `;

      // Show the modal
      modal.style.display = 'block';

      try {
        // Fetch available clusters
        const response = await fetch('/face_recognition/get-available-clusters');
        const data = await response.json();

        if (!response.ok) {
          throw new Error('Failed to fetch clusters');
        }

        // Get the current cluster for this unknown
        const unknownResponse = await fetch('/face_recognition/get-clustered-unknowns');
        const unknownData = await unknownResponse.json();

        let currentClusterId = null;

        // Find the current cluster for this unknown
        for (const clusterId in unknownData.clusters) {
          const unknowns = unknownData.clusters[clusterId];
          for (const unknown of unknowns) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = clusterId;
              break;
            }
          }
          if (currentClusterId) break;
        }

        // If not found in clusters, check unclustered
        if (!currentClusterId) {
          for (const unknown of unknownData.unclustered) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = 'unclustered';
              break;
            }
          }
        }

        // Build the clusters list
        let clustersHtml = '';

        // Add each cluster as a button
        for (const cluster of data.clusters) {
          // Skip the current cluster
          if (cluster.id === currentClusterId) continue;

          clustersHtml += `
            <button class="cluster-option" onclick="moveImageToCluster(${unknownId}, '${cluster.id}')">
              <i class="fas ${cluster.is_unclustered ? 'fa-users-slash' : 'fa-users'}"></i>
              ${cluster.display_name || cluster.name}
            </button>
          `;
        }

        if (clustersHtml === '') {
          clustersList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-info-circle"></i>
              <p>No other clusters available to move to.</p>
            </div>
          `;
        } else {
          clustersList.innerHTML = clustersHtml;
        }
      } catch (error) {
        console.error('Error fetching clusters:', error);
        clustersList.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Error loading clusters. Please try again.</p>
          </div>
        `;
      }
    }

    // Function to move an image to a different cluster
    async function moveImageToCluster(unknownId, targetClusterId) {
      try {
        showToast('Moving image...', 'info');

        // Close the modal
        document.getElementById('moveImageModal').style.display = 'none';

        // Send the request to move the image
        const response = await fetch(`/face_recognition/move-to-cluster?unknown_id=${unknownId}&target_cluster_id=${encodeURIComponent(targetClusterId)}`, {
          method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message, 'success');

          // Refresh the view to reflect the changes
          refreshView();
        } else {
          showToast(result.detail || 'Failed to move image', 'error');
        }
      } catch (error) {
        console.error('Error moving image:', error);
        showToast('Failed to move image. Please try again.', 'error');
      }
    }

    // Add the move image functions to the global scope
    window.handleMoveImageClick = handleMoveImageClick;
    window.showMoveImageModal = showMoveImageModal;
    window.moveImageToCluster = moveImageToCluster;
  </script>

  <!-- Move Image Modal -->
  <div id="moveImageModal" class="modal">
    <div class="modal-content" style="max-width: 500px;">
      <span class="close" onclick="document.getElementById('moveImageModal').style.display='none'">&times;</span>
      <h2>Move Image to Another Cluster</h2>
      <p>Select the target cluster to move this image to:</p>
      <div id="clustersList" class="clusters-list">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="document.getElementById('moveImageModal').style.display='none'">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Load Management JavaScript -->
  <script src="/static/admin_panel.js"></script>

</body>
</html>

