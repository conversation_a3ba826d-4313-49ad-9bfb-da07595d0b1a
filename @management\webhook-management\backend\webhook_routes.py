# Webhook Management API Routes
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
import json
import datetime
from datetime import timezone
from . import models
from .database import get_db

router = APIRouter()

@router.post("/webhooks", response_model=models.WebhookResponse)
async def create_webhook(
    webhook: models.WebhookCreate,
    db: Session = Depends(get_db)
):
    # Check if webhook URL already exists
    existing_webhook = db.query(models.Webhook).filter(models.Webhook.url == webhook.url).first()
    if existing_webhook:
        raise HTTPException(status_code=400, detail="Webhook URL already exists")

    # Create new webhook
    new_webhook = models.Webhook(
        url=webhook.url,
        description=webhook.description,
        http_method=webhook.http_method or 'POST',
        headers=json.dumps(webhook.headers) if webhook.headers else None,
        body_template=webhook.body_template
    )

    db.add(new_webhook)
    db.commit()
    db.refresh(new_webhook)

    return new_webhook

@router.get("/webhooks", response_model=List[models.WebhookResponse])
async def get_webhooks(db: Session = Depends(get_db)):
    webhooks = db.query(models.Webhook).all()
    return [
        {
            "id": webhook.id,
            "url": webhook.url,
            "description": webhook.description,
            "http_method": webhook.http_method,
            "headers": webhook.headers,
            "body_template": webhook.body_template,
            "is_active": webhook.is_active,
            "created_at": webhook.created_at,
            "updated_at": webhook.updated_at
        }
        for webhook in webhooks
    ]

@router.get("/webhooks/{webhook_id}", response_model=models.WebhookResponse)
async def get_webhook(webhook_id: int, db: Session = Depends(get_db)):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return webhook

@router.put("/webhooks/{webhook_id}", response_model=models.WebhookResponse)
async def update_webhook(
    webhook_id: int,
    webhook_update: models.WebhookUpdate,
    db: Session = Depends(get_db)
):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")

    # Update fields if provided
    if webhook_update.url is not None:
        # Check if the new URL already exists for another webhook
        existing_webhook = db.query(models.Webhook).filter(
            models.Webhook.url == webhook_update.url,
            models.Webhook.id != webhook_id
        ).first()
        if existing_webhook:
            raise HTTPException(status_code=400, detail="Webhook URL already exists")
        webhook.url = webhook_update.url

    if webhook_update.description is not None:
        webhook.description = webhook_update.description

    if webhook_update.http_method is not None:
        webhook.http_method = webhook_update.http_method

    if webhook_update.headers is not None:
        webhook.headers = json.dumps(webhook_update.headers)

    if webhook_update.body_template is not None:
        webhook.body_template = webhook_update.body_template

    if webhook_update.is_active is not None:
        webhook.is_active = webhook_update.is_active

    db.commit()
    db.refresh(webhook)

    return webhook

@router.put("/webhooks/{webhook_id}/toggle")
async def toggle_webhook_status(webhook_id: int, db: Session = Depends(get_db)):
    try:
        webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
        if not webhook:
            raise HTTPException(status_code=404, detail="Webhook not found")

        webhook.is_active = not webhook.is_active
        webhook.updated_at = datetime.datetime.now(timezone.utc)
        db.commit()

        status = "activated" if webhook.is_active else "deactivated"
        return {"status": "success", "message": f"Webhook {status} successfully"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error toggling webhook status: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/webhooks/{webhook_id}")
async def delete_webhook(webhook_id: int, db: Session = Depends(get_db)):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")

    db.delete(webhook)
    db.commit()

    return {"status": "success", "message": "Webhook deleted successfully"}

# Test webhook endpoint (commented out - can be enabled if needed)
# @router.post("/webhooks/{webhook_id}/test")
# async def test_webhook(webhook_id: int, db: Session = Depends(get_db)):
#     webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
#     if not webhook:
#         raise HTTPException(status_code=404, detail="Webhook not found")

#     if not webhook.is_active:
#         raise HTTPException(status_code=400, detail="Webhook is not active")

#     # Create a test payload
#     test_payload = {
#         "event": "test",
#         "timestamp": datetime.now(timezone.utc).isoformat(),
#         "data": {
#             "message": "This is a test notification from the face recognition system"
#         }
#     }

#     try:
#         # Send the test payload to the webhook URL
#         async with httpx.AsyncClient() as client:
#             response = await client.post(
#                 webhook.url,
#                 json=test_payload,
#                 timeout=10.0
#             )

#             return {
#                 "status": "success",
#                 "message": "Test webhook sent successfully",
#                 "response_status": response.status_code,
#                 "response_body": response.text
#             }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to send test webhook: {str(e)}")
