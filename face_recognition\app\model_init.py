import os
import urllib.request
import bz2
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Path to the shape predictor model
SHAPE_PREDICTOR_PATH = "app/models/shape_predictor_68_face_landmarks.dat"
SHAPE_PREDICTOR_URL = "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2"

def ensure_shape_predictor_model():
    """
    Ensure the dlib shape predictor model is available.
    Downloads and extracts it if it doesn't exist.
    
    Returns:
        str: Path to the shape predictor model file
    """
    # Check if the model already exists
    if os.path.exists(SHAPE_PREDICTOR_PATH):
        logger.info(f"Shape predictor model found at {SHAPE_PREDICTOR_PATH}")
        return SHAPE_PREDICTOR_PATH
    
    # Create the models directory if it doesn't exist
    os.makedirs(os.path.dirname(SHAPE_PREDICTOR_PATH), exist_ok=True)
    
    logger.info(f"Downloading shape predictor model to {SHAPE_PREDICTOR_PATH}...")
    
    try:
        # Download the compressed model file
        compressed_path = f"{SHAPE_PREDICTOR_PATH}.bz2"
        urllib.request.urlretrieve(SHAPE_PREDICTOR_URL, compressed_path)
        logger.info("Download completed. Extracting...")
        
        # Extract the bz2 file
        with open(SHAPE_PREDICTOR_PATH, 'wb') as new_file, bz2.BZ2File(compressed_path, 'rb') as compressed_file:
            new_file.write(compressed_file.read())
        
        # Remove the compressed file
        os.remove(compressed_path)
        
        logger.info("Shape predictor model downloaded and extracted successfully.")
        return SHAPE_PREDICTOR_PATH
        
    except Exception as e:
        logger.error(f"Failed to download shape predictor model: {str(e)}")
        raise RuntimeError(f"Could not initialize shape predictor model: {str(e)}")
