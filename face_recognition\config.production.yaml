# Face Recognition System Configuration - PRODUCTION/SERVER DEPLOYMENT
# This file contains non-sensitive configuration settings for production deployment

app:
  name: "Face Recognition System"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 8000
  debug: false
  reload: false

database:
  user: "root"
  host: "mysql"  # Docker service name or actual server IP
  name: "face_recognition_db"
  port: 3306
  # Password is set via environment variable DB_PASSWORD

face_recognition:
  threshold: 0.7
  model_repo: "arnabdhar/YOLOv8-Face-Detection"
  model_filename: "model.pt"

qdrant:
  url: "http://qdrant:6333"  # Docker service name or actual server IP
  collection_name: "face_embeddings"
  vector_size: 512

paths:
  dataset: "/app/Dataset"
  static: "/app/static"
  templates: "/app/templates"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

cors:
  origins: ["https://yourdomain.com", "https://www.yourdomain.com"]  # Replace with your actual domain
  credentials: true
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  headers: ["*"]

# Performance settings for production
performance:
  workers: 4
  max_connections: 1000
  keepalive_timeout: 5

# Security settings for production
security:
  allowed_hosts: ["yourdomain.com", "www.yourdomain.com"]  # Replace with your actual domain
  ssl_redirect: true
  secure_cookies: true
