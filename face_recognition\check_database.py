#!/usr/bin/env python3
"""
Script to check and fix database schema issues
"""

import sqlite3
import os
import sys

def check_and_fix_database():
    """Check database schema and fix any issues"""
    
    # Find the database file
    possible_paths = [
        'face_recognition.db',
        './face_recognition.db',
        'app/face_recognition.db',
        '../face_recognition.db'
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("Database file not found. It will be created when the application runs.")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"Found database at: {db_path}")
        
        # Check cameras table structure
        cursor.execute("PRAGMA table_info(cameras)")
        columns = cursor.fetchall()
        
        print("\nCameras table structure:")
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4]}")
        
        # Check if is_active column exists
        column_names = [col[1] for col in columns]
        
        if 'is_active' not in column_names:
            print("\n❌ Missing 'is_active' column in cameras table")
            print("Adding 'is_active' column...")
            
            cursor.execute("ALTER TABLE cameras ADD COLUMN is_active BOOLEAN DEFAULT 1 NOT NULL")
            cursor.execute("UPDATE cameras SET is_active = 1 WHERE is_active IS NULL")
            
            print("✅ Added 'is_active' column to cameras table")
        else:
            print("\n✅ 'is_active' column exists in cameras table")
        
        # Check existing cameras
        cursor.execute("SELECT name, rtsp_url, is_active FROM cameras")
        cameras = cursor.fetchall()
        
        print(f"\nExisting cameras ({len(cameras)}):")
        for camera in cameras:
            status = "Active" if camera[2] else "Inactive"
            print(f"  - {camera[0]}: {camera[1]} ({status})")
        
        # Check webhooks table
        cursor.execute("PRAGMA table_info(webhooks)")
        webhook_columns = cursor.fetchall()
        
        print(f"\nWebhooks table structure:")
        for col in webhook_columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4]}")
        
        # Check existing webhooks
        cursor.execute("SELECT id, url, is_active FROM webhooks")
        webhooks = cursor.fetchall()
        
        print(f"\nExisting webhooks ({len(webhooks)}):")
        for webhook in webhooks:
            status = "Active" if webhook[2] else "Inactive"
            print(f"  - ID {webhook[0]}: {webhook[1]} ({status})")
        
        conn.commit()
        print("\n✅ Database check completed successfully!")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔍 Checking database schema...")
    success = check_and_fix_database()
    
    if success:
        print("\n🎉 Database check completed!")
        sys.exit(0)
    else:
        print("\n💥 Database check failed!")
        sys.exit(1)
