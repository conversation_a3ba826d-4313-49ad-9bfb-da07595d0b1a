<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0a0a0a;
            color: #e4e4e7;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        /* Header */
        .header {
            background: rgba(9, 9, 11, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(39, 39, 42, 0.5);
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #fafafa;
            font-size: 1.4rem;
            font-weight: 600;
            letter-spacing: -0.02em;
        }
        
        .shield-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .back-btn {
            background: rgba(63, 63, 70, 0.3);
            border: 1px solid rgba(63, 63, 70, 0.5);
            color: #d4d4d8;
            padding: 10px 18px;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .back-btn:hover {
            background: rgba(63, 63, 70, 0.5);
            border-color: rgba(113, 113, 122, 0.5);
            transform: translateY(-1px);
        }
        
        /* Navigation */
        .nav-container {
            padding: 2rem;
            display: flex;
            justify-content: center;
        }
        
        .nav-tabs {
            background: rgba(24, 24, 27, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(39, 39, 42, 0.5);
            border-radius: 12px;
            padding: 6px;
            display: flex;
            gap: 4px;
        }
        
        .nav-tab {
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            background: transparent;
            color: #a1a1aa;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            font-size: 0.9rem;
        }
        
        .nav-tab.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        }
        
        .nav-tab:hover:not(.active) {
            background: rgba(63, 63, 70, 0.3);
            color: #e4e4e7;
        }
        
        /* Main Content */
        .main-content {
            padding: 0 2rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .unknown-persons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 1rem;
        }
        
        /* Person Card */
        .person-card {
            background: rgba(24, 24, 27, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(39, 39, 42, 0.5);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .person-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }
        
        .person-card:hover {
            transform: translateY(-4px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .person-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #fafafa;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: -0.01em;
        }
        
        .person-icon {
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
        }
        
        .delete-btn {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
            padding: 8px 14px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85rem;
        }
        
        .delete-btn:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.5);
            transform: translateY(-1px);
        }
        
        /* Profile Image */
        .profile-section {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            object-fit: cover;
            border: 2px solid rgba(63, 63, 70, 0.5);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .profile-image:hover {
            transform: scale(1.02);
            border-color: rgba(59, 130, 246, 0.5);
        }
        
        /* Attendance Records */
        .attendance-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            font-weight: 600;
            color: #fafafa;
            margin-bottom: 1rem;
            letter-spacing: -0.01em;
        }
        
        .clock-icon {
            width: 20px;
            height: 20px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .attendance-table {
            background: rgba(9, 9, 11, 0.6);
            border: 1px solid rgba(39, 39, 42, 0.5);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table-header {
            background: rgba(63, 63, 70, 0.3);
            color: #d4d4d8;
            padding: 1rem;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .table-row {
            padding: 1rem;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1rem;
            border-bottom: 1px solid rgba(39, 39, 42, 0.3);
            transition: background-color 0.2s ease;
            font-size: 0.9rem;
        }
        
        .table-row:hover {
            background: rgba(63, 63, 70, 0.2);
        }
        
        .table-row:last-child {
            border-bottom: none;
        }
        
        /* Registration Form */
        .registration-section {
            margin-top: 1.5rem;
        }
        
        .registration-options {
            display: flex;
            gap: 8px;
            margin-bottom: 1.5rem;
            background: rgba(39, 39, 42, 0.3);
            padding: 4px;
            border-radius: 10px;
        }
        
        .option-btn {
            flex: 1;
            padding: 10px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #a1a1aa;
            font-size: 0.9rem;
        }
        
        .option-btn:hover {
            background: rgba(63, 63, 70, 0.3);
            color: #e4e4e7;
        }
        
        .option-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        .user-icon {
            width: 12px;
            height: 12px;
            background: currentColor;
            border-radius: 50%;
        }
        
        /* Form Fields */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 500;
            color: #d4d4d8;
            font-size: 0.9rem;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(63, 63, 70, 0.5);
            border-radius: 10px;
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(24, 24, 27, 0.8);
            color: #e4e4e7;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-input::placeholder {
            color: #71717a;
        }
        
        /* Action Buttons */
        .form-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .assign-btn {
            background: #10b981;
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        
        .assign-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .info-note {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            color: #93c5fd;
            font-size: 0.8rem;
            flex: 1;
            font-weight: 400;
        }
        
        .info-icon {
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(39, 39, 42, 0.3);
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(113, 113, 122, 0.5);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(113, 113, 122, 0.7);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-container {
                padding: 1rem;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
                justify-content: center;
                gap: 6px;
            }
            
            .nav-tab {
                font-size: 0.85rem;
                padding: 10px 16px;
            }
            
            .unknown-persons-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .person-card {
                padding: 1.5rem;
            }
            
            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .info-note {
                text-align: center;
            }
        }
        
        /* Loading Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .person-card {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* Modern Focus Indicators */
        button:focus-visible,
        input:focus-visible {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-title">
            <div class="shield-icon">🛡</div>
            Face Recognition Admin Panel
        </div>
        <a href="#" class="back-btn">
            ← Back to Dashboard
        </a>
    </header>

    <!-- Navigation Tabs -->
    <div class="nav-container">
        <div class="nav-tabs">
            <button class="nav-tab active">
                <div class="user-icon"></div>
                Unknown Persons
            </button>
            <button class="nav-tab">
                <div class="user-icon"></div>
                User Registration
            </button>
            <button class="nav-tab">
                <div class="user-icon"></div>
                User Management
            </button>
            <button class="nav-tab">
                <div class="user-icon"></div>
                Management
            </button>
            <button class="nav-tab">
                <div class="user-icon"></div>
                Settings
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="unknown-persons-grid">
            <!-- Unknown Person #4 -->
            <div class="person-card">
                <div class="card-header">
                    <h3 class="person-title">
                        <div class="person-icon"></div>
                        Unknown Person #4
                    </h3>
                    <button class="delete-btn">
                        🗑 Delete
                    </button>
                </div>

                <div class="profile-section">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB4PSIyMCIgeT0iMjAiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAxMmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6bTAgMmMtMi4yIDAtNCAuOS00IDJIMHY2aDEydi02aC00YzAtMS4xLTEuOC0yLTQtMnoiLz4KPC9zdmc+Cjwvc3ZnPgo=" alt="Unknown Person #4" class="profile-image">
                </div>

                <div class="attendance-section">
                    <h4 class="section-title">
                        <div class="clock-icon">🕐</div>
                        Attendance Records
                    </h4>
                    <div class="attendance-table">
                        <div class="table-header">
                            <div>Date & Time</div>
                            <div>Camera</div>
                        </div>
                        <div class="table-row">
                            <div>2025-06-04 15:56:31</div>
                            <div>Camera 1</div>
                        </div>
                    </div>
                </div>

                <div class="registration-section">
                    <div class="registration-options">
                        <button class="option-btn">
                            <div class="user-icon"></div>
                            Existing User
                        </button>
                        <button class="option-btn active">
                            <div class="user-icon"></div>
                            New User
                        </button>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <div class="user-icon"></div>
                            Username
                        </label>
                        <input type="text" class="form-input" placeholder="Enter username">
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            📧 Email
                        </label>
                        <input type="email" class="form-input" placeholder="Enter email address">
                    </div>

                    <div class="form-actions">
                        <button class="assign-btn">
                            👤 Assign to User
                        </button>
                        <div class="info-note">
                            <div class="info-icon">i</div>
                            You can assign multiple images to the same user to improve recognition.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unknown Person #5 -->
            <div class="person-card">
                <div class="card-header">
                    <h3 class="person-title">
                        <div class="person-icon"></div>
                        Unknown Person #5
                    </h3>
                    <button class="delete-btn">
                        🗑 Delete
                    </button>
                </div>

                <div class="profile-section">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiM3NjRiYTIiLz4KPHN2ZyB4PSIyMCIgeT0iMjAiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAxMmMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6bTAgMmMtMi4yIDAtNCAuOS00IDJIMHY2aDEydi02aC00YzAtMS4xLTEuOC0yLTQtMnoiLz4KPC9zdmc+Cjwvc3ZnPgo=" alt="Unknown Person #5" class="profile-image">
                </div>

                <div class="attendance-section">
                    <h4 class="section-title">
                        <div class="clock-icon">🕐</div>
                        Attendance Records
                    </h4>
                    <div class="attendance-table">
                        <div class="table-header">
                            <div>Date & Time</div>
                            <div>Camera</div>
                        </div>
                        <div class="table-row">
                            <div>2025-06-04 15:58:24</div>
                            <div>Camera 1</div>
                        </div>
                    </div>
                </div>

                <div class="registration-section">
                    <div class="registration-options">
                        <button class="option-btn">
                            <div class="user-icon"></div>
                            Existing User
                        </button>
                        <button class="option-btn active">
                            <div class="user-icon"></div>
                            New User
                        </button>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <div class="user-icon"></div>
                            Username
                        </label>
                        <input type="text" class="form-input" placeholder="Enter username">
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            📧 Email
                        </label>
                        <input type="email" class="form-input" placeholder="Enter email address">
                    </div>

                    <div class="form-actions">
                        <button class="assign-btn">
                            👤 Assign to User
                        </button>
                        <div class="info-note">
                            <div class="info-icon">i</div>
                            You can assign multiple images to the same user to improve recognition.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Registration option switching
        document.querySelectorAll('.person-card').forEach(card => {
            const optionBtns = card.querySelectorAll('.option-btn');
            optionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    optionBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });

        // Button interactions with smooth animations
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if(confirm('Are you sure you want to delete this unknown person?')) {
                    const card = this.closest('.person-card');
                    card.style.transform = 'scale(0.8)';
                    card.style.opacity = '0';
                    setTimeout(() => {
                        card.remove();
                    }, 300);
                }
            });
        });

        document.querySelectorAll('.assign-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.person-card');
                const username = card.querySelector('input[placeholder="Enter username"]').value;
                const email = card.querySelector('input[placeholder="Enter email address"]').value;
                
                if(username && email) {
                    // Success feedback
                    this.style.background = '#10b981';
                    this.textContent = '✓ Assigned Successfully';
                    setTimeout(() => {
                        this.innerHTML = '👤 Assign to User';
                        this.style.background = '#10b981';
                    }, 2000);
                } else {
                    // Error feedback
                    this.style.background = '#ef4444';
                    this.textContent = '⚠ Fill all fields';
                    setTimeout(() => {
                        this.innerHTML = '👤 Assign to User';
                        this.style.background = '#10b981';
                    }, 2000);
                }
            });
        });

        // Smooth hover animations
        document.querySelectorAll('.person-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Intersection Observer for staggered animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.animationDelay = `${index * 0.1}s`;
                        entry.target.classList.add('animate-in');
                    }, index * 100);
                }
            });
        });

        document.querySelectorAll('.person-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>