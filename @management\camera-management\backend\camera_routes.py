# Camera Management API Routes
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
from . import models
from .database import get_db

router = APIRouter()

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: models.CameraData, db: Session = Depends(get_db)):
    # Check if camera name already exists
    existing_camera = db.query(models.Camera).filter(models.Camera.name == camera_data.cameraName).first()
    if existing_camera:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}

    # Check if RTSP URL already exists
    existing_url = db.query(models.Camera).filter(models.Camera.rtsp_url == camera_data.rtspUrl).first()
    if existing_url:
        return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_url.name}"}

    # Create new camera record
    new_camera = models.Camera(
        name=camera_data.cameraName,
        rtsp_url=camera_data.rtspUrl,
    )

    db.add(new_camera)
    db.commit()

    return {"status": "success", "message": "Camera added successfully"}

# Route to update a camera
@router.put("/update-camera/{camera_name}")
async def update_camera(
    camera_name: str,
    camera_update: dict,
    db: Session = Depends(get_db)
):
    """Update a camera's name and/or RTSP URL"""
    try:
        camera = db.query(models.Camera).filter(models.Camera.name == camera_name).first()
        if not camera:
            raise HTTPException(status_code=404, detail="Camera not found")

        # Check if new name is provided and different from current name
        if "name" in camera_update and camera_update["name"] != camera_name:
            new_name = camera_update["name"].strip()

            # Validate new name
            if not new_name:
                raise HTTPException(status_code=400, detail="Camera name cannot be empty")

            # Check if new name already exists
            existing_camera = db.query(models.Camera).filter(models.Camera.name == new_name).first()
            if existing_camera:
                raise HTTPException(status_code=400, detail=f"Camera with name '{new_name}' already exists")

            camera.name = new_name

        # Update RTSP URL
        if "rtsp_url" in camera_update:
            camera.rtsp_url = camera_update["rtsp_url"]

        db.commit()
        db.refresh(camera)

        final_name = camera.name
        return {"status": "success", "message": f"Camera '{final_name}' updated successfully"}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error and return a generic error message
        print(f"Error updating camera: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Route to toggle camera status
@router.put("/toggle-camera/{camera_name}")
async def toggle_camera_status(
    camera_name: str,
    status_update: dict,
    db: Session = Depends(get_db)
):
    """Toggle a camera's active status"""
    try:
        camera = db.query(models.Camera).filter(models.Camera.name == camera_name).first()
        if not camera:
            raise HTTPException(status_code=404, detail="Camera not found")

        # Update active status - handle case where is_active column might not exist
        if "active" in status_update:
            try:
                camera.is_active = status_update["active"]
            except AttributeError:
                # If is_active column doesn't exist, just return success without updating
                return {"status": "success", "message": f"Camera '{camera_name}' status updated (is_active column not available)"}

        db.commit()
        db.refresh(camera)

        # Safely get the status
        try:
            is_active = camera.is_active
            status_text = "activated" if is_active else "deactivated"
        except AttributeError:
            status_text = "updated"

        return {"status": "success", "message": f"Camera '{camera_name}' {status_text} successfully"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error toggling camera status: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Route to get all cameras
@router.get("/get-cameras")
async def get_cameras(db: Session = Depends(get_db)):
    print("getting cameras..")
    cameras = db.query(models.Camera).all()
    result = {}
    for camera in cameras:
        result[camera.name] = {
            "rtsp_url": camera.rtsp_url,
            "is_active": getattr(camera, 'is_active', True)  # Default to True if field doesn't exist yet
        }
    print("cameras:", result)
    return result

# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str, db: Session = Depends(get_db)):
    camera = db.query(models.Camera).filter(models.Camera.name == camera_name).first()

    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")

    db.delete(camera)
    db.commit()

    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}

# Route to add a test camera (for testing purposes)
@router.post("/add-test-camera")
async def add_test_camera(db: Session = Depends(get_db)):
    """Add a test camera for testing edit functionality"""
    try:
        # Check if test camera already exists
        existing_camera = db.query(models.Camera).filter(models.Camera.name == "Test_Camera").first()
        if existing_camera:
            return {"status": "success", "message": "Test camera already exists"}

        # Create test camera
        test_camera = models.Camera(
            name="Test_Camera",
            rtsp_url="rtsp://test.example.com:554/stream",
            is_active=True
        )

        db.add(test_camera)
        db.commit()
        db.refresh(test_camera)

        return {"status": "success", "message": "Test camera added successfully"}

    except Exception as e:
        print(f"Error adding test camera: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error adding test camera: {str(e)}")
