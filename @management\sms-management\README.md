# SMS Management Service

This folder contains all code related to SMS contact management functionality.

## Files Structure:
- **html/** - HTML templates for SMS management UI
- **css/** - CSS styles for SMS management components
- **js/** - JavaScript functions for SMS operations
- **backend/** - Backend API endpoints for SMS CRUD operations

## Functionality:
- Add new SMS contacts with phone numbers
- Edit existing SMS contact information
- Delete SMS contacts
- Toggle contact active/inactive status
- List all configured SMS contacts
- Phone number validation and formatting
- SMS-specific styling with orange theme

## API Endpoints:
- POST /sms-contacts - Create new SMS contact
- GET /sms-contacts - Get all SMS contacts
- GET /sms-contacts/{id} - Get specific SMS contact
- PUT /sms-contacts/{id} - Update SMS contact
- DELETE /sms-contacts/{id} - Delete SMS contact
- PUT /sms-contacts/{id}/toggle - Toggle contact status

## Database Models:
- SMSContact model with phone_number, name, is_active fields
- Phone number validation and international format support

## Features:
- International phone number format support
- SMS orange color scheme integration
- Contact validation and duplicate prevention
- Professional contact cards with action buttons
